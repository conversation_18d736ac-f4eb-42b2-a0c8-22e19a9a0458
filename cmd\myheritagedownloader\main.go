package main

import (
	"fmt"
	"os"

	"github.com/alexflint/go-arg"
	nested "github.com/antonfisher/nested-logrus-formatter"
	"github.com/dmagur/myheritage/internal"
	"github.com/dmagur/myheritage/internal/api"
	"github.com/dmagur/myheritage/internal/args"
	"github.com/dmagur/myheritage/internal/database"
	"github.com/dmagur/myheritage/internal/fetch"
	"github.com/sirupsen/logrus"
)

type ResponseBaseDir string

const (
	responsesBaseDir = ResponseBaseDir("responses")
)

// getEnv returns the value of an environment variable or a default value if not set
func getEnv(key, fallback string) string {
	if value, exists := os.LookupEnv(key); exists {
		return value
	}
	return fallback
}

func main() {
	var args args.Args

	arg.MustParse(&args)

	logger := logrus.New()
	logger.SetFormatter(&nested.Formatter{})

	logger.SetLevel(logrus.DebugLevel)

	if args.TraceMode {
		logger.SetLevel(logrus.TraceLevel)
		logger.SetReportCaller(true)
	}

	if err := internal.GetRuntimeTimezone(logger); err != nil {
		logger.Panic(err)
	}

	// Connect to the database using the factory
	db := database.NewDB()

	// Get the connection string for wire dependency injection
	dbType := getEnv("DB_TYPE", "postgres")
	var connStr database.ConnString

	if dbType == "mariadb" || dbType == "mysql" {
		// For MariaDB, we need to create a connection string that works with wire
		host := getEnv("DB_HOST", "localhost")
		user := getEnv("DB_USER", "myheritage")
		password := getEnv("DB_PASSWORD", "mysecretpassword")
		dbname := getEnv("DB_NAME", "dna_match_db")
		connStr = database.ConnString(fmt.Sprintf("%s:%s@tcp(%s:3306)/%s", user, password, host, dbname))
	} else {
		// For PostgreSQL, use the standard connection string
		host := getEnv("DB_HOST", "my_postgres_db")
		user := getEnv("DB_USER", "postgres")
		password := getEnv("DB_PASSWORD", "mysecretpassword")
		dbname := getEnv("DB_NAME", "dna_match_db")
		connStr = database.ConnString(fmt.Sprintf("host=%s user=%s password=%s dbname=%s sslmode=disable", host, user, password, dbname))
	}

	// Create a token provider to get the token from the database
	tokenProvider := api.NewTokenProvider(db, logger)
	bearerToken := tokenProvider.GetBearerToken()

	// If no token is found in the database, use the token from command line or default
	if string(bearerToken) == "" {
		if args.Token != "" {
			logger.Info("Using token from command line arguments.")
			bearerToken = fetch.BearerToken(args.Token)
		} else {
			logger.Warn("No token found in the database or command line. Using default token.")
			bearerToken = fetch.BearerToken("4.cbdd04f731661d487d728136bd8331a0.122085562.**********.1440.7345153..o5801bf1.63e35b208a452fbeb643d0729508e9b42ca26934b60d03cd71a038466a7decf9")
		}
	}

	// Create the application using wire dependency injection
	var application App
	var err error

	if args.LocalMode {
		application, err = CreateLocalApp(logger, responsesBaseDir, connStr, bearerToken)
		if err != nil {
			logger.Panic(err)
		}
	} else {
		application, err = CreateRemoteApp(logger, connStr, bearerToken)
		if err != nil {
			logger.Panic(err)
		}
	}

	if err := application.Run(); err != nil {
		logger.Panic(err)
	}
}
