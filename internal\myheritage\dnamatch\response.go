package dnamatch

type Response struct {
	Data ResponseData `json:"data"`
}

type ResponseData struct {
	DNAKit DNAKit `json:"dna_kit"`
}

type DNAKit struct {
	DNAMatches DNAMatches `json:"dna_matches"`
}

type DNAMatches struct {
	Data []DNAMatch `json:"data"`
}

type DNAMatch struct {
	Id                             string      `json:"id"`
	Link                           string      `json:"link"`
	IsNew                          bool        `json:"is_new"`
	TotalSharedSegmentsLengthInCm  float32     `json:"total_shared_segments_length_in_cm"`
	LargestSharedSegmentLengthInCm float32     `json:"largest_shared_segment_length_in_cm"`
	PercentageOfSharedSegments     float32     `json:"percentage_of_shared_segments"`
	TotalSharedSegments            int         `json:"total_shared_segments"`
	ExactDnaRelationship           string      `json:"exact_dna_relationship"`
	GenealogicalRelationship       string      `json:"genealogical_relationship"`
	IsRecentlyRecalculated         bool        `json:"is_recently_recalculated"`
	ConfidenceLevel                string      `json:"confidence_level"`
	OtherDNAKit                    OtherDNAKit `json:"other_dna_kit"`
}

type OtherDNAKit struct {
	Submitter            Submitter            `json:"submitter"`
	Member               Member               `json:"member"`
	AssociatedIndividual AssociatedIndividual `json:"associated_individual"`
}

type Submitter struct {
	Id                      string `json:"id"`
	Name                    string `json:"name"`
	NameTransliterated      string `json:"name_transliterated"`
	FirstName               string `json:"first_name"`
	FirstNameTransliterated string `json:"first_name_transliterated"`
	Link                    string `json:"link"`
	IsPublic                bool   `json:"is_public"`
}

type Member struct {
	Submitter
	Gender        string        `json:"gender"`
	AgeGroup      string        `json:"age_group"`
	PersonalPhoto PersonalPhoto `json:"personal_photo"`
	CountryCode   string        `json:"country_code"`
}

type Thumbnail struct {
	Url string `json:"url"`
}

type PersonalPhoto struct {
	Thumbnails []Thumbnail `json:"thumbnails"`
}

type AssociatedIndividual struct {
	Member
	BirthPlace         string        `json:"birth_place"`
	Tree               Tree          `json:"tree"`
	Relationship       *Relationship `json:"relationship,omitempty"`
	LinkInPedigreeTree string        `json:"link_in_pedigree_tree"`
	LinkInTree         string        `json:"link_in_tree"`
	AgeGroupInYears    string        `json:"age_group_in_years"`
}

type Relationship struct {
	RelationshipDescription string `json:"relationship_description"`
}

type Tree struct {
	Id              string `json:"id"`
	Name            string `json:"name"`
	Link            string `json:"link"`
	IndividualCount int    `json:"individual_count"`
	Site            Site   `json:"site"`
}

type Site struct {
	IsRequestMembershipAllowed bool    `json:"is_request_membership_allowed"`
	Creator                    Creator `json:"creator"`
}

type Creator struct {
	Id                 string `json:"id"`
	Name               string `json:"name"`
	NameTransliterated string `json:"name_transliterated"`
	Country            string `json:"country"`
	CountryCode        string `json:"country_code"`
	Link               string `json:"link"`
	IsPublic           bool   `json:"is_public"`
}
