package dnamatch

import (
	"fmt"
	"time"

	"github.com/dmagur/myheritage/internal/database"
	_ "github.com/lib/pq"
)

type Repository struct {
	db *database.GormDB
}

func NewRepository(db *database.GormDB) *Repository {
	return &Repository{
		db: db,
	}
}

// Save information about processed DNA kits and return its ID
func (r Repository) saveProcessedDnaKit(dnaKitID string) (int, error) {
	// Create a new processed DNA kit record
	processedKit := database.ProcessedDnaKit{
		DnaKitID:    dnaKitID,
		ProcessedAt: time.Now().Format("2006-01-02 15:04:05"),
	}

	// Try to find an existing record
	var existingKit database.ProcessedDnaKit
	result := r.db.DB.Where("dnaKitID = ?", dnaKitID).First(&existingKit)

	// If the record exists, update it
	if result.Error == nil {
		existingKit.ProcessedAt = processedKit.ProcessedAt
		r.db.DB.Save(&existingKit)
		fmt.Printf("Updated processed DNA kit with ID %s.\n", dnaKitID)
		return existingKit.ID, nil
	}

	// If the record doesn't exist, create a new one
	result = r.db.DB.Create(&processedKit)
	if result.Error != nil {
		return 0, fmt.Errorf("error saving processed DNA kit info: %v", result.Error)
	}

	fmt.Printf("Processed DNA kit with ID %s.\n", dnaKitID)
	return processedKit.ID, nil
}

func (r Repository) saveDnaMatches(dnaMatches []DNAMatch, sourceIndividualID string) (int, error) {
	for _, match := range dnaMatches {
		dnaMatchExists, err := r.checkDNAMatchExists(match.Id)
		if dnaMatchExists {
			return -1, nil
		}

		// Initialize IDs with default values
		var individualID string
		var treeID string

		// Save the Submitter if exists
		submitterID, err := r.upsertSubmitter(match.OtherDNAKit.Submitter)
		if err != nil {
			return 0, err
		}

		//Check if AssociatedIndividual has valid ID to determine if it exists
		if match.OtherDNAKit.AssociatedIndividual.Id != "" {
			// Check if Tree is associated with the Individual
			if match.OtherDNAKit.AssociatedIndividual.Tree.Id != "" {
				// Save the Tree
				treeID, err = r.upsertTree(match.OtherDNAKit.AssociatedIndividual.Tree)
				if err != nil {
					return 0, err
				}
			}

			// Save the Associated Individual
			individualID, err = r.upsertIndividual(match.OtherDNAKit.AssociatedIndividual)
			fmt.Printf("Upserted Individual ID: %s\n\n", individualID)

			if err != nil {
				return 0, err
			}
		}

		// Save the DNA Match
		if err := r.upsertDnaMatch(match, sourceIndividualID, submitterID, match.OtherDNAKit.AssociatedIndividual.Id, treeID); err != nil {
			fmt.Printf("OtherDnaKit: %+v\n", match.OtherDNAKit)
			return 0, err
		}
	}

	return 1, nil
}

// Helper function to insert or update a DNA match
func (r Repository) upsertDnaMatch(match DNAMatch, sourceIndividualID string, submitterID string, individualID string, treeID string) error {
	currentTime := time.Now()

	// Create a new DNA match record
	dnaMatch := database.DNAMatch{
		ID:                             match.Id,
		SourceIndividualID:             sourceIndividualID,
		Link:                           match.Link,
		TotalSharedSegmentsLengthInCm:  float64(match.TotalSharedSegmentsLengthInCm),
		LargestSharedSegmentLengthInCm: float64(match.LargestSharedSegmentLengthInCm),
		PercentageOfSharedSegments:     float64(match.PercentageOfSharedSegments),
		TotalSharedSegments:            match.TotalSharedSegments,
		ConfidenceLevel:                match.ConfidenceLevel,
		ExactDnaRelationship:           match.ExactDnaRelationship,
		GenealogicalRelationship:       match.GenealogicalRelationship,
		IsRecentlyRecalculated:         match.IsRecentlyRecalculated,
		CreatedAt:                      currentTime.Format("2006-01-02 15:04:05"),
		SubmitterID:                    submitterID,
		MatchIndividualID:              individualID,
		TreeID:                         treeID,
	}

	// Try to find an existing record
	var existingMatch database.DNAMatch
	result := r.db.DB.Where("id = ?", match.Id).First(&existingMatch)

	// If the record exists, update it
	if result.Error == nil {
		r.db.DB.Model(&existingMatch).Updates(dnaMatch)
	} else {
		// If the record doesn't exist, create a new one
		result = r.db.DB.Create(&dnaMatch)
		if result.Error != nil {
			return fmt.Errorf("failed to upsert dna match: %v", result.Error)
		}
	}

	return nil
}

// Helper function to insert or update a submitter
func (r Repository) upsertSubmitter(submitter Submitter) (string, error) {
	if submitter.Id == "" {
		return "", nil
	}

	// Create a new submitter record
	submitterRecord := database.Submitter{
		ID:                      submitter.Id,
		Name:                    submitter.Name,
		NameTransliterated:      submitter.NameTransliterated,
		FirstName:               submitter.FirstName,
		FirstNameTransliterated: submitter.FirstNameTransliterated,
		Link:                    submitter.Link,
		IsPublic:                submitter.IsPublic,
	}

	// Try to find an existing record
	var existingSubmitter database.Submitter
	result := r.db.DB.Where("id = ?", submitter.Id).First(&existingSubmitter)

	// If the record exists, update it
	if result.Error == nil {
		r.db.DB.Model(&existingSubmitter).Updates(submitterRecord)
		return existingSubmitter.ID, nil
	}

	// If the record doesn't exist, create a new one
	result = r.db.DB.Create(&submitterRecord)
	if result.Error != nil {
		return "", fmt.Errorf("failed to upsert submitter: %v", result.Error)
	}

	return submitterRecord.ID, nil
}

// Helper function to insert or update an individual
func (r Repository) upsertIndividual(individual AssociatedIndividual) (string, error) {
	if individual.Id == "" {
		return "", nil
	}

	// Create a new individual record
	individualRecord := database.Individual{
		ID:                      individual.Id,
		FirstName:               individual.FirstName,
		FirstNameTransliterated: individual.FirstNameTransliterated,
		Name:                    individual.Name,
		NameTransliterated:      individual.NameTransliterated,
		Gender:                  individual.Gender,
		AgeGroup:                individual.AgeGroup,
		AgeGroupInYears:         individual.AgeGroupInYears,
		LinkInPedigreeTree:      individual.LinkInPedigreeTree,
		LinkInTree:              individual.LinkInTree,
		BirthPlace:              individual.BirthPlace,
		TreeID:                  individual.Tree.Id,
	}

	// Try to find an existing record
	var existingIndividual database.Individual
	result := r.db.DB.Where("id = ?", individual.Id).First(&existingIndividual)

	// If the record exists, update it
	if result.Error == nil {
		r.db.DB.Model(&existingIndividual).Updates(individualRecord)
		return existingIndividual.ID, nil
	}

	// If the record doesn't exist, create a new one
	result = r.db.DB.Create(&individualRecord)
	if result.Error != nil {
		return "", fmt.Errorf("failed to upsert individual: %v", result.Error)
	}

	return individualRecord.ID, nil
}

// Helper function to insert or update a tree
func (r Repository) upsertTree(tree Tree) (string, error) {
	if tree.Id == "" {
		return "", nil
	}

	// Create a new tree record
	treeRecord := database.Tree{
		ID:                             tree.Id,
		Name:                           tree.Name,
		Link:                           tree.Link,
		IndividualCount:                tree.IndividualCount,
		SiteIsRequestMembershipAllowed: tree.Site.IsRequestMembershipAllowed,
		SiteCreatorID:                  tree.Site.Creator.Id,
		SiteCreatorName:                tree.Site.Creator.Name,
		SiteCreatorNameTransliterated:  tree.Site.Creator.NameTransliterated,
		SiteCreatorCountry:             tree.Site.Creator.Country,
		SiteCreatorCountryCode:         tree.Site.Creator.CountryCode,
		SiteCreatorLink:                tree.Site.Creator.Link,
		SiteCreatorIsPublic:            tree.Site.Creator.IsPublic,
	}

	// Try to find an existing record
	var existingTree database.Tree
	result := r.db.DB.Where("id = ?", tree.Id).First(&existingTree)

	// If the record exists, update it
	if result.Error == nil {
		r.db.DB.Model(&existingTree).Updates(treeRecord)
		return existingTree.ID, nil
	}

	// If the record doesn't exist, create a new one
	result = r.db.DB.Create(&treeRecord)
	if result.Error != nil {
		return "", fmt.Errorf("failed to save tree: %v", result.Error)
	}

	return treeRecord.ID, nil
}

func (r Repository) checkIndividualExists(individualId string) (bool, error) {
	var count int64
	result := r.db.DB.Model(&database.Individual{}).Where("id = ?", individualId).Count(&count)
	if result.Error != nil {
		return true, result.Error
	}
	return count > 0, nil
}

func (r Repository) checkDNAMatchExists(DNAMatchId string) (bool, error) {
	var count int64
	result := r.db.DB.Model(&database.DNAMatch{}).Where("id = ?", DNAMatchId).Count(&count)
	if result.Error != nil {
		return true, result.Error
	}
	return count > 0, nil
}
