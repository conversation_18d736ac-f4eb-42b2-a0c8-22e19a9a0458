# Run the API server with Docker
Write-Host "Building and starting the API server with Docker..."

# Build and start the containers
docker-compose up -d --build

# Wait for the API server to start
Write-Host "Waiting for the API server to start..."
$maxRetries = 10
$retryCount = 0
$serverStarted = $false

while (-not $serverStarted -and $retryCount -lt $maxRetries) {
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:1231/health" -Method GET -TimeoutSec 2 -ErrorAction SilentlyContinue
        if ($response.StatusCode -eq 200) {
            Write-Host "API server started successfully"
            $serverStarted = $true
        }
    } catch {
        Write-Host "Waiting for API server to start... ($($retryCount + 1)/$maxRetries)"
        Start-Sleep -Seconds 1
        $retryCount++
    }
}

if (-not $serverStarted) {
    Write-Host "Failed to start API server within the timeout period"
    Write-Host "Check the logs with: docker-compose logs apiserver"
    exit 1
}

# Show the available endpoints
Write-Host ""
Write-Host "API server is running at http://localhost:1231"
Write-Host ""
Write-Host "Available endpoints:"
Write-Host "- API Documentation: http://localhost:1231/swagger"
Write-Host "- Token Management: http://localhost:1231/api/v1/token"
Write-Host "- DNA Matches: http://localhost:1231/dna-matches"
Write-Host "- Health: http://localhost:1231/health"
Write-Host ""
Write-Host "To stop the API server, run: docker-compose down"
