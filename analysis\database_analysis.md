# MyHeritage DNA Match Database Analysis

## Database Overview

This document provides an analysis of the MyHeritage DNA match database structure, relationships, and data characteristics. The database contains information about DNA matches, individuals, family trees, and shared DNA segments.

## Database Statistics

| Table Name | Record Count |
|------------|--------------|
| dna_matches | 1,460,538 |
| individuals | 308,823 |
| trees | 23,236 |
| shared_segment | 25,805 |
| family | 209,313 |
| individual_family | 494,842 |
| individual_place | 19,476 |
| individual_surname | 273,954 |
| submitters | 24,084 |
| surname | 150,377 |
| api_calls | 0 |

## Key Tables and Their Structure

### dna_matches

Contains information about DNA matches between individuals.

| Column | Data Type | Description |
|--------|-----------|-------------|
| id | character varying(255) | Primary key, unique identifier for the match |
| link | text | Link to the match on MyHeritage |
| total_shared_segments_length_in_cm | double precision | Total amount of shared DNA in centimorgans |
| largest_shared_segment_length_in_cm | double precision | Size of the largest shared DNA segment |
| percentage_of_shared_segments | double precision | Percentage of shared DNA |
| total_shared_segments | integer | Number of shared DNA segments |
| confidence_level | character varying(50) | Confidence level of the match |
| exact_dna_relationship | character varying(255) | Exact relationship prediction (e.g., "Mutter") |
| genealogical_relationship | character varying(255) | Genealogical relationship |
| is_recently_recalculated | boolean | Whether the match was recently recalculated |
| created_at | timestamp without time zone | When the match was created |
| submitter_id | character varying(255) | ID of the submitter |
| match_individual_id | character varying(255) | ID of the matched individual |
| tree_id | character varying(255) | ID of the family tree |
| source_individual_id | character varying(255) | ID of the source individual |
| shared_matches_count | integer | Number of shared matches |
| updated_at | timestamp without time zone | When the match was last updated |
| surnames_count | integer | Number of shared surnames |
| places_count | integer | Number of shared places |
| shared_segments_count | integer | Number of shared DNA segments |
| pedigree_processed | boolean | Whether the pedigree has been processed |

### individuals

Contains information about individuals in the database.

| Column | Data Type | Description |
|--------|-----------|-------------|
| id | character varying | Primary key, unique identifier for the individual |
| first_name | text | First name |
| first_name_transliterated | text | Transliterated first name |
| name | text | Full name |
| name_transliterated | text | Transliterated full name |
| gender | character | Gender (M, F, U) |
| age_group | character varying | Age group |
| age_group_in_years | character varying | Age group in years |
| link_in_pedigree_tree | text | Link to the individual in the pedigree tree |
| link_in_tree | text | Link to the individual in the family tree |
| relationship_description | text | Description of relationship |
| birth_place | text | Place of birth |
| tree_id | character varying | ID of the family tree |
| last_name | character varying | Last name |
| formatted_last_name | text | Formatted last name |
| is_alive | boolean | Whether the individual is alive |
| birth_date_year | character varying | Year of birth |
| death_date_year | character varying | Year of death |

### trees

Contains information about family trees.

| Column | Data Type | Description |
|--------|-----------|-------------|
| id | character varying | Primary key, unique identifier for the tree |
| name | character varying | Name of the tree |
| link | text | Link to the tree |
| individual_count | integer | Number of individuals in the tree |
| site_is_request_membership_allowed | boolean | Whether membership requests are allowed |
| site_creator_id | character varying | ID of the tree creator |
| site_creator_name | character varying | Name of the tree creator |
| site_creator_name_transliterated | character varying | Transliterated name of the tree creator |
| site_creator_country | character varying | Country of the tree creator |
| site_creator_country_code | character varying | Country code of the tree creator |
| site_creator_link | text | Link to the tree creator's profile |
| site_creator_is_public | boolean | Whether the tree creator's profile is public |

### shared_segment

Contains information about shared DNA segments.

| Column | Data Type | Description |
|--------|-----------|-------------|
| id | integer | Primary key, unique identifier for the shared segment |
| match_id | character varying | ID of the DNA match |
| chromosome_id | integer | ID of the chromosome |
| start_position | integer | Start position on the chromosome |
| end_position | integer | End position on the chromosome |
| start_rsid | character varying | Start RSID (reference SNP ID) |
| end_rsid | character varying | End RSID (reference SNP ID) |
| length_in_centimorgans | double precision | Length of the segment in centimorgans |
| snp_count | integer | Number of SNPs in the segment |

## Database Relationships

The database has the following key relationships:

1. **dna_matches to submitters**: Each DNA match is associated with a submitter (dna_matches.submitter_id → submitters.id)

2. **dna_enums to dna_matches**: DNA enums reference DNA matches (dna_enums.system_id → dna_matches.id)

3. **dna_shared_segment to dna_matches**: Shared segments are linked to DNA matches (dna_shared_segment.dna_match_id → dna_matches.id)

4. **individual_family to family**: Individual-family relationships link to families (individual_family.family_id → family.id)

5. **individual_family to individuals**: Individual-family relationships link to individuals (individual_family.individual_id → individuals.id)

6. **individuals to trees**: Individuals belong to family trees (individuals.tree_id → trees.id)

## Data Characteristics

### DNA Matches

- DNA matches include information about the amount of shared DNA in centimorgans
- Match IDs follow the pattern: `dnamatch-D-[UUID]-D-[UUID]`
- Some matches have relationship predictions (like "Mutter" for mother)
- The total shared DNA ranges from small amounts (8.99 cM) to large amounts (3541.3 cM for parent-child relationships)

### Individuals

- Individual IDs follow the pattern: `individual-[NUMBER]-[NUMBER]`
- Some individuals have private information (shown as `<Privat>`)
- Gender is represented as M (male), F (female), or U (unknown)
- Not all individuals have birth place information

### Shared Segments

- Shared segments include specific chromosome locations (start and end positions)
- Segments include genetic distance measurements in centimorgans
- Multiple segments can be associated with a single DNA match

### Family Trees

- Tree IDs follow the pattern: `tree-[NUMBER]-[NUMBER]`
- Trees vary in size from 1 individual to over 1,600 individuals
- Trees include information about the creator, including their name and country

## Conclusion

The MyHeritage DNA match database provides a comprehensive structure for storing and analyzing genetic relationships between individuals. The database contains over 1.4 million DNA matches and over 300,000 individuals, making it a rich source of genetic genealogy information.

The database structure allows for detailed analysis of shared DNA segments, family relationships, and genealogical connections. The relationships between tables enable complex queries that can reveal patterns of genetic inheritance and family connections.
