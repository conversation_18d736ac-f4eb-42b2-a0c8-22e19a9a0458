package sharedmatches

import (
	"encoding/json"
	"fmt"
	"github.com/dmagur/myheritage/internal/fetch"
	log "github.com/sirupsen/logrus"
	"math/rand"
	"time"
)

type HandlerInterface interface {
	Handle() error
}

type Handler struct {
	client     *fetch.MyHeritageApiClient
	logger     *log.Logger
	repository *Repository
}

func NewHandler(
	client *fetch.MyHeritageApiClient,
	logger *log.Logger,
	repository *Repository,
) Handler {
	return Handler{
		client:     client,
		logger:     logger,
		repository: repository,
	}
}

func (h Handler) Handle() error {
	dnaMatchIDs, err := h.repository.GetDnaMatchesWithoutSharedMatchesCount(0, 400)

	if err != nil {
		return fmt.Errorf("error getting DNA matches from DB: %w", err)
	}

	offset := 0
	// Now loop through the slice of DnaMatch
	for _, match := range dnaMatchIDs {
		if match.MatchIndividualID == "" {
			continue
		}
		// Output the match details
		fmt.Println("-Shared matches-")
		fmt.Printf("Offset: %d\n", offset)
		fmt.Printf("Match ID: %s\n", match.ID)
		fmt.Printf("Match Individual ID: %s\n", match.MatchIndividualID)
		offset++

		sharedOffset := 0
		limit := 10
		sharedCount := 0
		for {
			apiResponse, err := h.FetchDnaMatches(match.ID, sharedOffset, limit)

			if err != nil {
				return fmt.Errorf("error fetching DNA matches: %w", err)
			}

			if len(apiResponse.Data.DnaMatch.DnaSharedMatches.Data) == 0 || sharedOffset > 2000 {
				break
			}

			fmt.Printf("Shared matches count: %d\n", apiResponse.Data.DnaMatch.DnaSharedMatches.Count)

			//fmt.Println("------------Shared Matches-------------")
			// Store the matches and print them to the console
			for _, sharedMatch := range apiResponse.Data.DnaMatch.DnaSharedMatches.Data {
				if sharedMatch.SharedIndividual.ID == "" {
					//fmt.Printf("Shared Match: %v\n", sharedMatch)
					continue
				}

				err := h.repository.UpsertDnaMatch(sharedMatch, match.MatchIndividualID)

				if err != nil {
					return fmt.Errorf("error upserting DNA matches: %w", err)
				}

				// Output match details to the console
				//fmt.Printf("Match ID: %s\n", sharedMatch.DnaMatch.ID)
				//fmt.Printf("Match Individual ID: %s\n", sharedMatch.SharedIndividual.ID)
				//fmt.Printf("Link: %s\n", match.DnaMatch.Link)
				//fmt.Printf("Total Shared Segments Length (in cm): %.2f\n", match.TotalSharedSegmentsLengthInCm)
				//fmt.Printf("Largest Shared Segment Length (in cm): %.2f\n", match.LargestSharedSegmentLengthInCm)
				//fmt.Printf("Percentage of Shared Segments: %.2f%%\n", match.PercentageOfSharedSegments)
				//fmt.Printf("Offset: %d\n", offset)
			}

			randomNumber := rand.Intn(10) + 1
			time.Sleep(time.Duration(randomNumber) * time.Second)
			sharedOffset += limit

			if sharedOffset+limit > apiResponse.Data.DnaMatch.DnaSharedMatches.Count {
				limit = apiResponse.Data.DnaMatch.DnaSharedMatches.Count - sharedOffset
			}

			if apiResponse.Data.DnaMatch.DnaSharedMatches.Count <= sharedOffset {
				sharedCount = apiResponse.Data.DnaMatch.DnaSharedMatches.Count
				break
			}
		}

		err = h.repository.updateSharedMatchesCount(match.ID, sharedCount)
		if err != nil {
			return fmt.Errorf("error updating shared matches count: %w", err)
		}
	}

	return nil
}

func (h *Handler) FetchDnaMatches(dnaMatchId string, offset int, limit int) (*ApiResponse, error) {
	url := "https://familygraphql.myheritage.com/dna_single_match_get_shared_matches/"

	query := fmt.Sprintf(`"{dna_match(id:\"%s\",lang:\"DE\"){dna_shared_matches(offset:%d,limit:%d,sort_shared_matches_by:\"match\"){count data{dna_matches_cluster_shared_segments_count is_obfuscated shared_member{personal_photo{...personal_photo_info}age_group gender name id}shared_individual{personal_photo{...personal_photo_info}age_group gender name id link_in_pedigree_tree tree{individual_count site{creator{id name country_code}}}}dna_match{...shared_match_details link dna_cm_explainer{most_probable_relationships{...dna_match_relationship}calculation_strategy}is_favorite match_notes{data{id text}}match_labels{data{id}}}other_dna_match{...shared_match_details dna_cm_explainer{most_probable_relationships{...dna_match_relationship}calculation_strategy}}shared_profile_personal_image{thumbnails(thumbnail_size:\"16x16\"){url}}}}}}fragment shared_match_details on DnaMatch{id refined_dna_relationships{relationship_type relationship_degree}exact_dna_relationship percentage_of_shared_segments total_shared_segments_length_in_cm}fragment personal_photo_info on Photo{thumbnails(thumbnail_size:\"96x96\"){url}}fragment dna_match_relationship on DnaMatchRelationship{relationship_type relationship_class path_type probability most_recent_common_ancestor_relationship_type most_recent_common_ancestor_relationship_class}"`,
		dnaMatchId, offset, limit)

	body, err := h.client.Fetch(url, query, "DNA Single Match - get shared matches")
	if err != nil {
		fmt.Println("Error fetching response:", err)
		return nil, err
	}

	var apiResponse ApiResponse
	err = json.Unmarshal(body, &apiResponse)
	if err != nil {
		// Print the response body if JSON unmarshalling fails
		fmt.Println("Error unmarshalling JSON response:", err)
		fmt.Printf("Response body: %s\n", string(body))
		return nil, err
	}

	return &apiResponse, nil
}
