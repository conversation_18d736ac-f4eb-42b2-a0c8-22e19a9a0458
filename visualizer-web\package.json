{"name": "myheritage-visualizer", "version": "1.0.0", "description": "MyHeritage DNA Database Visualizer", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.4.3", "@types/jest": "^27.5.2", "@types/node": "^16.18.11", "@types/react": "^18.0.26", "@types/react-dom": "^18.0.10", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "typescript": "^4.9.4", "web-vitals": "^2.1.4", "@mui/material": "^5.11.0", "@mui/icons-material": "^5.11.0", "@emotion/react": "^11.10.5", "@emotion/styled": "^11.10.5", "react-router-dom": "^6.6.1", "@tanstack/react-query": "^4.20.4", "axios": "^1.2.2", "recharts": "^2.4.3", "d3": "^7.7.0", "@types/d3": "^7.4.0", "three": "^0.148.0", "@types/three": "^0.148.0", "3d-force-graph": "^1.70.19", "leaflet": "^1.9.3", "react-leaflet": "^4.2.0", "@types/leaflet": "^1.9.0", "cytoscape": "^3.23.0", "@types/cytoscape": "^3.19.9", "cytoscape-react": "^1.0.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/react-router-dom": "^5.3.3", "eslint": "^8.30.0", "@typescript-eslint/eslint-plugin": "^5.48.0", "@typescript-eslint/parser": "^5.48.0"}, "proxy": "http://localhost:1231"}