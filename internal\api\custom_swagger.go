package api

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// CompleteOpenAPISpec is the complete OpenAPI specification
const CompleteOpenAPISpec = `{
  "openapi": "3.0.0",
  "info": {
    "title": "Myheritage Downloader - API",
    "version": "0.3.0"
  },
  "servers": [
    {
      "description": "Development",
      "url": "http://localhost:1231"
    }
  ],
  "tags": [
    {
      "name": "Myheritage Downloader",
      "description": "Myheritage Downloader"
    },
    {
      "name": "Matches",
      "description": "DNA Matches"
    },
    {
      "name": "Token Management",
      "description": "API Token Management"
    },
    {
      "name": "Health",
      "description": "Health and Status Endpoints"
    },
    {
      "name": "Metrics",
      "description": "Metrics Endpoints"
    }
  ],
  "security": [
    {
      "ApiKeyAuth": []
    }
  ],
  "components": {
    "securitySchemes": {
      "ApiKeyAuth": {
        "type": "apiKey",
        "in": "header",
        "name": "Authorization",
        "description": "API key authentication. Format: \"Bearer {token}\""
      }
    },
    "schemas": {
      "DnaMatch": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string"
          },
          "matchName": {
            "type": "string"
          },
          "matchGender": {
            "type": "string"
          },
          "matchCountry": {
            "type": "string"
          },
          "matchManagedBy": {
            "type": "string"
          },
          "matchTreeName": {
            "type": "string"
          },
          "matchTreeUrl": {
            "type": "string"
          },
          "matchTreePersonsCount": {
            "type": "integer"
          },
          "matchTreeLastUpdated": {
            "type": "string",
            "format": "date-time"
          },
          "matchHasTree": {
            "type": "boolean"
          },
          "matchHasSharedSegments": {
            "type": "boolean"
          },
          "matchHasSharedCentimorgans": {
            "type": "boolean"
          },
          "matchSharedCentimorgans": {
            "type": "number"
          },
          "matchSharedSegmentsCount": {
            "type": "integer"
          },
          "matchSharedSegments": {
            "type": "array",
            "items": {
              "type": "object",
              "properties": {
                "chromosome": {
                  "type": "string"
                },
                "startPosition": {
                  "type": "integer"
                },
                "endPosition": {
                  "type": "integer"
                },
                "centimorgans": {
                  "type": "number"
                },
                "snps": {
                  "type": "integer"
                }
              }
            }
          },
          "matchHasCommonAncestors": {
            "type": "boolean"
          },
          "matchCommonAncestors": {
            "type": "array",
            "items": {
              "type": "object",
              "properties": {
                "name": {
                  "type": "string"
                },
                "birthYear": {
                  "type": "integer"
                },
                "deathYear": {
                  "type": "integer"
                },
                "location": {
                  "type": "string"
                }
              }
            }
          },
          "matchHasSmartMatches": {
            "type": "boolean"
          },
          "matchSmartMatches": {
            "type": "array",
            "items": {
              "type": "object",
              "properties": {
                "name": {
                  "type": "string"
                },
                "birthYear": {
                  "type": "integer"
                },
                "deathYear": {
                  "type": "integer"
                },
                "location": {
                  "type": "string"
                }
              }
            }
          },
          "matchHasSharedEthnicity": {
            "type": "boolean"
          },
          "matchSharedEthnicity": {
            "type": "array",
            "items": {
              "type": "object",
              "properties": {
                "ethnicity": {
                  "type": "string"
                },
                "percentage": {
                  "type": "number"
                }
              }
            }
          },
          "matchReviewStatus": {
            "type": "string"
          },
          "matchAddedDate": {
            "type": "string",
            "format": "date-time"
          },
          "matchLastUpdated": {
            "type": "string",
            "format": "date-time"
          }
        }
      },
      "ErrorResponse": {
        "type": "object",
        "properties": {
          "error": {
            "type": "string"
          },
          "message": {
            "type": "string"
          },
          "status": {
            "type": "integer"
          }
        }
      },
      "TokenResponse": {
        "type": "object",
        "properties": {
          "token": {
            "type": "string",
            "description": "The masked token value"
          },
          "created_at": {
            "type": "string",
            "format": "date-time",
            "description": "The creation timestamp"
          },
          "is_active": {
            "type": "boolean",
            "description": "Whether the token is active"
          }
        }
      },
      "UpdateTokenRequest": {
        "type": "object",
        "properties": {
          "token": {
            "type": "string",
            "description": "The new token value"
          }
        },
        "required": ["token"]
      },
      "HealthResponse": {
        "type": "object",
        "properties": {
          "status": {
            "type": "string",
            "description": "Health status",
            "example": "ok"
          }
        }
      }
    }
  },
  "paths": {
    "/dna-matches": {
      "get": {
        "summary": "Get dna matches list",
        "tags": [
          "Matches"
        ],
        "operationId": "getMatches",
        "responses": {
          "200": {
            "description": "Ok",
            "content": {
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/DnaMatch"
                  }
                }
              }
            }
          },
          "default": {
            "description": "Error response",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ErrorResponse"
                }
              }
            }
          }
        }
      }
    },
    "/api/v1/token": {
      "get": {
        "summary": "Get the current active token",
        "tags": [
          "Token Management"
        ],
        "operationId": "getToken",
        "responses": {
          "200": {
            "description": "Ok",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "token": {
                      "$ref": "#/components/schemas/TokenResponse"
                    },
                    "message": {
                      "type": "string",
                      "description": "Message when no token is found"
                    }
                  }
                }
              }
            }
          },
          "default": {
            "description": "Error response",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ErrorResponse"
                }
              }
            }
          }
        }
      },
      "post": {
        "summary": "Update the API token",
        "tags": [
          "Token Management"
        ],
        "operationId": "updateToken",
        "requestBody": {
          "required": true,
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/UpdateTokenRequest"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "Ok",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "message": {
                      "type": "string",
                      "description": "Success message"
                    }
                  }
                }
              }
            }
          },
          "400": {
            "description": "Bad Request",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "error": {
                      "type": "string",
                      "description": "Error message"
                    }
                  }
                }
              }
            }
          },
          "default": {
            "description": "Error response",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ErrorResponse"
                }
              }
            }
          }
        }
      }
    },
    "/health": {
      "get": {
        "summary": "Health check endpoint",
        "tags": [
          "Health"
        ],
        "operationId": "getHealth",
        "security": [],
        "responses": {
          "200": {
            "description": "Ok",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/HealthResponse"
                }
              }
            }
          }
        }
      }
    },
    "/liveness": {
      "get": {
        "summary": "Liveness check endpoint",
        "tags": [
          "Health"
        ],
        "operationId": "getLiveness",
        "security": [],
        "responses": {
          "200": {
            "description": "Ok",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "string"
                }
              }
            }
          }
        }
      }
    },
    "/readiness": {
      "get": {
        "summary": "Readiness check endpoint",
        "tags": [
          "Health"
        ],
        "operationId": "getReadiness",
        "security": [],
        "responses": {
          "200": {
            "description": "Ok",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "string"
                }
              }
            }
          }
        }
      }
    },
    "/metrics": {
      "get": {
        "summary": "Prometheus metrics endpoint",
        "tags": [
          "Metrics"
        ],
        "operationId": "getMetrics",
        "security": [],
        "responses": {
          "200": {
            "description": "Ok",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "string",
                  "description": "Prometheus metrics"
                }
              }
            }
          }
        }
      }
    }
  }
}`

// CustomSwaggerJSON returns the complete OpenAPI specification as JSON
func CustomSwaggerJSON() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Data(http.StatusOK, "application/json", []byte(CompleteOpenAPISpec))
	}
}

// CustomSwaggerUI returns the Swagger UI HTML page
func CustomSwaggerUI() gin.HandlerFunc {
	return func(c *gin.Context) {
		html := `
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="description" content="SwaggerUI" />
    <title>MyHeritage API - Swagger UI</title>
    <link rel="stylesheet" href="https://unpkg.com/swagger-ui-dist@4.5.0/swagger-ui.css" />
  </head>
  <body>
  <div id="swagger-ui"></div>
  <script src="https://unpkg.com/swagger-ui-dist@4.5.0/swagger-ui-bundle.js" crossorigin></script>
  <script src="https://unpkg.com/swagger-ui-dist@4.5.0/swagger-ui-standalone-preset.js" crossorigin></script>
  <script>
    window.onload = () => {
      window.ui = SwaggerUIBundle({
        url: '/swagger.json',
        dom_id: '#swagger-ui',
        presets: [
          SwaggerUIBundle.presets.apis,
          SwaggerUIStandalonePreset
        ],
        layout: "BaseLayout",
      });
    };
  </script>
  </body>
</html>
`
		c.Data(http.StatusOK, "text/html; charset=utf-8", []byte(html))
	}
}
