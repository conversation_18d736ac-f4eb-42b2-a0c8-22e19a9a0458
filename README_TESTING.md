# GORM Migration Testing Guide

This document describes how to test the MyHeritage GORM migration using the automated test script.

## Test Script Overview

The `test_gorm_migration.sh` script provides comprehensive testing for the GORM migration, covering:

- **Go Module Dependencies** - Verifies GORM and database drivers are installed
- **Package Compilation** - Tests all core packages compile successfully  
- **Application Compilation** - Tests all main applications build correctly
- **Unit Tests** - Runs all package tests
- **Code Quality** - Checks formatting and linting
- **File Structure** - Verifies required files exist
- **Docker Builds** - Tests Docker container builds (optional)
- **Git Repository Status** - Shows current repository state
- **Performance Tests** - Race condition and memory leak detection (optional)

## Usage

### Quick Test (Recommended)
```bash
./test_gorm_migration.sh --quick
```
Runs essential tests only, skipping Docker builds and performance tests. Fast execution (~30 seconds).

### Full Test Suite
```bash
./test_gorm_migration.sh
```
Runs all tests including Docker builds and performance tests. Takes longer (~5-10 minutes).

### Skip Docker Tests
```bash
./test_gorm_migration.sh --no-docker
```
Runs all tests except Docker builds. Good for environments without Docker.

### Help
```bash
./test_gorm_migration.sh --help
```
Shows all available options and usage examples.

## Test Categories

### 1. Go Module Dependencies
- ✅ GORM core library
- ✅ PostgreSQL driver  
- ✅ MySQL/MariaDB driver

### 2. Core Package Compilation
- ✅ `internal/database` - Database abstraction layer
- ✅ `internal/fetch` - Data fetching logic
- ✅ `internal/models` - GORM models

### 3. Application Compilation  
- ✅ `cmd/myheritagedownloader` - Main downloader application
- ✅ `cmd/apiserver` - API server
- ✅ `cmd/clique` - Clique analysis tool

### 4. Unit Tests
- ✅ Database package tests
- ✅ Models package tests  
- ✅ All package tests with `-short` flag

### 5. Code Quality
- ✅ `go fmt` formatting check
- ✅ `go vet` static analysis

### 6. File Structure
- ✅ Database documentation (`internal/database/README.md`)
- ✅ Core database files exist
- ✅ Application directories exist

### 7. Docker Builds (Optional)
- ✅ MyHeritage Downloader container
- ✅ API Server container

### 8. Performance Tests (Optional)
- ✅ Race condition detection
- ✅ Memory leak detection

## Manual Testing Steps

If you prefer to run tests manually, here are the key commands:

### 1. Test Dependencies
```bash
go list -m gorm.io/gorm
go list -m gorm.io/driver/postgres  
go list -m gorm.io/driver/mysql
```

### 2. Test Compilation
```bash
go build ./internal/database
go build ./internal/fetch
go build ./internal/models
go build ./cmd/myheritagedownloader
go build ./cmd/apiserver
go build ./cmd/clique
go build ./...
```

### 3. Run Tests
```bash
go test ./internal/database -v
go test ./internal/models -v
go test ./... -short
```

### 4. Code Quality
```bash
go fmt ./...
go vet ./...
```

### 5. Docker Builds
```bash
docker build -f Dockerfile.downloader -t myheritage-downloader .
docker build -f Dockerfile.apiserver -t myheritage-apiserver .
```

## Expected Results

### ✅ Success Indicators
- All packages compile without errors
- All tests pass
- No formatting issues
- No static analysis warnings
- Docker builds complete successfully
- Clean git repository status

### ❌ Failure Indicators  
- Compilation errors
- Test failures
- Formatting violations
- Static analysis warnings
- Docker build failures
- Missing dependencies

## Troubleshooting

### Common Issues

**Missing Dependencies**
```bash
go mod tidy
go mod download
```

**Compilation Errors**
- Check Go version compatibility
- Verify all imports are correct
- Run `go clean -cache`

**Test Failures**
- Check database connectivity (if using real databases)
- Verify test data setup
- Run tests individually for debugging

**Docker Build Failures**
- Ensure Docker is running
- Check Dockerfile syntax
- Verify build context

### Getting Help

1. Run the test script with verbose output:
   ```bash
   ./test_gorm_migration.sh -v
   ```

2. Check individual test components manually

3. Review the GORM migration documentation in `internal/database/README.md`

## Integration with CI/CD

The test script is designed to work in CI/CD environments:

```yaml
# Example GitHub Actions step
- name: Run GORM Migration Tests
  run: |
    chmod +x test_gorm_migration.sh
    ./test_gorm_migration.sh --quick
```

For environments without Docker:
```yaml
- name: Run GORM Migration Tests (No Docker)
  run: ./test_gorm_migration.sh --no-docker
```

## Test Script Maintenance

The test script is located at `./test_gorm_migration.sh` and should be updated when:

- New packages are added
- New applications are created  
- New dependencies are introduced
- Test requirements change

The script uses colored output and provides detailed progress information to help identify issues quickly.
