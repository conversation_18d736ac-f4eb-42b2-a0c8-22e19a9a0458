import React from 'react';
import { useQuery } from '@tanstack/react-query';
import {
  Box,
  Grid,
  Paper,
  Typography,
  Card,
  CardContent,
  CircularProgress,
  Alert,
  Chip,
} from '@mui/material';
import {
  People as PeopleIcon,
  Biotech as BiotechIcon,
  AccountTree as TreeIcon,
  Timeline as TimelineIcon,
  Group as GroupIcon,
  Badge as BadgeIcon,
  Public as PublicIcon,
} from '@mui/icons-material';

import { analyticsApi, OverviewStats } from '../../services/api';
import StatCard from '../../components/Dashboard/StatCard';

const Dashboard: React.FC = () => {
  const {
    data: overviewData,
    isLoading,
    error,
    refetch,
  } = useQuery<OverviewStats>({
    queryKey: ['overview'],
    queryFn: analyticsApi.getOverview,
    refetchInterval: 30000, // Refetch every 30 seconds
  });

  if (isLoading) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        minHeight="400px"
      >
        <CircularProgress size={60} />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert
        severity="error"
        action={
          <Chip
            label="Retry"
            onClick={() => refetch()}
            color="error"
            variant="outlined"
          />
        }
      >
        Failed to load dashboard data. Please check if the API server is running.
      </Alert>
    );
  }

  const stats = [
    {
      title: 'DNA Matches',
      value: overviewData?.total_dna_matches || 0,
      icon: <BiotechIcon />,
      color: '#1976d2',
      description: 'Total DNA matches in database',
    },
    {
      title: 'Individuals',
      value: overviewData?.total_individuals || 0,
      icon: <PeopleIcon />,
      color: '#388e3c',
      description: 'Unique individuals',
    },
    {
      title: 'Family Trees',
      value: overviewData?.total_trees || 0,
      icon: <TreeIcon />,
      color: '#f57c00',
      description: 'Family tree structures',
    },
    {
      title: 'DNA Segments',
      value: overviewData?.total_shared_segments || 0,
      icon: <TimelineIcon />,
      color: '#7b1fa2',
      description: 'Shared DNA segments',
    },
    {
      title: 'Submitters',
      value: overviewData?.total_submitters || 0,
      icon: <BadgeIcon />,
      color: '#c2185b',
      description: 'DNA kit submitters',
    },
    {
      title: 'Families',
      value: overviewData?.total_families || 0,
      icon: <GroupIcon />,
      color: '#00796b',
      description: 'Family relationships',
    },
    {
      title: 'Surnames',
      value: overviewData?.total_surnames || 0,
      icon: <PublicIcon />,
      color: '#5d4037',
      description: 'Unique surnames',
    },
  ];

  return (
    <Box>
      {/* Header */}
      <Box mb={4}>
        <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
          Dashboard
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          Overview of your MyHeritage DNA database
        </Typography>
      </Box>

      {/* Statistics Cards */}
      <Grid container spacing={3} mb={4}>
        {stats.map((stat, index) => (
          <Grid item xs={12} sm={6} md={4} lg={3} key={index}>
            <StatCard {...stat} />
          </Grid>
        ))}
      </Grid>

      {/* Database Summary Section */}
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              Database Summary
            </Typography>
            <Typography variant="body1" color="text.secondary" paragraph>
              Your MyHeritage database contains comprehensive genealogical data with{' '}
              <strong>{overviewData?.total_dna_matches?.toLocaleString()}</strong> DNA matches,{' '}
              <strong>{overviewData?.total_individuals?.toLocaleString()}</strong> individuals, and{' '}
              <strong>{overviewData?.total_families?.toLocaleString()}</strong> family relationships.
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Last updated: {overviewData?.last_updated ? new Date(overviewData.last_updated).toLocaleString() : 'Unknown'}
            </Typography>
          </Paper>
        </Grid>

        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Quick Actions
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6} md={3}>
                <Card
                  sx={{
                    cursor: 'pointer',
                    '&:hover': { backgroundColor: 'grey.50' },
                  }}
                  onClick={() => window.open('/network', '_self')}
                >
                  <CardContent sx={{ textAlign: 'center' }}>
                    <BiotechIcon sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
                    <Typography variant="h6">View Network</Typography>
                    <Typography variant="body2" color="text.secondary">
                      Explore DNA match connections
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <Card
                  sx={{
                    cursor: 'pointer',
                    '&:hover': { backgroundColor: 'grey.50' },
                  }}
                  onClick={() => window.open('/geography', '_self')}
                >
                  <CardContent sx={{ textAlign: 'center' }}>
                    <PublicIcon sx={{ fontSize: 40, color: 'success.main', mb: 1 }} />
                    <Typography variant="h6">Geographic View</Typography>
                    <Typography variant="body2" color="text.secondary">
                      See global distribution
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <Card
                  sx={{
                    cursor: 'pointer',
                    '&:hover': { backgroundColor: 'grey.50' },
                  }}
                  onClick={() => window.open('/analytics', '_self')}
                >
                  <CardContent sx={{ textAlign: 'center' }}>
                    <TimelineIcon sx={{ fontSize: 40, color: 'warning.main', mb: 1 }} />
                    <Typography variant="h6">Analytics</Typography>
                    <Typography variant="body2" color="text.secondary">
                      Deep dive into data
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <Card
                  sx={{
                    cursor: 'pointer',
                    '&:hover': { backgroundColor: 'grey.50' },
                  }}
                  onClick={() => window.open('/api/v1/analytics/overview', '_blank')}
                >
                  <CardContent sx={{ textAlign: 'center' }}>
                    <TreeIcon sx={{ fontSize: 40, color: 'info.main', mb: 1 }} />
                    <Typography variant="h6">API Access</Typography>
                    <Typography variant="body2" color="text.secondary">
                      Direct API endpoints
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Dashboard;
