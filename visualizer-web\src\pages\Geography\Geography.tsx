import React from 'react';
import { useQuery } from '@tanstack/react-query';
import {
  Box,
  Typography,
  Paper,
  Grid,
  CircularProgress,
  Alert,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material';
import {
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
  Tooltip,
  ResponsiveContainer,
  Legend,
} from 'recharts';

import { analyticsApi, CountryStats } from '../../services/api';

// Colors for pie chart
const COLORS = [
  '#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8',
  '#82CA9D', '#FFC658', '#FF7C7C', '#8DD1E1', '#D084D0',
  '#87D068', '#FFA07A', '#98D8C8', '#F7DC6F', '#BB8FCE'
];

const Geography: React.FC = () => {
  const {
    data: countryData,
    isLoading,
    error,
    refetch,
  } = useQuery<CountryStats[]>({
    queryKey: ['topCountries'],
    queryFn: () => analyticsApi.getTopCountries(15),
  });

  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress size={60} />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert
        severity="error"
        action={<Button onClick={() => refetch()}>Retry</Button>}
      >
        Failed to load geographic data.
      </Alert>
    );
  }

  return (
    <Box>
      <Box mb={4}>
        <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
          Geographic Distribution
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          Global distribution of DNA matches and individuals
        </Typography>
      </Box>

      <Grid container spacing={3}>
        {/* Pie Chart */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, height: '500px' }}>
            <Typography variant="h6" gutterBottom>
              Top Countries Distribution
            </Typography>
            {countryData && (() => {
              const totalMatches = countryData.reduce((sum, c) => sum + c.count, 0);
              const top10Countries = countryData.slice(0, 10);

              return (
                <ResponsiveContainer width="100%" height="90%">
                  <PieChart>
                    <Pie
                      data={top10Countries}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ country, value }) => {
                        const percentage = ((value / totalMatches) * 100).toFixed(1);
                        return `${country} ${percentage}%`;
                      }}
                      outerRadius={120}
                      fill="#8884d8"
                      dataKey="count"
                    >
                      {top10Countries.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip
                      formatter={(value) => {
                        const numValue = Number(value);
                        const percentage = ((numValue / totalMatches) * 100).toFixed(1);
                        return [`${numValue} matches (${percentage}%)`, 'Count'];
                      }}
                    />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              );
            })()}
          </Paper>
        </Grid>

        {/* Table */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, height: '500px', overflow: 'auto' }}>
            <Typography variant="h6" gutterBottom>
              Countries by Match Count
            </Typography>
            {countryData && (
              <TableContainer>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell><strong>Rank</strong></TableCell>
                      <TableCell><strong>Country</strong></TableCell>
                      <TableCell align="right"><strong>Matches</strong></TableCell>
                      <TableCell align="right"><strong>%</strong></TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {countryData.map((country, index) => {
                      const total = countryData.reduce((sum, c) => sum + c.count, 0);
                      const percentage = ((country.count / total) * 100).toFixed(1);
                      return (
                        <TableRow key={country.country}>
                          <TableCell>{index + 1}</TableCell>
                          <TableCell>{country.country}</TableCell>
                          <TableCell align="right">{country.count.toLocaleString()}</TableCell>
                          <TableCell align="right">{percentage}%</TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </TableContainer>
            )}
          </Paper>
        </Grid>

        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Alert severity="info">
              Interactive world map visualization is under development.
              This will include heat maps, migration patterns, and regional clustering.
            </Alert>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Geography;
