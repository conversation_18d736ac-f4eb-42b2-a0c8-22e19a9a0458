package database

import (
	"os"
	"regexp"
)

// getEnv gets an environment variable or returns a default value
func getEnv(key, fallback string) string {
	if value, exists := os.LookupEnv(key); exists {
		return value
	}
	return fallback
}

// convertPgParamsToMariaDB converts PostgreSQL-style $n parameters to MariaDB-style ? parameters
func convertPgParamsToMariaDB(query string) string {
	// Regular expression to match PostgreSQL-style parameters ($1, $2, etc.)
	re := regexp.MustCompile(`\$\d+`)
	
	// Replace all occurrences of $n with ?
	return re.ReplaceAllString(query, "?")
}
