package api

import (
	"net/http"
	"strconv"
	"time"

	"github.com/dmagur/myheritage/internal/database"
	"github.com/gin-gonic/gin"
	log "github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// AnalyticsHandler handles API requests for analytics
type AnalyticsHandler struct {
	db     *gorm.DB
	logger *log.Logger
}

// NewAnalyticsHandler creates a new AnalyticsHandler
func NewAnalyticsHandler(db *gorm.DB, logger *log.Logger) *AnalyticsHandler {
	return &AnalyticsHandler{
		db:     db,
		logger: logger,
	}
}

// RegisterRoutes registers the analytics routes
func (h *AnalyticsHandler) RegisterRoutes(router *gin.Engine) {
	// Group API routes under /api/v1/analytics
	analytics := router.Group("/api/v1/analytics")
	{
		analytics.GET("/overview", h.GetOverview)
		analytics.GET("/matches/stats", h.GetMatchStats)
		analytics.GET("/geography/top-countries", h.GetTopCountries)
		analytics.GET("/individuals/stats", h.GetIndividualStats)

		// Network visualization endpoints
		analytics.GET("/network/graph", h.GetNetworkGraph)
		analytics.GET("/network/cliques", h.GetNetworkCliques)

		// Enhanced data endpoints with filtering
		analytics.GET("/matches", h.GetMatches)
		analytics.GET("/individuals", h.GetIndividuals)
		analytics.GET("/segments", h.GetSegments)
	}
}

// GetOverview returns database overview statistics
func (h *AnalyticsHandler) GetOverview(c *gin.Context) {
	h.logger.Info("Getting database overview statistics")

	var overview OverviewResponse
	overview.LastUpdated = time.Now()

	// Use a single optimized query to get all counts at once
	// This is much faster than multiple separate COUNT queries
	var result struct {
		DNAMatches     int64 `json:"dna_matches"`
		Individuals    int64 `json:"individuals"`
		Trees          int64 `json:"trees"`
		SharedSegments int64 `json:"shared_segments"`
		Submitters     int64 `json:"submitters"`
		Families       int64 `json:"families"`
		Surnames       int64 `json:"surnames"`
	}

	// Execute optimized query with timeout
	ctx := c.Request.Context()
	query := `
		SELECT
			(SELECT COUNT(*) FROM dna_matches) as dna_matches,
			(SELECT COUNT(*) FROM individuals) as individuals,
			(SELECT COUNT(*) FROM trees) as trees,
			(SELECT COUNT(*) FROM shared_segment) as shared_segments,
			(SELECT COUNT(*) FROM submitters) as submitters,
			(SELECT COUNT(*) FROM family) as families,
			(SELECT COUNT(*) FROM surname) as surnames
	`

	if err := h.db.WithContext(ctx).Raw(query).Scan(&result).Error; err != nil {
		h.logger.WithError(err).Error("Failed to get overview statistics")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get overview statistics"})
		return
	}

	overview.TotalDNAMatches = result.DNAMatches
	overview.TotalIndividuals = result.Individuals
	overview.TotalTrees = result.Trees
	overview.TotalSharedSegments = result.SharedSegments
	overview.TotalSubmitters = result.Submitters
	overview.TotalFamilies = result.Families
	overview.TotalSurnames = result.Surnames

	h.logger.WithFields(log.Fields{
		"dna_matches":     overview.TotalDNAMatches,
		"individuals":     overview.TotalIndividuals,
		"trees":           overview.TotalTrees,
		"shared_segments": overview.TotalSharedSegments,
		"submitters":      overview.TotalSubmitters,
		"families":        overview.TotalFamilies,
		"surnames":        overview.TotalSurnames,
	}).Info("Database overview statistics retrieved")

	c.JSON(http.StatusOK, overview)
}

// GetMatchStats returns DNA match statistics
func (h *AnalyticsHandler) GetMatchStats(c *gin.Context) {
	h.logger.Info("Getting DNA match statistics")

	var stats MatchStatsResponse
	ctx := c.Request.Context()

	// Get basic statistics in a single optimized query
	var basicStats struct {
		TotalMatches int64   `json:"total_matches"`
		AvgCM        float64 `json:"avg_cm"`
		MaxCM        float64 `json:"max_cm"`
		MinCM        float64 `json:"min_cm"`
	}

	basicQuery := `
		SELECT
			COUNT(*) as total_matches,
			AVG(CASE WHEN total_shared_segments_length_in_cm > 0 THEN total_shared_segments_length_in_cm END) as avg_cm,
			MAX(total_shared_segments_length_in_cm) as max_cm,
			MIN(CASE WHEN total_shared_segments_length_in_cm > 0 THEN total_shared_segments_length_in_cm END) as min_cm
		FROM dna_matches
	`

	if err := h.db.WithContext(ctx).Raw(basicQuery).Scan(&basicStats).Error; err != nil {
		h.logger.WithError(err).Error("Failed to get basic match statistics")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get basic match statistics"})
		return
	}

	stats.TotalMatches = basicStats.TotalMatches
	stats.AvgSharedCM = basicStats.AvgCM
	stats.MaxSharedCM = basicStats.MaxCM
	stats.MinSharedCM = basicStats.MinCM

	// Get confidence level distribution with timeout
	var confidenceResults []struct {
		ConfidenceLevel string
		Count           int64
	}

	if err := h.db.WithContext(ctx).Model(&database.DNAMatch{}).
		Select("COALESCE(confidence_level, 'unknown') as confidence_level, COUNT(*) as count").
		Group("confidence_level").
		Limit(20).
		Scan(&confidenceResults).Error; err != nil {
		h.logger.WithError(err).Error("Failed to get confidence distribution")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get confidence distribution"})
		return
	}

	stats.MatchesByConfidence = make(map[string]int64)
	for _, result := range confidenceResults {
		stats.MatchesByConfidence[result.ConfidenceLevel] = result.Count
	}

	// Get relationship distribution with timeout and limit
	var relationshipResults []struct {
		ExactDnaRelationship string
		Count                int64
	}

	if err := h.db.WithContext(ctx).Model(&database.DNAMatch{}).
		Select("COALESCE(exact_dna_relationship, 'unknown') as exact_dna_relationship, COUNT(*) as count").
		Group("exact_dna_relationship").
		Order("count DESC").
		Limit(15).
		Scan(&relationshipResults).Error; err != nil {
		h.logger.WithError(err).Error("Failed to get relationship distribution")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get relationship distribution"})
		return
	}

	stats.MatchesByRelationship = make(map[string]int64)
	for _, result := range relationshipResults {
		stats.MatchesByRelationship[result.ExactDnaRelationship] = result.Count
	}

	// Get cM distribution using a single optimized query
	distributionQuery := `
		SELECT
			CASE
				WHEN total_shared_segments_length_in_cm >= 0 AND total_shared_segments_length_in_cm < 10 THEN '0-10'
				WHEN total_shared_segments_length_in_cm >= 10 AND total_shared_segments_length_in_cm < 20 THEN '10-20'
				WHEN total_shared_segments_length_in_cm >= 20 AND total_shared_segments_length_in_cm < 50 THEN '20-50'
				WHEN total_shared_segments_length_in_cm >= 50 AND total_shared_segments_length_in_cm < 100 THEN '50-100'
				WHEN total_shared_segments_length_in_cm >= 100 AND total_shared_segments_length_in_cm < 200 THEN '100-200'
				WHEN total_shared_segments_length_in_cm >= 200 AND total_shared_segments_length_in_cm < 500 THEN '200-500'
				WHEN total_shared_segments_length_in_cm >= 500 AND total_shared_segments_length_in_cm < 1000 THEN '500-1000'
				WHEN total_shared_segments_length_in_cm >= 1000 THEN '1000+'
				ELSE 'unknown'
			END as bucket,
			COUNT(*) as count
		FROM dna_matches
		WHERE total_shared_segments_length_in_cm >= 0
		GROUP BY bucket
		ORDER BY
			CASE bucket
				WHEN '0-10' THEN 1
				WHEN '10-20' THEN 2
				WHEN '20-50' THEN 3
				WHEN '50-100' THEN 4
				WHEN '100-200' THEN 5
				WHEN '200-500' THEN 6
				WHEN '500-1000' THEN 7
				WHEN '1000+' THEN 8
				ELSE 9
			END
	`

	var distributionResults []struct {
		Bucket string
		Count  int64
	}

	if err := h.db.WithContext(ctx).Raw(distributionQuery).Scan(&distributionResults).Error; err != nil {
		h.logger.WithError(err).Error("Failed to get cM distribution")
		// Don't fail the entire request, just log the error
		stats.CMDistribution = []CMDistributionBucket{}
	} else {
		// Map bucket names to actual ranges
		bucketMap := map[string]CMDistributionBucket{
			"0-10":     {MinCM: 0, MaxCM: 10},
			"10-20":    {MinCM: 10, MaxCM: 20},
			"20-50":    {MinCM: 20, MaxCM: 50},
			"50-100":   {MinCM: 50, MaxCM: 100},
			"100-200":  {MinCM: 100, MaxCM: 200},
			"200-500":  {MinCM: 200, MaxCM: 500},
			"500-1000": {MinCM: 500, MaxCM: 1000},
			"1000+":    {MinCM: 1000, MaxCM: 10000},
		}

		stats.CMDistribution = make([]CMDistributionBucket, 0, len(distributionResults))
		for _, result := range distributionResults {
			if bucket, exists := bucketMap[result.Bucket]; exists {
				bucket.Count = result.Count
				stats.CMDistribution = append(stats.CMDistribution, bucket)
			}
		}
	}

	h.logger.WithFields(log.Fields{
		"total_matches": stats.TotalMatches,
		"avg_cm":        stats.AvgSharedCM,
		"max_cm":        stats.MaxSharedCM,
		"min_cm":        stats.MinSharedCM,
	}).Info("DNA match statistics retrieved")

	c.JSON(http.StatusOK, stats)
}

// GetTopCountries returns the top countries by DNA matches
func (h *AnalyticsHandler) GetTopCountries(c *gin.Context) {
	h.logger.Info("Getting top countries statistics")

	var results []struct {
		Country string
		Count   int64
	}

	// Get top countries from trees table (site_creator_country)
	if err := h.db.Table("trees").
		Select("site_creator_country as country, COUNT(*) as count").
		Where("site_creator_country IS NOT NULL AND site_creator_country != ''").
		Group("site_creator_country").
		Order("count DESC").
		Limit(20).
		Scan(&results).Error; err != nil {
		h.logger.WithError(err).Error("Failed to get top countries")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get top countries"})
		return
	}

	var response TopCountriesResponse
	response.Countries = make([]CountryStats, len(results))
	var total int64

	for i, result := range results {
		response.Countries[i] = CountryStats{
			Country: result.Country,
			Count:   result.Count,
		}
		total += result.Count
	}
	response.Total = total

	h.logger.WithField("countries_count", len(response.Countries)).Info("Top countries statistics retrieved")

	c.JSON(http.StatusOK, response)
}

// GetIndividualStats returns individual demographics statistics
func (h *AnalyticsHandler) GetIndividualStats(c *gin.Context) {
	h.logger.Info("Getting individual statistics")

	var stats IndividualStatsResponse

	// Get total count
	if err := h.db.Model(&database.Individual{}).Count(&stats.TotalIndividuals).Error; err != nil {
		h.logger.WithError(err).Error("Failed to count individuals")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get individual count"})
		return
	}

	// Get gender distribution
	var genderResults []struct {
		Gender string
		Count  int64
	}

	if err := h.db.Model(&database.Individual{}).
		Select("gender, COUNT(*) as count").
		Group("gender").
		Scan(&genderResults).Error; err != nil {
		h.logger.WithError(err).Error("Failed to get gender distribution")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get gender distribution"})
		return
	}

	stats.GenderDistribution = make(map[string]int64)
	for _, result := range genderResults {
		if result.Gender == "" {
			stats.GenderDistribution["unknown"] = result.Count
		} else {
			stats.GenderDistribution[result.Gender] = result.Count
		}
	}

	// Get age group distribution
	var ageResults []struct {
		AgeGroup string
		Count    int64
	}

	if err := h.db.Model(&database.Individual{}).
		Select("age_group, COUNT(*) as count").
		Group("age_group").
		Scan(&ageResults).Error; err != nil {
		h.logger.WithError(err).Error("Failed to get age group distribution")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get age group distribution"})
		return
	}

	stats.AgeGroupDistribution = make(map[string]int64)
	for _, result := range ageResults {
		if result.AgeGroup == "" {
			stats.AgeGroupDistribution["unknown"] = result.Count
		} else {
			stats.AgeGroupDistribution[result.AgeGroup] = result.Count
		}
	}

	// Count individuals with trees
	if err := h.db.Model(&database.Individual{}).
		Where("tree_id IS NOT NULL AND tree_id != ''").
		Count(&stats.IndividualsWithTrees).Error; err != nil {
		h.logger.WithError(err).Error("Failed to count individuals with trees")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to count individuals with trees"})
		return
	}

	h.logger.WithFields(log.Fields{
		"total_individuals":      stats.TotalIndividuals,
		"individuals_with_trees": stats.IndividualsWithTrees,
	}).Info("Individual statistics retrieved")

	c.JSON(http.StatusOK, stats)
}

// GetNetworkGraph returns network graph data for visualization
func (h *AnalyticsHandler) GetNetworkGraph(c *gin.Context) {
	h.logger.Info("Getting network graph data")

	// Parse query parameters
	depthStr := c.DefaultQuery("depth", "2")
	minCMStr := c.DefaultQuery("min_cm", "20")
	limitStr := c.DefaultQuery("limit", "1000")

	depth, err := strconv.Atoi(depthStr)
	if err != nil || depth < 1 || depth > 5 {
		depth = 2
	}

	minCM, err := strconv.ParseFloat(minCMStr, 64)
	if err != nil || minCM < 0 {
		minCM = 20
	}

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit < 1 || limit > 5000 {
		limit = 1000
	}

	// Get network data with filtering
	var networkData []struct {
		SourceID     string  `json:"source_id"`
		TargetID     string  `json:"target_id"`
		SharedCM     float64 `json:"shared_cm"`
		Relationship string  `json:"relationship"`
	}

	query := `
		SELECT
			source_individual_id as source_id,
			match_individual_id as target_id,
			total_shared_segments_length_in_cm as shared_cm,
			COALESCE(exact_dna_relationship, 'unknown') as relationship
		FROM dna_matches
		WHERE total_shared_segments_length_in_cm >= ?
		ORDER BY total_shared_segments_length_in_cm DESC
		LIMIT ?
	`

	if err := h.db.Raw(query, minCM, limit).Scan(&networkData).Error; err != nil {
		h.logger.WithError(err).Error("Failed to get network graph data")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get network graph data"})
		return
	}

	// Build nodes and edges for visualization
	nodeMap := make(map[string]bool)
	var nodes []NetworkNode
	var edges []NetworkEdge

	for _, match := range networkData {
		// Add nodes if not already present
		if !nodeMap[match.SourceID] {
			nodes = append(nodes, NetworkNode{
				ID:    match.SourceID,
				Label: match.SourceID,
				Type:  "individual",
			})
			nodeMap[match.SourceID] = true
		}

		if !nodeMap[match.TargetID] {
			nodes = append(nodes, NetworkNode{
				ID:    match.TargetID,
				Label: match.TargetID,
				Type:  "individual",
			})
			nodeMap[match.TargetID] = true
		}

		// Add edge
		edges = append(edges, NetworkEdge{
			Source:       match.SourceID,
			Target:       match.TargetID,
			Weight:       match.SharedCM,
			Relationship: match.Relationship,
		})
	}

	response := NetworkGraphResponse{
		Nodes: nodes,
		Edges: edges,
		Stats: NetworkStats{
			NodeCount: len(nodes),
			EdgeCount: len(edges),
			MinCM:     minCM,
			Depth:     depth,
		},
	}

	h.logger.WithFields(log.Fields{
		"nodes":  len(nodes),
		"edges":  len(edges),
		"min_cm": minCM,
		"depth":  depth,
	}).Info("Network graph data retrieved")

	c.JSON(http.StatusOK, response)
}

// GetNetworkCliques returns network clique analysis data
func (h *AnalyticsHandler) GetNetworkCliques(c *gin.Context) {
	h.logger.Info("Getting network cliques data")

	// Parse query parameters
	minSizeStr := c.DefaultQuery("min_size", "3")
	minCMStr := c.DefaultQuery("min_cm", "50")
	limitStr := c.DefaultQuery("limit", "100")

	minSize, err := strconv.Atoi(minSizeStr)
	if err != nil || minSize < 2 || minSize > 20 {
		minSize = 3
	}

	minCM, err := strconv.ParseFloat(minCMStr, 64)
	if err != nil || minCM < 0 {
		minCM = 50
	}

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit < 1 || limit > 500 {
		limit = 100
	}

	// For now, return a simplified clique analysis
	// In a full implementation, this would use graph algorithms to find actual cliques
	var cliqueData []struct {
		PersonID string  `json:"person_id"`
		SharedCM float64 `json:"shared_cm"`
		Count    int64   `json:"count"`
	}

	query := `
		SELECT
			source_individual_id as person_id,
			AVG(total_shared_segments_length_in_cm) as shared_cm,
			COUNT(*) as count
		FROM dna_matches
		WHERE total_shared_segments_length_in_cm >= ?
		GROUP BY source_individual_id
		HAVING COUNT(*) >= ?
		ORDER BY count DESC, shared_cm DESC
		LIMIT ?
	`

	if err := h.db.Raw(query, minCM, minSize, limit).Scan(&cliqueData).Error; err != nil {
		h.logger.WithError(err).Error("Failed to get clique data")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get clique data"})
		return
	}

	// Build cliques response
	cliques := make([]NetworkClique, 0)
	var totalSize int
	var maxSize int
	var minSizeFound int

	for i, data := range cliqueData {
		size := int(data.Count)
		clique := NetworkClique{
			ID:      i + 1,
			Members: []string{data.PersonID}, // Simplified - would include all clique members
			Size:    size,
			AvgCM:   data.SharedCM,
		}
		cliques = append(cliques, clique)

		totalSize += size
		if size > maxSize {
			maxSize = size
		}
		if i == 0 || size < minSizeFound {
			minSizeFound = size
		}
	}

	avgSize := 0.0
	if len(cliques) > 0 {
		avgSize = float64(totalSize) / float64(len(cliques))
	}

	// Initialize stats properly when no cliques found
	if len(cliques) == 0 {
		minSizeFound = 0
	}

	response := NetworkCliquesResponse{
		Cliques: cliques,
		Stats: CliqueStats{
			TotalCliques: len(cliques),
			AvgSize:      avgSize,
			MaxSize:      maxSize,
			MinSize:      minSizeFound,
		},
	}

	h.logger.WithFields(log.Fields{
		"cliques":  len(cliques),
		"min_size": minSize,
		"min_cm":   minCM,
	}).Info("Network cliques data retrieved")

	c.JSON(http.StatusOK, response)
}

// GetMatches returns filtered DNA matches with pagination
func (h *AnalyticsHandler) GetMatches(c *gin.Context) {
	h.logger.Info("Getting filtered DNA matches")

	// Parse query parameters
	pageStr := c.DefaultQuery("page", "1")
	limitStr := c.DefaultQuery("limit", "100")
	minCMStr := c.DefaultQuery("min_cm", "0")
	maxCMStr := c.DefaultQuery("max_cm", "10000")
	relationship := c.Query("relationship")
	confidence := c.Query("confidence")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid page parameter. Must be a positive integer."})
		return
	}

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit < 1 || limit > 1000 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid limit parameter. Must be between 1 and 1000."})
		return
	}

	minCM, err := strconv.ParseFloat(minCMStr, 64)
	if err != nil || minCM < 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid min_cm parameter. Must be a non-negative number."})
		return
	}

	maxCM, err := strconv.ParseFloat(maxCMStr, 64)
	if err != nil || maxCM < 0 {
		maxCM = 10000
	}

	offset := (page - 1) * limit

	// Build query with filters
	query := h.db.Model(&database.DNAMatch{}).
		Where("total_shared_segments_length_in_cm >= ? AND total_shared_segments_length_in_cm <= ?", minCM, maxCM)

	if relationship != "" {
		query = query.Where("exact_dna_relationship = ?", relationship)
	}

	if confidence != "" {
		query = query.Where("confidence_level = ?", confidence)
	}

	// Get total count
	var total int64
	if err := query.Count(&total).Error; err != nil {
		h.logger.WithError(err).Error("Failed to count matches")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to count matches"})
		return
	}

	// Get matches with pagination
	var matches []database.DNAMatch
	if err := query.Offset(offset).Limit(limit).Find(&matches).Error; err != nil {
		h.logger.WithError(err).Error("Failed to get matches")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get matches"})
		return
	}

	// Convert to response format
	matchData := make([]MatchData, 0)
	for _, match := range matches {
		matchData = append(matchData, MatchData{
			ID:           match.ID,
			Person1ID:    match.SourceIndividualID,
			Person2ID:    match.MatchIndividualID,
			SharedCM:     match.TotalSharedSegmentsLengthInCm,
			Relationship: match.ExactDnaRelationship,
			Confidence:   match.ConfidenceLevel,
		})
	}

	response := MatchesResponse{
		Matches: matchData,
		Total:   total,
		Page:    page,
		Limit:   limit,
	}

	h.logger.WithFields(log.Fields{
		"matches": len(matchData),
		"total":   total,
		"page":    page,
		"limit":   limit,
	}).Info("Filtered DNA matches retrieved")

	c.JSON(http.StatusOK, response)
}

// GetIndividuals returns filtered individuals with pagination
func (h *AnalyticsHandler) GetIndividuals(c *gin.Context) {
	h.logger.Info("Getting filtered individuals")

	// Parse query parameters
	pageStr := c.DefaultQuery("page", "1")
	limitStr := c.DefaultQuery("limit", "100")
	gender := c.Query("gender")
	ageGroup := c.Query("age_group")
	birthPlace := c.Query("birth_place")
	hasTree := c.Query("has_tree")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid page parameter. Must be a positive integer."})
		return
	}

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit < 1 || limit > 1000 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid limit parameter. Must be between 1 and 1000."})
		return
	}

	offset := (page - 1) * limit

	// Build query with filters
	query := h.db.Model(&database.Individual{})

	if gender != "" {
		query = query.Where("gender = ?", gender)
	}

	if ageGroup != "" {
		query = query.Where("age_group = ?", ageGroup)
	}

	if birthPlace != "" {
		query = query.Where("birth_place LIKE ?", "%"+birthPlace+"%")
	}

	if hasTree == "true" {
		query = query.Where("tree_id IS NOT NULL AND tree_id != ''")
	} else if hasTree == "false" {
		query = query.Where("tree_id IS NULL OR tree_id = ''")
	}

	// Get total count
	var total int64
	if err := query.Count(&total).Error; err != nil {
		h.logger.WithError(err).Error("Failed to count individuals")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to count individuals"})
		return
	}

	// Get individuals with pagination
	var individuals []database.Individual
	if err := query.Offset(offset).Limit(limit).Find(&individuals).Error; err != nil {
		h.logger.WithError(err).Error("Failed to get individuals")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get individuals"})
		return
	}

	// Convert to response format
	individualData := make([]IndividualData, 0)
	for _, individual := range individuals {
		individualData = append(individualData, IndividualData{
			ID:       individual.ID,
			TreeID:   individual.TreeID,
			Gender:   individual.Gender,
			AgeGroup: individual.AgeGroup,
			Country:  individual.BirthPlace, // Using BirthPlace as Country
		})
	}

	response := IndividualsResponse{
		Individuals: individualData,
		Total:       total,
		Page:        page,
		Limit:       limit,
	}

	h.logger.WithFields(log.Fields{
		"individuals": len(individualData),
		"total":       total,
		"page":        page,
		"limit":       limit,
	}).Info("Filtered individuals retrieved")

	c.JSON(http.StatusOK, response)
}

// GetSegments returns filtered DNA segments with pagination
func (h *AnalyticsHandler) GetSegments(c *gin.Context) {
	h.logger.Info("Getting filtered DNA segments")

	// Parse query parameters
	pageStr := c.DefaultQuery("page", "1")
	limitStr := c.DefaultQuery("limit", "100")
	matchID := c.Query("match_id")
	chromosomeStr := c.Query("chromosome")
	minLengthStr := c.DefaultQuery("min_length", "0")
	maxLengthStr := c.DefaultQuery("max_length", "1000")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid page parameter. Must be a positive integer."})
		return
	}

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit < 1 || limit > 1000 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid limit parameter. Must be between 1 and 1000."})
		return
	}

	minLength, err := strconv.ParseFloat(minLengthStr, 64)
	if err != nil || minLength < 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid min_cm parameter. Must be a non-negative number."})
		return
	}

	maxLength, err := strconv.ParseFloat(maxLengthStr, 64)
	if err != nil || maxLength < 0 {
		maxLength = 1000
	}

	offset := (page - 1) * limit

	// Build query with filters
	query := h.db.Model(&database.SharedSegment{}).
		Where("length_in_centimorgans >= ? AND length_in_centimorgans <= ?", minLength, maxLength)

	if matchID != "" {
		query = query.Where("match_id = ?", matchID)
	}

	if chromosomeStr != "" {
		chromosome, err := strconv.Atoi(chromosomeStr)
		if err != nil || chromosome < 1 || chromosome > 23 {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid chromosome parameter. Must be between 1 and 23."})
			return
		}
		query = query.Where("chromosome_id = ?", chromosome)
	}

	// Get total count
	var total int64
	if err := query.Count(&total).Error; err != nil {
		h.logger.WithError(err).Error("Failed to count segments")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to count segments"})
		return
	}

	// Get segments with pagination
	var segments []database.SharedSegment
	if err := query.Offset(offset).Limit(limit).Find(&segments).Error; err != nil {
		h.logger.WithError(err).Error("Failed to get segments")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get segments"})
		return
	}

	// Convert to response format
	segmentData := make([]SegmentData, 0)
	for _, segment := range segments {
		segmentData = append(segmentData, SegmentData{
			ID:         strconv.Itoa(segment.ID),
			MatchID:    segment.MatchID,
			Chromosome: strconv.Itoa(segment.ChromosomeID),
			StartPos:   int64(segment.StartPosition),
			EndPos:     int64(segment.EndPosition),
			Length:     segment.LengthInCentimorgans,
		})
	}

	response := SegmentsResponse{
		Segments: segmentData,
		Total:    total,
		Page:     page,
		Limit:    limit,
	}

	h.logger.WithFields(log.Fields{
		"segments": len(segmentData),
		"total":    total,
		"page":     page,
		"limit":    limit,
	}).Info("Filtered DNA segments retrieved")

	c.JSON(http.StatusOK, response)
}
