package sharedmatches

import (
	"fmt"
	"time"

	"github.com/dmagur/myheritage/internal/database"
)

type Repository struct {
	db *database.GormDB
}

type Result struct {
	ID                string
	MatchIndividualID string
}

func NewRepository(db *database.GormDB) *Repository {
	return &Repository{db}
}

func (r *Repository) GetDnaMatchesWithoutSharedMatchesCount(offset int, limit int) ([]Result, error) {
	// Slice to hold the results
	var dnaMatches []Result

	// Execute the query using GORM's query builder instead of raw SQL
	result := r.db.DB.Table("dna_matches").
		Select("id, match_individual_id").
		Where("link is not null and shared_matches_count is null").
		Order("total_shared_segments_length_in_cm desc").
		Offset(offset).
		Limit(limit).
		Scan(&dnaMatches)

	if result.Error != nil {
		return nil, result.Error
	}

	return dnaMatches, nil
}

func (r *Repository) UpsertDnaMatch(match DnaSharedMatchDetails, sourceIndividualID string) error {
	currentTime := time.Now()

	// Handle the ExactDnaRelationship field which can be nil
	var exactDnaRelationship string
	if match.OtherDnaMatch.ExactDnaRelationship != nil {
		exactDnaRelationship = *match.OtherDnaMatch.ExactDnaRelationship
	} else {
		exactDnaRelationship = "" // Default to empty string if nil
	}

	// Create a new DNA match record
	dnaMatch := database.DNAMatch{
		ID:                            match.OtherDnaMatch.ID,
		SourceIndividualID:            sourceIndividualID,
		TotalSharedSegmentsLengthInCm: match.OtherDnaMatch.TotalSharedSegmentsLengthInCm,
		PercentageOfSharedSegments:    match.OtherDnaMatch.PercentageOfSharedSegments,
		ExactDnaRelationship:          exactDnaRelationship,
		CreatedAt:                     currentTime.Format("2006-01-02 15:04:05"),
		MatchIndividualID:             match.SharedIndividual.ID,
	}

	// Try to find an existing record
	var existingMatch database.DNAMatch
	result := r.db.DB.Where("id = ?", match.OtherDnaMatch.ID).First(&existingMatch)

	// If the record exists, update it
	if result.Error == nil {
		r.db.DB.Model(&existingMatch).Updates(dnaMatch)
	} else {
		// If the record doesn't exist, create a new one
		result = r.db.DB.Create(&dnaMatch)
		if result.Error != nil {
			return fmt.Errorf("failed to upsert dna match: %v", result.Error)
		}
	}

	return nil
}

func (r *Repository) updateSharedMatchesCount(matchId string, count int) error {
	currentTime := time.Now()

	// Update the record using GORM
	result := r.db.DB.Model(&database.DNAMatch{}).
		Where("id = ?", matchId).
		Updates(map[string]interface{}{
			"shared_matches_count": count,
			"updated_at":           currentTime.Format("2006-01-02 15:04:05"),
		})

	if result.Error != nil {
		return result.Error
	}

	return nil
}
