package api

import (
	"context"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/dmagur/myheritage/apigen"
	"github.com/dmagur/myheritage/internal/repository"
	"github.com/gin-gonic/gin"
	log "github.com/sirupsen/logrus"
)

// MockRepository is a mock implementation of the repository.Interface
type MockRepository struct {
	FindAllFunc func(ctx context.Context, offset int, limit int) (*repository.MultiResult[*apigen.DnaMatch], error)
}

func (m *MockRepository) FindAll(ctx context.Context, offset int, limit int) (*repository.MultiResult[*apigen.DnaMatch], error) {
	return m.FindAllFunc(ctx, offset, limit)
}

func TestAPI_GetMatches_Success(t *testing.T) {
	// Create a mock repository
	mockRepository := &MockRepository{
		FindAllFunc: func(ctx context.Context, offset int, limit int) (*repository.MultiResult[*apigen.DnaMatch], error) {
			return &repository.MultiResult[*apigen.DnaMatch]{
				Count: 1,
				Models: []*apigen.DnaMatch{
					{
						Id:                 "dnamatch-123",
						SharedLength:       10.5,
						MatchName:          "Test Match",
						MatchIndividualId:  "individual-123",
						SourceName:         "Test Source",
						SourceIndividualId: "individual-456",
					},
				},
			}, nil
		},
	}

	// Create a logger
	logger := log.New()

	// Create an API
	api := NewAPI(logger, mockRepository)

	// Create a Gin router
	router := gin.Default()
	// Register the API handler
	router.GET("/dna-matches", api.GetMatches)

	// Create a test request
	req, _ := http.NewRequest("GET", "/dna-matches", nil)
	// Create a test response recorder
	w := httptest.NewRecorder()

	// Serve the request
	router.ServeHTTP(w, req)

	// Check the response status code
	if w.Code != http.StatusOK {
		t.Errorf("Expected status code %d, got %d", http.StatusOK, w.Code)
	}
}

func TestAPI_GetMatches_Error(t *testing.T) {
	// Create a mock repository that returns an error
	mockRepository := &MockRepository{
		FindAllFunc: func(ctx context.Context, offset int, limit int) (*repository.MultiResult[*apigen.DnaMatch], error) {
			return nil, errors.New("repository error")
		},
	}

	// Create a logger
	logger := log.New()

	// Create an API
	api := NewAPI(logger, mockRepository)

	// Create a Gin router with a custom error handler
	router := gin.Default()
	// Register the API handler
	router.GET("/dna-matches", func(c *gin.Context) {
		// This will panic due to the repository error
		defer func() {
			if r := recover(); r == nil {
				t.Error("Expected a panic, but none occurred")
			}
		}()
		api.GetMatches(c)
	})

	// Create a test request
	req, _ := http.NewRequest("GET", "/dna-matches", nil)
	// Create a test response recorder
	w := httptest.NewRecorder()

	// Serve the request
	router.ServeHTTP(w, req)
}
