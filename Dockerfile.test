# Test Dockerfile for running Go tests with all dependencies
FROM golang:1.23-alpine

# Install necessary packages
RUN apk add --no-cache \
    gcc \
    musl-dev \
    sqlite \
    sqlite-dev

# Set working directory
WORKDIR /app

# Copy go mod files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy source code
COPY . .

# Set CGO_ENABLED for SQLite
ENV CGO_ENABLED=1

# Default command to run tests
CMD ["go", "test", "./internal/api", "-v", "-run", "TestAnalyticsHandler"]
