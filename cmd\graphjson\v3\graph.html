<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D Force Graph</title>
    <style> body { margin: 0; } </style>
    <script src="3d-force-graph.min.js"></script>
</head>
<body>
<div id="3d-graph"></div>

<script>
    const gData = {
  "nodes": [
    {
      "id": "<PERSON><PERSON> (Eidmane)",
      "group": "individual-1184522591-1500001"
    },
    {
      "id": "<PERSON><PERSON>",
      "group": "individual-1283369742-1500001"
    },
    {
      "id": "Daria Ark",
      "group": "individual-1476405972-1500001"
    },
    {
      "id": "Morgan Young",
      "group": "individual-1630844652-1500001"
    },
    {
      "id": "<PERSON><PERSON>",
      "group": "individual-1643045858-1500001"
    },
    {
      "id": "<PERSON><PERSON><PERSON>",
      "group": "individual-1650276810-1500007"
    },
    {
      "id": "<PERSON>",
      "group": "individual-1655553354-1500001"
    },
    {
      "id": "Tuomas Korpi",
      "group": "individual-1659008146-1500001"
    },
    {
      "id": "Дмитрий Magur (geb. Магур)",
      "group": "individual-32361932-1000003"
    },
    {
      "id": "Franziska Kelber",
      "group": "individual-491353211-1500003"
    },
    {
      "id": "Admir Čimpo",
      "group": "individual-491553411-1500001"
    },
    {
      "id": "Дарья Герасимова",
      "group": "individual-561632651-1500012"
    },
    {
      "id": "Hena Mangin",
      "group": "individual-699610641-1500001"
    },
    {
      "id": "Saniye Sen",
      "group": "individual-704444431-1500004"
    },
    {
      "id": "Maria Irena Dziemianowska (geb. Markwart)",
      "group": "individual-77638363-6584452"
    },
    {
      "id": "Dominika Gryz",
      "group": "individual-854139891-1500001"
    },
    {
      "id": "Marko Lauri Johannes Ylilehto",
      "group": "individual-859759771-1500001"
    }
  ],
  "links": [
    {
      "source": "Marko Lauri Johannes Ylilehto",
      "target": "Kaarina Murtomäki",
      "value": 25
    },
    {
      "source": "Tuomas Korpi",
      "target": "Kaarina Murtomäki",
      "value": 36
    },
    {
      "source": "Marko Lauri Johannes Ylilehto",
      "target": "Tuomas Korpi",
      "value": 75
    },
    {
      "source": "Дмитрий Magur (geb. Магур)",
      "target": "Franziska Kelber",
      "value": 14
    },
    {
      "source": "Дмитрий Magur (geb. Магур)",
      "target": "Maira Kapare (Eidmane)",
      "value": 13
    },
    {
      "source": "Дмитрий Magur (geb. Магур)",
      "target": "Lasse Knudsen",
      "value": 26
    },
    {
      "source": "Дмитрий Magur (geb. Магур)",
      "target": "Admir Čimpo",
      "value": 23
    },
    {
      "source": "Дмитрий Magur (geb. Магур)",
      "target": "Marko Lauri Johannes Ylilehto",
      "value": 21
    },
    {
      "source": "Дмитрий Magur (geb. Магур)",
      "target": "Saniye Sen",
      "value": 21
    },
    {
      "source": "Дмитрий Magur (geb. Магур)",
      "target": "Дарья Герасимова",
      "value": 13
    },
    {
      "source": "Дмитрий Magur (geb. Магур)",
      "target": "Suzana Zemljič",
      "value": 8
    },
    {
      "source": "Дмитрий Magur (geb. Магур)",
      "target": "Dominika Gryz",
      "value": 9
    },
    {
      "source": "Дмитрий Magur (geb. Магур)",
      "target": "Hena Mangin",
      "value": 13
    },
    {
      "source": "Дмитрий Magur (geb. Магур)",
      "target": "Beatrice Gassmann",
      "value": 12
    },
    {
      "source": "Дмитрий Magur (geb. Магур)",
      "target": "Daria Ark",
      "value": 8
    },
    {
      "source": "Дмитрий Magur (geb. Магур)",
      "target": "Morgan Young",
      "value": 8
    },
    {
      "source": "Дмитрий Magur (geb. Магур)",
      "target": "Maria Irena Dziemianowska (geb. Markwart)",
      "value": 10
    },
    {
      "source": "Дмитрий Magur (geb. Магур)",
      "target": "Franziska Kelber",
      "value": 14
    },
    {
      "source": "Дмитрий Magur (geb. Магур)",
      "target": "Maira Kapare (Eidmane)",
      "value": 13
    },
    {
      "source": "Дмитрий Magur (geb. Магур)",
      "target": "Lasse Knudsen",
      "value": 26
    },
    {
      "source": "Дмитрий Magur (geb. Магур)",
      "target": "Admir Čimpo",
      "value": 23
    },
    {
      "source": "Дмитрий Magur (geb. Магур)",
      "target": "Marko Lauri Johannes Ylilehto",
      "value": 21
    },
    {
      "source": "Дмитрий Magur (geb. Магур)",
      "target": "Saniye Sen",
      "value": 21
    },
    {
      "source": "Marko Lauri Johannes Ylilehto",
      "target": "Tuomas Korpi",
      "value": 75
    },
    {
      "source": "Дмитрий Magur (geb. Магур)",
      "target": "Tuomas Korpi",
      "value": 9
    },
    {
      "source": "Tuomas Korpi",
      "target": "Kaarina Murtomäki",
      "value": 36
    },
    {
      "source": "Дмитрий Magur (geb. Магур)",
      "target": "Kaarina Murtomäki",
      "value": 8
    },
    {
      "source": "Marko Lauri Johannes Ylilehto",
      "target": "Kaarina Murtomäki",
      "value": 25
    }
  ]
};

    const Graph = ForceGraph3D()
    (document.getElementById('3d-graph'))
        .graphData(gData)
        .nodeLabel('id')
        .nodeAutoColorBy('group');
</script>
</body>
</html>