package database

import (
	"database/sql"
	"fmt"
)

// NewDB creates a new database connection based on the DB_TYPE environment variable
// This function is kept for backward compatibility with existing code that still uses raw SQL
// New code should use NewGormDB instead
func NewDB() *sql.DB {
	dbType := getEnv("DB_TYPE", "mariadb")

	switch dbType {
	case "mariadb", "mysql":
		return NewMariaDB()
	case "postgres", "postgresql":
		connStr := ConnString(getPostgresConnString())
		return NewPgSql(connStr)
	default:
		// Default to MariaDB
		return NewMariaDB()
	}
}

// getPostgresConnString returns the PostgreSQL connection string from environment variables or a default value
func getPostgresConnString() string {
	host := getEnv("DB_HOST", "my_postgres_db")
	user := getEnv("DB_USER", "postgres")
	password := getEnv("DB_PASSWORD", "mysecretpassword")
	dbname := getEnv("DB_NAME", "dna_match_db")

	return "host=" + host + " user=" + user + " password=" + password + " dbname=" + dbname + " sslmode=disable"
}

// GetConnectionString returns a connection string for the specified database type
// This is a helper function for creating GORM connections
func GetConnectionString() ConnString {
	dbType := getEnv("DB_TYPE", "postgres")

	switch dbType {
	case "mariadb", "mysql":
		host := getEnv("DB_HOST", "localhost")
		user := getEnv("DB_USER", "myheritage")
		password := getEnv("DB_PASSWORD", "mysecretpassword")
		dbname := getEnv("DB_NAME", "dna_match_db")
		return ConnString(fmt.Sprintf("%s:%s@tcp(%s:3306)/%s", user, password, host, dbname))
	case "postgres", "postgresql":
		return ConnString(getPostgresConnString())
	default:
		// Default to PostgreSQL
		return ConnString(getPostgresConnString())
	}
}
