# PowerShell script to fix boolean values in SQL files

# Parameters
param (
    [Parameter(Mandatory=$true)]
    [string]$InputFilePath,
    
    [Parameter(Mandatory=$false)]
    [string]$OutputFilePath = ""
)

# If output file path is not specified, create one based on the input file
if ($OutputFilePath -eq "") {
    $OutputFilePath = [System.IO.Path]::ChangeExtension($InputFilePath, "fixed.sql")
}

# Check if the input file exists
if (-not (Test-Path $InputFilePath)) {
    Write-Host "Error: Input file not found: $InputFilePath" -ForegroundColor Red
    exit
}

$fileSize = (Get-Item $InputFilePath).Length
Write-Host "Input file found. File size: $fileSize bytes"
Write-Host "Output will be written to: $OutputFilePath"

# Process the file in streaming mode to handle large files
$reader = [System.IO.File]::OpenText($InputFilePath)
$writer = [System.IO.File]::CreateText($OutputFilePath)

try {
    $lineCount = 0
    $replacementCount = 0
    
    # Process the file line by line
    while (($line = $reader.ReadLine()) -ne $null) {
        $lineCount++
        
        # Show progress every 100,000 lines
        if ($lineCount % 100000 -eq 0) {
            Write-Host "Processed $lineCount lines, replaced $replacementCount occurrences..."
        }
        
        # Replace 't' with 1 and 'f' with 0 for boolean values
        $newLine = $line
        
        # Count occurrences before replacement
        $tCount = ([regex]::Matches($line, "'t'")).Count
        $fCount = ([regex]::Matches($line, "'f'")).Count
        
        # Replace the values
        $newLine = $newLine -replace "'t'", "1"
        $newLine = $newLine -replace "'f'", "0"
        
        # Update replacement count
        $replacementCount += $tCount + $fCount
        
        # Write the line to the output file
        $writer.WriteLine($newLine)
    }
    
    Write-Host "Replacement completed! Processed $lineCount lines, replaced $replacementCount occurrences."
    Write-Host "The fixed SQL file is available at: $OutputFilePath"
} finally {
    $reader.Close()
    $writer.Close()
}
