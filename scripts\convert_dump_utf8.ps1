# PowerShell script to convert PostgreSQL dump to SQL INSERT statements with proper UTF-8 encoding

# Parameters
param (
    [Parameter(Mandatory=$false)]
    [string]$DumpFilePath = "data\trees-dump.sql",
    
    [Parameter(Mandatory=$false)]
    [string]$OutputFilePath = "data\trees-insert.sql"
)

# Check if the dump file exists
if (-not (Test-Path $DumpFilePath)) {
    Write-Host "Error: Dump file not found: $DumpFilePath" -ForegroundColor Red
    exit
}

$fileSize = (Get-Item $DumpFilePath).Length
Write-Host "Dump file found. File size: $fileSize bytes"

# Read the content of the dump file with UTF-8 encoding
$content = [System.IO.File]::ReadAllText($DumpFilePath, [System.Text.Encoding]::UTF8)

# Extract the table name and columns
$copyMatch = [regex]::Match($content, "COPY public\.(\w+) \((.*?)\) FROM stdin;")
if (-not $copyMatch.Success) {
    Write-Host "Error: Could not find COPY statement in the dump file." -ForegroundColor Red
    exit
}

$tableName = $copyMatch.Groups[1].Value
$columns = $copyMatch.Groups[2].Value
Write-Host "Found table: $tableName with columns: $columns"

# Extract the data section
$dataMatch = [regex]::Match($content, "COPY public\.$tableName.*?FROM stdin;\r?\n(.*?)\\\.", [System.Text.RegularExpressions.RegexOptions]::Singleline)
if (-not $dataMatch.Success) {
    Write-Host "Error: Could not extract data section from the dump file." -ForegroundColor Red
    exit
}

$dataSection = $dataMatch.Groups[1].Value
$dataRows = $dataSection -split "\r?\n" | Where-Object { $_ -ne "" }
Write-Host "Extracted $($dataRows.Count) data rows"

# Create INSERT statements
$insertStatements = New-Object System.Text.StringBuilder
$insertStatements.AppendLine("SET NAMES utf8mb4;") | Out-Null
$insertStatements.AppendLine("SET FOREIGN_KEY_CHECKS=0;") | Out-Null

foreach ($row in $dataRows) {
    # Split the row by tabs
    $values = $row -split "`t"
    
    # Escape single quotes in values
    $escapedValues = @()
    foreach ($value in $values) {
        if ($value -eq "\N") {
            $escapedValues += "NULL"
        } else {
            $escapedValues += "'" + ($value -replace "'", "''") + "'"
        }
    }
    
    # Create the INSERT statement
    $insertStatement = "INSERT INTO $tableName ($columns) VALUES (" + ($escapedValues -join ", ") + ");"
    $insertStatements.AppendLine($insertStatement) | Out-Null
}

$insertStatements.AppendLine("SET FOREIGN_KEY_CHECKS=1;") | Out-Null

# Write the INSERT statements to the output file with UTF-8 encoding
[System.IO.File]::WriteAllText($OutputFilePath, $insertStatements.ToString(), [System.Text.Encoding]::UTF8)

Write-Host "Conversion completed! The SQL file is available at: $OutputFilePath"
Write-Host "Generated $($dataRows.Count) INSERT statements"
