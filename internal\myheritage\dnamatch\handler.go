package dnamatch

import (
	"encoding/json"
	"fmt"

	"github.com/dmagur/myheritage/internal/fetch"
	log "github.com/sirupsen/logrus"
)

type HandlerInterface interface {
	Handle() error
}

type Handler struct {
	client     *fetch.MyHeritageApiClient
	logger     *log.Logger
	repository *Repository
}

type ApiResponse struct {
	Data struct {
		DnaKit struct {
			DnaMatches struct {
				Data []DNAMatch `json:"data"`
			} `json:"dna_matches"`
		} `json:"dna_kit"`
	} `json:"data"`
}

func NewHandler(
	client *fetch.MyHeritageApiClient,
	logger *log.Logger,
	repository *Repository,
) Handler {
	return Handler{
		client:     client,
		logger:     logger,
		repository: repository,
	}
}

func (h Handler) Handle() error {
	individuals := map[string]string{
		"individual-32361932-1000002": "dnakit-455CACAF-4DA8-4BA1-B8C7-AA0C0053EEFA",
		"individual-32361932-1000003": "dnakit-79557E8D-FF6E-4C36-A764-BD01534FEDA2",
		"individual-32361932-1000006": "dnakit-A04C42E1-1714-48FA-A781-EBF3F0960FEB",
		"individual-32361932-1000007": "dnakit-9E32173E-786B-4458-B737-F50B3E8B1CC2",
	}

	for individualID, dnaKitID := range individuals {
		fmt.Printf("Individual ID: %s\n", individualID)
		limit := 50
		for offset := 0; offset < 10000; offset += limit {
			fmt.Printf("Offset: %d\n", offset)
			matches, err := h.GetMatches(dnaKitID, offset, limit)

			if err != nil {
				return fmt.Errorf("could not fetch dna matches: %w", err)
			}

			if len(matches) < 1 {
				break
			}

			result, err := h.repository.saveDnaMatches(matches, individualID)
			if err != nil {
				fmt.Println("Error saving DNA matches:", err)
			}

			if result < 0 {
				break
			}
		}
	}

	return nil
}

func (h Handler) GetMatches(dnaKitID string, offset int, limit int) ([]DNAMatch, error) {
	h.logger.Info("Downloading dna matches")

	url := "https://familygraphql.myheritage.com/fetch_dna_matches_for_kit/"
	lang := "DE"
	sortQuery := "creation_time"
	queryText := ""

	query := fmt.Sprintf(`"{dna_kit(id:\"%s\",lang:\"%s\"){dna_matches(offset:%d,limit:%d,sort_query:\"%s\",query:\"%s\",filter:\"0\",filter_by_relationship:\"0\",filter_by_country:\"0\",filter_by_labels:\"0\"){data{id link is_new complete_dna_relationships{relationship_type relationship_degree}refined_dna_relationships{relationship_type relationship_degree}dna_cm_explainer{relationships{...dna_match_relationship}most_probable_relationships{...dna_match_relationship}calculation_strategy}exact_dna_relationship genealogical_relationship total_shared_segments_length_in_cm largest_shared_segment_length_in_cm percentage_of_shared_segments total_shared_segments is_recently_recalculated confidence_level other_dna_kit{submitter{id name name_transliterated first_name first_name_transliterated link is_public}member{id first_name first_name_transliterated name name_transliterated gender age_group age_group_in_years personal_photo{...PERSONAL_PHOTO_INFO}country_code country is_public link}associated_individual{id first_name first_name_transliterated name name_transliterated gender age_group age_group_in_years personal_photo{...PERSONAL_PHOTO_INFO}birth_place tree{...tree_info}relationship{...RELATIONSHIP_INFO}link_in_pedigree_tree link_in_tree}}}}}}fragment dna_match_relationship on DnaMatchRelationship{relationship_type relationship_class path_type probability most_recent_common_ancestor_relationship_type most_recent_common_ancestor_relationship_class}fragment tree_info on Tree{id name link individual_count site{is_request_membership_allowed creator{id name name_transliterated country country_code link is_public}}}fragment PERSONAL_PHOTO_INFO on Photo{thumbnails(thumbnail_size:\"96x96\"){url}}fragment RELATIONSHIP_INFO on Relationship{relationship_description}"`,
		dnaKitID, lang, offset, limit, sortQuery, queryText)

	query = fmt.Sprintf(`"{dna_kit(id:\"%s\",lang:\"%s\"){dna_matches(offset:%d,limit:%d,sort_query:\"%s\",query:\"%s\",filter:\"0\",filter_by_relationship:\"0\",filter_by_country:\"0\",filter_by_labels:\"0\"){data{id link is_new complete_dna_relationships{relationship_type relationship_degree}refined_dna_relationships{relationship_type relationship_degree}dna_cm_explainer{relationships{...dna_match_relationship}most_probable_relationships{...dna_match_relationship}calculation_strategy}exact_dna_relationship genealogical_relationship total_shared_segments_length_in_cm largest_shared_segment_length_in_cm percentage_of_shared_segments total_shared_segments is_recently_recalculated confidence_level other_dna_kit{submitter{id name name_transliterated first_name first_name_transliterated link is_public}member{id first_name first_name_transliterated name name_transliterated gender age_group age_group_in_years personal_photo{...PERSONAL_PHOTO_INFO}country_code country is_public link}associated_individual{id first_name first_name_transliterated name name_transliterated gender age_group age_group_in_years personal_photo{...PERSONAL_PHOTO_INFO}birth_place tree{...tree_info}relationship{...RELATIONSHIP_INFO}link_in_pedigree_tree link_in_tree}}}}}}fragment dna_match_relationship on DnaMatchRelationship{relationship_type relationship_class path_type probability most_recent_common_ancestor_relationship_type most_recent_common_ancestor_relationship_class}fragment tree_info on Tree{id name link individual_count site{is_request_membership_allowed creator{id name name_transliterated country country_code link is_public}}}fragment PERSONAL_PHOTO_INFO on Photo{thumbnails(thumbnail_size:\"96x96\"){url}}fragment RELATIONSHIP_INFO on Relationship{relationship_description}"`,
		dnaKitID, lang, offset, limit, sortQuery, queryText)

	body, err := h.client.Fetch(url, query, "FETCH_DNA_MATCHES_FOR_KIT")
	if err != nil {
		return nil, fmt.Errorf("could not fetch matches: %w", err)
	}

	var apiResponse ApiResponse
	err = json.Unmarshal(body, &apiResponse)
	if err != nil {
		// Print the response body if JSON unmarshalling fails
		fmt.Println("Error unmarshalling JSON response:", err)
		fmt.Printf("Response body: %s\n", string(body))
		return nil, err
	}

	matches := apiResponse.Data.DnaKit.DnaMatches.Data

	h.logger.Infof("%d matches downloaded", len(matches))

	return matches, nil
}
