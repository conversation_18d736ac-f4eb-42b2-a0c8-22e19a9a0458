components:
  schemas:
    DnaMatch:
      type: object
      required:
        - id
        - sharedLength
        - createdTime
        - matchName
        - matchIndividualId
        - sourceName
        - sourceIndividualId
        - sharedMatchesCount
      properties:
        id:
          type: string
        sharedLength:
          type: number
        createdTime:
          type: string
          format: date-time
        matchName:
          type: string
        matchIndividualId:
          type: string
        sourceName:
          type: string
        sourceIndividualId:
          type: string
        sharedMatchesCount:
          type: integer
          nullable: true