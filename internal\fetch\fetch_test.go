package fetch

import (
	"database/sql"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
)

// RequestError is a custom error type for testing
type RequestError struct {
	StatusCode int
	Message    string
}

func (e *RequestError) Error() string {
	return e.Message
}

// TestDB is a simplified implementation for testing
type TestDB struct {
	ExecFunc func(query string, args ...interface{}) (sql.Result, error)
}

func (m *TestDB) Exec(query string, args ...interface{}) (sql.Result, error) {
	return m.ExecFunc(query, args...)
}

// TestResult is a simplified implementation for testing
type TestResult struct{}

func (m *TestResult) LastInsertId() (int64, error) {
	return 1, nil
}

func (m *TestResult) RowsAffected() (int64, error) {
	return 1, nil
}

// TestMyHeritageApiClient is a simplified implementation for testing
type TestMyHeritageApiClient struct {
	bearerToken BearerToken
	db          *TestDB
	fetchFunc   func(url string, query string, queryName string) ([]byte, error)
}

func (c *TestMyHeritageApiClient) Fetch(url string, query string, queryName string) ([]byte, error) {
	return c.fetchFunc(url, query, queryName)
}

func TestFetch_Success(t *testing.T) {
	// Create a test server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Check the request method
		if r.Method != "POST" {
			t.Errorf("Expected POST request, got %s", r.Method)
		}

		// Check the Content-Type header
		contentType := r.Header.Get("Content-Type")
		if !strings.Contains(contentType, "multipart/form-data") {
			t.Errorf("Expected Content-Type to contain 'multipart/form-data', got %s", contentType)
		}

		// Write a response
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{"data":{"dna_kit":{"dna_matches":{"data":[{"id":"dnamatch-123"}]}}}}`))
	}))
	defer server.Close()

	// Create a test client
	client := &TestMyHeritageApiClient{
		bearerToken: BearerToken("test-token"),
		db: &TestDB{
			ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
				return &TestResult{}, nil
			},
		},
		fetchFunc: func(url string, query string, queryName string) ([]byte, error) {
			if url != server.URL {
				t.Errorf("Expected URL %s, got %s", server.URL, url)
			}
			if query != "test-query" {
				t.Errorf("Expected query 'test-query', got '%s'", query)
			}
			if queryName != "test-query-name" {
				t.Errorf("Expected queryName 'test-query-name', got '%s'", queryName)
			}
			return []byte(`{"data":{"dna_kit":{"dna_matches":{"data":[{"id":"dnamatch-123"}]}}}}`), nil
		},
	}

	// Call the Fetch method
	response, err := client.Fetch(server.URL, "test-query", "test-query-name")

	// Check for errors
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	// Check the response
	expectedResponse := `{"data":{"dna_kit":{"dna_matches":{"data":[{"id":"dnamatch-123"}]}}}}`
	if string(response) != expectedResponse {
		t.Errorf("Expected response %s, got %s", expectedResponse, string(response))
	}
}

func TestFetch_RequestError(t *testing.T) {
	// Create a test client that returns an error
	client := &TestMyHeritageApiClient{
		bearerToken: BearerToken("test-token"),
		db: &TestDB{
			ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
				return &TestResult{}, nil
			},
		},
		fetchFunc: func(url string, query string, queryName string) ([]byte, error) {
			return nil, &RequestError{StatusCode: 0, Message: "invalid URL"}
		},
	}

	// Call the Fetch method
	_, err := client.Fetch("invalid-url", "test-query", "test-query-name")

	// Check for errors
	if err == nil {
		t.Error("Expected an error, got nil")
	}
}

func TestFetch_RateLimitError(t *testing.T) {
	// Create a test client that returns a rate limit error
	client := &TestMyHeritageApiClient{
		bearerToken: BearerToken("test-token"),
		db: &TestDB{
			ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
				return &TestResult{}, nil
			},
		},
		fetchFunc: func(url string, query string, queryName string) ([]byte, error) {
			return nil, &RequestError{StatusCode: http.StatusTooManyRequests, Message: "exceeded the allowed rate limit"}
		},
	}

	// Call the Fetch method
	_, err := client.Fetch("https://example.com", "test-query", "test-query-name")

	// Check for errors
	if err == nil {
		t.Error("Expected an error, got nil")
	}

	// Check the error message
	if err.Error() != "exceeded the allowed rate limit" {
		t.Errorf("Expected error message 'exceeded the allowed rate limit', got '%s'", err.Error())
	}
}

func TestFetch_InvalidTokenError(t *testing.T) {
	// Create a test client that returns an invalid token error
	client := &TestMyHeritageApiClient{
		bearerToken: BearerToken("test-token"),
		db: &TestDB{
			ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
				return &TestResult{}, nil
			},
		},
		fetchFunc: func(url string, query string, queryName string) ([]byte, error) {
			return nil, &RequestError{StatusCode: http.StatusUnauthorized, Message: "Invalid OAuth2 access token"}
		},
	}

	// Call the Fetch method
	_, err := client.Fetch("https://example.com", "test-query", "test-query-name")

	// Check for errors
	if err == nil {
		t.Error("Expected an error, got nil")
	}

	// Check the error message
	if err.Error() != "Invalid OAuth2 access token" {
		t.Errorf("Expected error message 'Invalid OAuth2 access token', got '%s'", err.Error())
	}
}
