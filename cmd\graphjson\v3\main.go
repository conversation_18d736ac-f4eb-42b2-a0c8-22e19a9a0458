package main

import (
	"database/sql"
	"encoding/json"
	"fmt"
	_ "github.com/lib/pq"
	"html/template"
	"os"
)

type GraphNode struct {
	Id    string `json:"id"`
	Group string `json:"group"`
}

type GraphLink struct {
	Source string `json:"source"`
	Target string `json:"target"`
	Value  int    `json:"value"`
}

type GraphData struct {
	Nodes []GraphNode `json:"nodes"`
	Links []GraphLink `json:"links"`
}

var individualIds []string

func main() {
	connStr := "user=postgres password=mysecretpassword dbname=dna_match_db sslmode=disable"
	db, err := sql.Open("postgres", connStr)
	if err != nil {
		fmt.Println("Error connecting to the database:", err)
		return
	}
	defer db.Close()

	// Pavel
	/*
		offset := 0
		sourceIndividualId := "individual-32361932-1000006"
		dnaMatchIDs, err := getDnaMatchesByIndividualId(db, sourceIndividualId, offset, 500)
		bearerToken := "4.cbdd04f731661d487d728136bd8331a0.122085562.1725369381.1440.7345153..s0gts5f0.881b70f1bb9aaa4b3d9ce42ab04ca654004f194b2c7b8298a45976d057f6195d"
	*/

	// Elena

	//offset := 0
	//sourceIndividualId := "individual-32361932-1000002"
	//dnaMatchIDs, err := getDnaMatchesByIndividualId(db, sourceIndividualId, offset, 500)
	//bearerToken := os.Getenv("BEARER_TOKEN")

	//Dmitrii
	offset := 0
	sourceIndividualId := "individual-32361932-1000003"
	sourceName := "Дмитрий Magur (geb. Магур)"

	//sourceIndividualId := "individual-1592334192-1500079"
	//sourceName := "Віктор Заноха"

	graphData := GraphData{}
	//graphData.Nodes = append(graphData.Nodes, GraphNode{Id: "Павел Магур", Group: "individual-32361932-1000006"})
	//graphData.Nodes = append(graphData.Nodes, GraphNode{Id: "Елена Магур (geb. Тягунова Тягунов)", Group: "individual-32361932-1000002"})
	//graphData.Nodes = append(graphData.Nodes, GraphNode{Id: "Дмитрий Magur (geb. Магур)", Group: "individual-32361932-1000003"})

	individuals, err := getIndividuals(db, 0, 10000)
	//var individuals []Individual
	//individuals = append(individuals, Individual{
	//	ID:   "individual-32361932-1000006",
	//	Name: "Павел Магур",
	//})
	//individuals = append(individuals, Individual{
	//	ID:   "individual-32361932-1000002",
	//	Name: "Елена Магур (geb. Тягунова Тягунов)",
	//})
	//individuals = append(individuals, Individual{
	//	ID:   "individual-32361932-1000003",
	//	Name: "Дмитрий Magur (geb. Магур)",
	//})
	//individuals = append(individuals, Individual{
	//	ID:   "individual-821625541-1500003",
	//	Name: "Annette Solveig Jensen",
	//})
	//individuals = append(individuals, Individual{
	//	ID:   "individual-391218951-2000005",
	//	Name: "Sebastian Markus Bauer",
	//})
	//individuals = append(individuals, Individual{
	//	ID:   "individual-391218951-2000018",
	//	Name: "Hildegard Bauer",
	//})
	//individuals = append(individuals, Individual{
	//	ID:   "individual-616617281-1500028",
	//	Name: "Michael Jakob Johannes Maria Huhn",
	//})
	//individuals = append(individuals, Individual{
	//	ID:   "individual-800978721-1500003",
	//	Name: "Javier Santome Gonzalez",
	//})
	//individuals = append(individuals, Individual{
	//	ID:   "individual-1184664891-1500003",
	//	Name: "Buka Buchukuri",
	//})
	//individuals = append(individuals, Individual{
	//	ID:   "individual-1284261062-1500001",
	//	Name: "Alex Wagner",
	//})

	//individuals, err := getIndividualsBySourceIndividualId(db, sourceIndividualId)
	//if err != nil {
	//	fmt.Println("Error fetching individual from db:", err)
	//}
	//
	nodesHashMap := make(map[string]int)
	//nodesHashMap[sourceIndividualId] = 0
	//
	for _, individual := range individuals {
		//graphData.Nodes = append(graphData.Nodes, GraphNode{Id: individual.Name, Group: individual.ID})
		nodesHashMap[individual.ID] = 0
		individualIds = append(individualIds, individual.ID)
	}

	fmt.Printf("Individual IDs: %v\n", individualIds)
	dnaMatchIDs, err := getDnaMatchesByIndividualId(db, sourceIndividualId, offset, 5000, individualIds)
	fmt.Printf("DNA matches: %v\n", dnaMatchIDs)

	linksHashMap := make(map[string]map[string]GraphLink)

	// Now loop through the slice of DnaMatch
	for _, match := range dnaMatchIDs {
		if match.MatchIndividualID == "" {
			continue
		}

		_, ok := nodesHashMap[match.MatchIndividualID]
		if !ok {
			continue
		}

		_, ok2 := linksHashMap[sourceIndividualId][match.MatchIndividualID]
		_, ok3 := linksHashMap[match.MatchIndividualID][sourceIndividualId]

		if !ok2 && !ok3 {
			//graphData.Links = append(graphData.Links, GraphLink{Source: sourceName, Target: match.Name, Value: int(match.SharedLength)})
			_, ok4 := linksHashMap[sourceIndividualId]
			if !ok4 {
				linksHashMap[sourceIndividualId] = make(map[string]GraphLink)
			}
			linksHashMap[sourceIndividualId][match.MatchIndividualID] = GraphLink{Source: sourceName, Target: match.Name, Value: int(match.SharedLength)}
			nodesHashMap[match.MatchIndividualID]++
			nodesHashMap[sourceIndividualId]++
		}

		linksHashMap, nodesHashMap = getSharedMatches(db, match, &graphData, 1, linksHashMap, nodesHashMap)
	}

	//fmt.Printf("Links hash map: %v\n", linksHashMap)

	minLinks := 0
	for _, individual := range individuals {
		if nodesHashMap[individual.ID] > minLinks {
			graphData.Nodes = append(graphData.Nodes, GraphNode{Id: individual.Name, Group: individual.ID})
			for linkedIndividualId, link := range linksHashMap[individual.ID] {
				if nodesHashMap[linkedIndividualId] > minLinks && linkedIndividualId != sourceIndividualId {
					graphData.Links = append(graphData.Links, link)
				}
			}
		}
	}

	for _, links := range linksHashMap {
		for linkedIndividualId, link := range links {
			if nodesHashMap[linkedIndividualId] > minLinks {
				graphData.Links = append(graphData.Links, link)
			}
		}
	}

	// Convert the struct to JSON
	jsonData, err := json.MarshalIndent(graphData, "", "  ")
	if err != nil {
		fmt.Println("Error converting struct to JSON:", err)
		return
	}

	// Read the HTML template from a file
	templateContent, err := os.ReadFile("graph.tmpl")
	if err != nil {
		fmt.Println("Error reading template file:", err)
		return
	}

	// Create a file to write the HTML content
	file, err := os.Create("graph.html")
	if err != nil {
		fmt.Println("Error creating file:", err)
		return
	}
	defer file.Close()

	// Parse and execute the template, injecting the JSON data
	tmpl, err := template.New("html").Parse(string(templateContent))
	if err != nil {
		fmt.Println("Error parsing template:", err)
		return
	}

	// Insert the JSON data into the HTML template
	err = tmpl.Execute(file, template.JS(jsonData))
	if err != nil {
		fmt.Println("Error executing template:", err)
		return
	}

	fmt.Println("HTML file generated successfully: graph.html")
}

func getSharedMatches(db *sql.DB, match Result, graphData *GraphData, level int, linksHashMap map[string]map[string]GraphLink, nodesHashMap map[string]int) (map[string]map[string]GraphLink, map[string]int) {
	if level > 3 {
		return linksHashMap, nodesHashMap
	}
	level++

	sharedMatches, err := getSharedDnaMatchesByIndividualId(db, match.MatchIndividualID, 0, 2000, individualIds)

	fmt.Printf("Match individual id: %s\n", match.MatchIndividualID)
	fmt.Printf("Number of shared matches: %d\n", len(sharedMatches))
	fmt.Printf("level: %d\n", level)
	if err != nil {
		fmt.Println("Error fetching shared matches:", err)
		return linksHashMap, nodesHashMap
	}
	for _, sharedMatch := range sharedMatches {
		if sharedMatch.MatchIndividualID == "" {
			continue
		}

		_, ok := nodesHashMap[sharedMatch.MatchIndividualID]
		if !ok {
			continue
		}
		fmt.Printf("Shared match: %v\n", sharedMatch)

		_, ok2 := linksHashMap[sharedMatch.MatchIndividualID][match.MatchIndividualID]
		_, ok3 := linksHashMap[match.MatchIndividualID][sharedMatch.MatchIndividualID]

		if !ok2 && !ok3 {
			//graphData.Links = append(graphData.Links, GraphLink{Source: match.Name, Target: sharedMatch.Name, Value: int(sharedMatch.SharedLength)})
			_, ok4 := linksHashMap[sharedMatch.MatchIndividualID]
			if !ok4 {
				linksHashMap[sharedMatch.MatchIndividualID] = make(map[string]GraphLink)
			}
			linksHashMap[sharedMatch.MatchIndividualID][match.MatchIndividualID] = GraphLink{Source: match.Name, Target: sharedMatch.Name, Value: int(sharedMatch.SharedLength)}
			nodesHashMap[sharedMatch.MatchIndividualID]++
			nodesHashMap[match.MatchIndividualID]++
		}

		linksHashMap, nodesHashMap = getSharedMatches(db, sharedMatch, graphData, level, linksHashMap, nodesHashMap)
	}

	return linksHashMap, nodesHashMap

}
