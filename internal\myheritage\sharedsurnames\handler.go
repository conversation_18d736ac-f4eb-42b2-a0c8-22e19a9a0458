package sharedsurnames

import (
	"encoding/json"
	"fmt"
	"github.com/dmagur/myheritage/internal/fetch"
	log "github.com/sirupsen/logrus"
	"math/rand"
	"time"
)

type HandlerInterface interface {
	Handle() error
}

type Handler struct {
	client     *fetch.MyHeritageApiClient
	logger     *log.Logger
	repository *Repository
}

func NewHandler(
	client *fetch.MyHeritageApiClient,
	logger *log.Logger,
	repository *Repository,
) Handler {
	return Handler{
		client:     client,
		logger:     logger,
		repository: repository,
	}
}

func (h Handler) Handle() error {
	fmt.Println("-----------Shared surnames--------------")
	dnaMatchIDs, err := h.repository.GetDnaMatchesWithoutSharedSurnames(0, 1500)

	if err != nil {
		return fmt.Errorf("error getting DNA matches from DB: %w", err)
	}

	offset := 0
	// Now loop through the slice of DnaMatch
	for _, match := range dnaMatchIDs {
		// Output the match details
		fmt.Printf("Offset: %d\n", offset)
		fmt.Printf("Match ID: %s\n", match.ID)
		fmt.Printf("Match Individual ID: %s\n", match.MatchIndividualID)
		offset++

		sharedCount := 0
		apiResponse, err := h.Fetch(match.ID)

		if err != nil {
			return fmt.Errorf("error fetching DNA matches: %w", err)
		}

		fmt.Printf("Shared surnames count: %d\n", apiResponse.Data.DNAMatch.SurnameList.Count)

		surnameCount, placeCount, err := h.repository.saveDnaMatcheSurnames(apiResponse.Data.DNAMatch.SurnameList.Data, match.MatchIndividualID)

		if err != nil {
			return fmt.Errorf("error upserting surnames: %w", err)
		}

		randomNumber := rand.Intn(10) + 1
		time.Sleep(time.Duration(randomNumber) * time.Second)

		fmt.Printf("Surname count: %d\n", sharedCount)
		err = h.repository.updateSurnamesCount(match.ID, surnameCount)
		if err != nil {
			return fmt.Errorf("error updating surnames count: %w", err)
		}

		fmt.Printf("Place count: %d\n", placeCount)
		err = h.repository.updatePlacesCount(match.ID, placeCount)
		if err != nil {
			return fmt.Errorf("error updating places count: %w", err)
		}
	}

	return nil
}

func (h *Handler) Fetch(dnaMatchId string) (*ApiResponse, error) {
	url := "https://familygraphql.myheritage.com/dna_single_match_get_shared_surnames/"

	query := fmt.Sprintf(`"{dna_match(id:\"%s\",lang:\"DE\"){surname_matches{count data{surnames individual_ancestors{...surname_individual}other_individual_ancestors{...surname_individual}}}surname_list:surname_matches(filter:\"all\"){count data{surnames other_surnames places{country state_or_province country_code state_or_province_code}other_places{country state_or_province country_code state_or_province_code}}}}}fragment surname_individual on RelationshipConnection{count data{relationship_description individual{id name gender age_group personal_photo{...personal_photo_info}}}}fragment personal_photo_info on Photo{thumbnails(thumbnail_size:\"96x96\"){url}}"`,
		dnaMatchId)

	body, err := h.client.Fetch(url, query, "DNA Single Match - get shared surnames")
	if err != nil {
		fmt.Println("Error fetching response:", err)
		return nil, err
	}

	var apiResponse ApiResponse
	err = json.Unmarshal(body, &apiResponse)
	if err != nil {
		// Print the response body if JSON unmarshalling fails
		fmt.Println("Error unmarshalling JSON response:", err)
		fmt.Printf("Response body: %s\n", string(body))
		return nil, err
	}

	return &apiResponse, nil
}
