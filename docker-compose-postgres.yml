version: '3'
services:
  db:
    image: postgres
    container_name: my_postgres_db
    environment:
      POSTGRES_PASSWORD: mysecretpassword
      POSTGRES_DB: dna_match_db
    networks:
      - my_network
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./data:/root/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5

  apiserver:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: my_api_server
    depends_on:
      db:
        condition: service_healthy
    environment:
      - PORT=1231
      - DB_TYPE=postgres
      - DB_HOST=db
      - DB_USER=postgres
      - DB_PASSWORD=mysecretpassword
      - DB_NAME=dna_match_db
    networks:
      - my_network
    ports:
      - "1231:1231"
networks:
  my_network:
    driver: bridge

volumes:
  postgres_data:
