# API Package

This package contains the API implementation for the MyHeritage project.

## Token API

The token API provides endpoints for managing API tokens:

- `GET /api/v1/token`: Get the current active token
- `POST /api/v1/token`: Create or update the token

## Running Tests

### Unit Tests

To run the unit tests for the API package:

```bash
go test -v ./internal/api
```

### Integration Tests

To run the integration tests for the token API, you need to start the token API server first:

```bash
# Using the shell script (Linux/Mac)
./scripts/run_token_api.sh

# Using the PowerShell script (Windows)
./scripts/run_token_api.ps1

# Or manually
go run cmd/tokenapi/main.go
```

Then, in a separate terminal, run the integration tests:

```bash
# Using the shell script (Linux/Mac)
./scripts/run_token_api_tests.sh

# Using the PowerShell script (Windows)
./scripts/run_token_api_tests.ps1

# Or manually
RUN_INTEGRATION_TESTS=true go test -v ./internal/api -run TestTokenAPIIntegration
```

The integration tests will:

1. Test the health endpoint
2. Test getting the current token state
3. Test creating a token
4. Test getting the token after creating it
5. Test updating the token
6. Test getting the token after updating it
7. Test updating the token with an invalid value (too short)
8. Test updating the token with an invalid JSON

## API Documentation

### GET /api/v1/token

Returns the current active token.

**Response:**

If a token exists:

```json
{
  "token": {
    "token": "my-te...12345",
    "created_at": "2025-04-21T11:32:48.555186Z",
    "is_active": true
  }
}
```

If no token exists:

```json
{
  "message": "No active token found",
  "token": {
    "created_at": "0001-01-01T00:00:00Z",
    "is_active": false
  }
}
```

### POST /api/v1/token

Creates or updates the token.

**Request:**

```json
{
  "token": "my-test-token-12345"
}
```

**Response:**

```json
{
  "message": "Token updated successfully"
}
```

**Error Responses:**

If the token is too short:

```json
{
  "error": "Token is too short"
}
```

If the request format is invalid:

```json
{
  "error": "Invalid request format"
}
```

### GET /health

Returns the health status of the API.

**Response:**

```json
{
  "status": "ok"
}
```
