package dnamatch

import (
	"errors"
	"testing"
)

// TestHandler is a simplified implementation for testing
type TestHandler struct {
	GetMatchesFunc func(dnaKitID string, offset int, limit int) ([]DNAMatch, error)
	HandleFunc     func() error
}

func (h *TestHandler) GetMatches(dnaKitID string, offset int, limit int) ([]DNAMatch, error) {
	return h.GetMatchesFunc(dnaKitID, offset, limit)
}

func (h *TestHandler) Handle() error {
	return h.HandleFunc()
}

func TestGetMatches_Success(t *testing.T) {
	// Create a test handler
	handler := &TestHandler{
		GetMatchesFunc: func(dnaKitID string, offset int, limit int) ([]DNAMatch, error) {
			if dnaKitID != "dnakit-123" {
				t.<PERSON><PERSON><PERSON>("Expected dnaKitID 'dnakit-123', got '%s'", dnaKitID)
			}
			if offset != 0 {
				t.<PERSON><PERSON>("Expected offset 0, got %d", offset)
			}
			if limit != 10 {
				t.<PERSON><PERSON><PERSON>("Expected limit 10, got %d", limit)
			}
			return []DNAMatch{
				{
					Id:                            "dnamatch-123",
					TotalSharedSegmentsLengthInCm: 10.5,
					PercentageOfSharedSegments:    0.5,
					OtherDNAKit: OtherDNAKit{
						Submitter: Submitter{
							Id:   "submitter-123",
							Name: "Test Submitter",
						},
						AssociatedIndividual: AssociatedIndividual{
							Member: Member{
								Submitter: Submitter{
									Id:   "individual-123",
									Name: "Test Individual",
								},
							},
						},
					},
				},
			}, nil
		},
	}

	// Call the GetMatches method
	matches, err := handler.GetMatches("dnakit-123", 0, 10)

	// Check for errors
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	// Check the number of matches
	if len(matches) != 1 {
		t.Errorf("Expected 1 match, got %d", len(matches))
	}

	// Check the match ID
	if matches[0].Id != "dnamatch-123" {
		t.Errorf("Expected match ID 'dnamatch-123', got '%s'", matches[0].Id)
	}
}

func TestGetMatches_Error(t *testing.T) {
	// Create a test handler
	handler := &TestHandler{
		GetMatchesFunc: func(dnaKitID string, offset int, limit int) ([]DNAMatch, error) {
			return nil, errors.New("fetch error")
		},
	}

	// Call the GetMatches method
	matches, err := handler.GetMatches("dnakit-123", 0, 10)

	// Check for errors
	if err == nil {
		t.Error("Expected an error, got nil")
	}

	// Check the error message
	if err.Error() != "fetch error" {
		t.Errorf("Expected error message 'fetch error', got '%s'", err.Error())
	}

	// Check the matches
	if matches != nil {
		t.Errorf("Expected nil matches, got %v", matches)
	}
}

func TestHandle_Success(t *testing.T) {
	// Create a test handler
	handler := &TestHandler{
		HandleFunc: func() error {
			return nil
		},
	}

	// Call the Handle method
	err := handler.Handle()

	// Check for errors
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}
}

func TestHandle_Error(t *testing.T) {
	// Create a test handler
	handler := &TestHandler{
		HandleFunc: func() error {
			return errors.New("handle error")
		},
	}

	// Call the Handle method
	err := handler.Handle()

	// Check for errors
	if err == nil {
		t.Error("Expected an error, got nil")
	}

	// Check the error message
	if err.Error() != "handle error" {
		t.Errorf("Expected error message 'handle error', got '%s'", err.Error())
	}
}
