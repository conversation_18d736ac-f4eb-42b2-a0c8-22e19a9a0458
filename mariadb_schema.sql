-- MariaDB schema equivalent to the PostgreSQL schema

-- Create tables

CREATE TABLE api_calls (
    id INT AUTO_INCREMENT PRIMARY KEY,
    url VARCHAR(255),
    response TEXT,
    created_at TIMESTAMP,
    query TEXT
);

CREATE TABLE api_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    token TEXT NOT NULL,
    created_at TIMESTAMP NOT NULL,
    is_active BOOLEAN DEFAULT TRUE NOT NULL
);

CREATE TABLE chromosome (
    id INT AUTO_INCREMENT PRIMARY KEY,
    chromosome_id INT,
    start_location INT,
    end_location INT,
    length INT,
    length_in_percentage DOUBLE,
    centromere_position_in_percents DOUBLE
);

CREATE TABLE dna_enums (
    id INT AUTO_INCREMENT PRIMARY KEY,
    system_id VARCHAR(255),
    chromosomes JSON,
    not_sampled_chromosome_segments JSON
);

CREATE TABLE dna_kits (
    id VARCHAR(255) PRIMARY KEY,
    name VA<PERSON>HA<PERSON>(255) NOT NULL
);

CREATE TABLE submitters (
    id VARCHAR(255) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    name_transliterated VARCHAR(255),
    first_name VARCHAR(255) NOT NULL,
    first_name_transliterated VARCHAR(255),
    link TEXT,
    is_public BOOLEAN NOT NULL
);

CREATE TABLE trees (
    id VARCHAR(255) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    link TEXT,
    individual_count INT NOT NULL,
    site_is_request_membership_allowed BOOLEAN NOT NULL,
    site_creator_id VARCHAR(255),
    site_creator_name VARCHAR(255),
    site_creator_name_transliterated VARCHAR(255),
    site_creator_country VARCHAR(255),
    site_creator_country_code VARCHAR(10),
    site_creator_link TEXT,
    site_creator_is_public BOOLEAN NOT NULL
);

CREATE TABLE individuals (
    id VARCHAR(255) PRIMARY KEY,
    first_name TEXT NOT NULL,
    first_name_transliterated TEXT,
    name TEXT NOT NULL,
    name_transliterated TEXT,
    gender CHAR(1),
    age_group VARCHAR(50),
    age_group_in_years VARCHAR(50),
    link_in_pedigree_tree TEXT,
    link_in_tree TEXT,
    relationship_description TEXT,
    birth_place TEXT,
    tree_id VARCHAR(255),
    last_name VARCHAR(255),
    formatted_last_name TEXT,
    is_alive BOOLEAN,
    birth_date_year VARCHAR(255),
    death_date_year VARCHAR(255),
    FOREIGN KEY (tree_id) REFERENCES trees(id)
);

CREATE TABLE dna_matches (
    id VARCHAR(255) PRIMARY KEY,
    link TEXT,
    total_shared_segments_length_in_cm DOUBLE NOT NULL,
    largest_shared_segment_length_in_cm DOUBLE,
    percentage_of_shared_segments DOUBLE NOT NULL,
    total_shared_segments INT,
    confidence_level VARCHAR(50),
    exact_dna_relationship VARCHAR(255),
    genealogical_relationship VARCHAR(255),
    is_recently_recalculated BOOLEAN,
    created_at TIMESTAMP NOT NULL,
    submitter_id VARCHAR(255),
    match_individual_id VARCHAR(255),
    tree_id VARCHAR(255),
    source_individual_id VARCHAR(255),
    shared_matches_count INT,
    updated_at TIMESTAMP,
    surnames_count INT,
    places_count INT,
    shared_segments_count INT,
    pedigree_processed BOOLEAN DEFAULT FALSE NOT NULL,
    FOREIGN KEY (submitter_id) REFERENCES submitters(id)
);

CREATE TABLE dna_shared_segment (
    id INT AUTO_INCREMENT PRIMARY KEY,
    dna_match_id VARCHAR(255),
    chromosome_id INT,
    start_position INT,
    end_position INT,
    start_rsid VARCHAR(50),
    end_rsid VARCHAR(50),
    length_in_centimorgans DOUBLE,
    snp_count INT,
    FOREIGN KEY (dna_match_id) REFERENCES dna_matches(id)
);

CREATE TABLE family (
    id VARCHAR(255) PRIMARY KEY
);

CREATE TABLE individual_family (
    id INT AUTO_INCREMENT PRIMARY KEY,
    family_id VARCHAR(255) NOT NULL,
    individual_id VARCHAR(255) NOT NULL,
    role VARCHAR(255) NOT NULL,
    UNIQUE KEY individual_family_unique (individual_id, family_id, role),
    FOREIGN KEY (family_id) REFERENCES family(id),
    FOREIGN KEY (individual_id) REFERENCES individuals(id)
);

CREATE TABLE place (
    id INT AUTO_INCREMENT PRIMARY KEY,
    country VARCHAR(255),
    state_or_province VARCHAR(255),
    country_code VARCHAR(255),
    state_or_province_code VARCHAR(255),
    UNIQUE KEY place_unique (country, state_or_province, country_code, state_or_province_code)
);

CREATE TABLE individual_place (
    id INT AUTO_INCREMENT PRIMARY KEY,
    individual_id VARCHAR(255),
    place_id INT,
    UNIQUE KEY individual_place_unique (individual_id, place_id)
);

CREATE TABLE surname (
    id INT AUTO_INCREMENT PRIMARY KEY,
    surname VARCHAR(255),
    UNIQUE KEY surname_unique (surname)
);

CREATE TABLE individual_surname (
    id INT AUTO_INCREMENT PRIMARY KEY,
    individual_id VARCHAR(255),
    surname_id INT,
    UNIQUE KEY individual_surname_unique (individual_id, surname_id)
);

CREATE TABLE not_sampled_chromosome_segment (
    id INT AUTO_INCREMENT PRIMARY KEY,
    chromosome_id INT,
    start_position INT,
    end_position INT,
    start_percentage DOUBLE,
    end_percentage DOUBLE
);

CREATE TABLE processed_dna_kits (
    id INT AUTO_INCREMENT PRIMARY KEY,
    dnakitid VARCHAR(255) NOT NULL UNIQUE,
    processed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE shared_matches (
    id INT AUTO_INCREMENT PRIMARY KEY
);

CREATE TABLE shared_segment (
    id INT AUTO_INCREMENT PRIMARY KEY,
    match_id VARCHAR(255) NOT NULL,
    chromosome_id INT NOT NULL,
    start_position INT NOT NULL,
    end_position INT NOT NULL,
    start_rsid VARCHAR(255) NOT NULL,
    end_rsid VARCHAR(255) NOT NULL,
    length_in_centimorgans DOUBLE NOT NULL,
    snp_count INT NOT NULL,
    UNIQUE KEY shared_segment_unique (match_id, chromosome_id, start_position, end_position, start_rsid, end_rsid, length_in_centimorgans, snp_count)
);

-- Create indexes
CREATE INDEX idx_api_tokens_is_active ON api_tokens(is_active);

-- Add foreign key constraint for dna_enums
ALTER TABLE dna_enums
ADD CONSTRAINT dna_enums_system_id_fkey FOREIGN KEY (system_id) REFERENCES dna_matches(id);
