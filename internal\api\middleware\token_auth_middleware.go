package middleware

import (
	"errors"
	"github.com/dmagur/myheritage/internal/api/errorresponse"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
)

const tokenLength = 2

func extractBearerToken(header string) (string, error) {
	if header == "" {
		return "", errors.New("bad header value given")
	}

	token := strings.Split(header, " ")
	if len(token) != tokenLength {
		return "", errors.New("incorrectly formatted authorization header")
	}

	return token[1], nil
}

func TokenAuthMiddleware(token AuthToken) gin.HandlerFunc {
	return func(context *gin.Context) {
		headerToken, err := extractBearerToken(context.GetHeader("Authorization"))

		if headerToken == "" || err != nil {
			panic(errorresponse.NewErr(http.StatusUnauthorized, errors.New("unauthorized")))
		}

		if headerToken != string(token) {
			panic(errorresponse.NewErr(http.StatusUnauthorized, errors.New("invalid API token")))
		}

		context.Next()
	}
}
