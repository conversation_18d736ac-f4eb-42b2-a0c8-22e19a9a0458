import React, { useState, useEffect, useRef } from 'react';
import { useQuery } from '@tanstack/react-query';
import {
  Box,
  Paper,
  Typography,
  Grid,
  Slider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  CircularProgress,
  Alert,
  Card,
  CardContent,
  Chip,
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  Fullscreen as FullscreenIcon,
  Settings as SettingsIcon,
} from '@mui/icons-material';

import { analyticsApi, NetworkGraph as NetworkGraphData } from '../../services/api';
import ForceGraph3D from '../../components/NetworkGraph/ForceGraph3D';

const NetworkGraph: React.FC = () => {
  const [depth, setDepth] = useState(2);
  const [minCM, setMinCM] = useState(20);
  const [limit, setLimit] = useState(1000);
  const [graphType, setGraphType] = useState<'3d' | '2d'>('3d');
  const graphRef = useRef<any>(null);

  const {
    data: networkData,
    isLoading,
    error,
    refetch,
  } = useQuery<NetworkGraphData>({
    queryKey: ['networkGraph', depth, minCM, limit],
    queryFn: () => analyticsApi.getNetworkGraph({ depth, min_cm: minCM, limit }),
    refetchOnWindowFocus: false,
  });

  const handleRefresh = () => {
    refetch();
  };

  const handleFullscreen = () => {
    if (graphRef.current) {
      const element = graphRef.current;
      if (element.requestFullscreen) {
        element.requestFullscreen();
      }
    }
  };

  const handleReset = () => {
    setDepth(2);
    setMinCM(20);
    setLimit(1000);
  };

  if (isLoading) {
    return (
      <Box
        display="flex"
        flexDirection="column"
        justifyContent="center"
        alignItems="center"
        minHeight="400px"
        gap={2}
      >
        <CircularProgress size={60} />
        <Typography variant="h6" color="text.secondary">
          Loading network graph...
        </Typography>
        <Typography variant="body2" color="text.secondary">
          This may take a moment for large datasets
        </Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Alert
        severity="error"
        action={
          <Button onClick={handleRefresh} startIcon={<RefreshIcon />}>
            Retry
          </Button>
        }
      >
        Failed to load network graph data. Please check if the API server is running.
      </Alert>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box mb={3}>
        <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
          Network Graph Visualization
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          Interactive 3D visualization of DNA match relationships
        </Typography>
      </Box>

      {/* Controls */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Graph Controls
            </Typography>
            
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6} md={3}>
                <Typography gutterBottom>Network Depth: {depth}</Typography>
                <Slider
                  value={depth}
                  onChange={(_, value) => setDepth(value as number)}
                  min={1}
                  max={5}
                  step={1}
                  marks
                  valueLabelDisplay="auto"
                />
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <Typography gutterBottom>Min cM: {minCM}</Typography>
                <Slider
                  value={minCM}
                  onChange={(_, value) => setMinCM(value as number)}
                  min={5}
                  max={100}
                  step={5}
                  valueLabelDisplay="auto"
                />
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <Typography gutterBottom>Max Connections: {limit}</Typography>
                <Slider
                  value={limit}
                  onChange={(_, value) => setLimit(value as number)}
                  min={100}
                  max={5000}
                  step={100}
                  valueLabelDisplay="auto"
                />
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <FormControl fullWidth>
                  <InputLabel>Graph Type</InputLabel>
                  <Select
                    value={graphType}
                    label="Graph Type"
                    onChange={(e) => setGraphType(e.target.value as '3d' | '2d')}
                  >
                    <MenuItem value="3d">3D Force Graph</MenuItem>
                    <MenuItem value="2d">2D Network</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            </Grid>

            <Box sx={{ mt: 2, display: 'flex', gap: 1 }}>
              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={handleRefresh}
              >
                Refresh Data
              </Button>
              <Button
                variant="outlined"
                startIcon={<SettingsIcon />}
                onClick={handleReset}
              >
                Reset Settings
              </Button>
              <Button
                variant="outlined"
                startIcon={<FullscreenIcon />}
                onClick={handleFullscreen}
              >
                Fullscreen
              </Button>
            </Box>
          </Paper>
        </Grid>

        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Graph Statistics
            </Typography>
            
            {networkData && (
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Card variant="outlined">
                    <CardContent sx={{ textAlign: 'center', py: 1 }}>
                      <Typography variant="h5" color="primary">
                        {networkData.stats.node_count}
                      </Typography>
                      <Typography variant="body2">Nodes</Typography>
                    </CardContent>
                  </Card>
                </Grid>
                
                <Grid item xs={6}>
                  <Card variant="outlined">
                    <CardContent sx={{ textAlign: 'center', py: 1 }}>
                      <Typography variant="h5" color="secondary">
                        {networkData.stats.edge_count}
                      </Typography>
                      <Typography variant="body2">Connections</Typography>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 1 }}>
                    <Chip
                      label={`Depth: ${networkData.stats.depth}`}
                      size="small"
                      color="primary"
                    />
                    <Chip
                      label={`Min cM: ${networkData.stats.min_cm}`}
                      size="small"
                      color="secondary"
                    />
                  </Box>
                </Grid>
              </Grid>
            )}
          </Paper>
        </Grid>
      </Grid>

      {/* Graph Visualization */}
      <Paper sx={{ p: 0, height: '70vh', overflow: 'hidden' }} ref={graphRef}>
        {networkData && (
          <ForceGraph3D
            data={networkData}
            type={graphType}
            width="100%"
            height="70vh"
          />
        )}
      </Paper>

      {/* Instructions */}
      <Paper sx={{ p: 2, mt: 2, backgroundColor: 'grey.50' }}>
        <Typography variant="body2" color="text.secondary">
          <strong>Instructions:</strong> Use mouse to rotate and zoom the 3D graph. 
          Click and drag nodes to explore connections. 
          Adjust the controls above to filter the network by relationship strength and depth.
        </Typography>
      </Paper>
    </Box>
  );
};

export default NetworkGraph;
