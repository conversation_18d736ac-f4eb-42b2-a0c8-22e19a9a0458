# MyHeritage Project - AI Assistant Memories

This file contains important context, preferences, and project information for AI assistants working on this codebase.

## 🔐 **Critical Preferences**

### Git & Version Control
- **NEVER commit without explicit approval** - Always ask before running `git commit`
- User prefers to review all changes before committing
- User prefers explicit confirmation before creating any new files or making significant changes
- User prefers to use Git Bash console or WSL to run bash scripts rather than PowerShell or batch files

### Development Environment
- User prefers to use Bash terminal for command execution
- User prefers to use WSL/Git Bash over PowerShell for development tasks
- User has updated the Go version

## 🏗️ **Project Architecture & Preferences**

### Database
- User is migrating from PostgreSQL to MariaDB databases
- User has MariaDB running on another host and doesn't need a container for it
- User prefers to use GORM for database interactions in Go applications
- User is concerned about data persistence when restarting Docker database containers

### API Development
- User prefers React for web interfaces
- User prefers to test APIs by running them and making actual test calls before proceeding
- User prefers to consolidate API functionality rather than having separate API servers
- User prefers to have automated tests for API functionality that can be run repeatedly
- User prefers to update wire.go files and let wire_gen.go files be automatically generated
- User prefers to run API servers with Docker for containerization
- User prefers to have comprehensive API documentation in Swagger for all routes
- User prefers to have working functionality rather than simplified placeholder implementations

### Dependency Management
- **Always use package managers** for dependency management instead of manually editing package files
- User prefers to use wire dependency injection for managing dependencies

### Testing
- User wants to improve test coverage in the codebase
- User prefers to cover new code with tests before proceeding with implementation

### Code Quality
- User prefers to remove redundant code to maintain a clean codebase
- User prefers explicit confirmation before creating any new files or making significant changes

## 📁 **Project Structure**

### Applications
The project has multiple applications in the `cmd/` directory:
- `apiserver` - Main API server
- `clique` - Clique analysis tool
- `myheritagedownloader` - Data downloader
- `graphjson/v1`, `graphjson/v2`, `graphjson/v3`, `graphjson/v4` - Graph JSON processors

### Key Directories
- `cmd/` - Application entry points
- `internal/api/` - API handlers and logic
- `internal/database/` - Database layer (migrated to GORM)
- `internal/fetch/` - Data fetching logic
- `internal/myheritage/` - MyHeritage-specific functionality

## 🛠️ **Tools & Scripts**

### Health Check Script
- `health_check.sh` - Comprehensive application health check tool
- Tests Go modules, compilation, unit tests, startup, Docker builds
- Supports multiple modes: `--quick`, `--verbose`, `--help`, `--list-apps`
- Auto-discovers applications from `cmd/` directory

### Usage Examples
```bash
./health_check.sh --quick          # Fast health check
./health_check.sh --verbose        # Detailed output
./health_check.sh --list-apps      # Show discovered applications
./health_check.sh --help           # Show all options
```

## 🧪 **Testing Strategy**

### Current Status
- All applications compile successfully
- Unit tests pass (some database-dependent tests are temporarily skipped)
- Health check script reports all checks passing

### Test Files
- `internal/api/api_test.go` - API endpoint tests
- `internal/api/token_handler_test.go` - Token handler tests (migrated to GORM-compatible)
- `internal/fetch/fetch_test.go` - Data fetching tests

## 🐳 **Docker & Containerization**

### Preferences
- User prefers to use Docker for containerization
- Health check script supports Docker container builds for apiserver and downloader
- User is concerned about data persistence in Docker containers

## 📝 **Documentation**

### Available Documentation
- `README_HEALTH_CHECK.md` - Comprehensive health check script documentation
- `PROJECT_MEMORIES.md` - This file (AI assistant context)

## 🔄 **Migration Notes**

### Database Migration
- Successfully migrated from custom database interfaces to GORM
- Some test files needed updates to work with GORM
- Added SQLite driver for future test improvements

### API Migration
- Consolidated API functionality
- Implemented token management endpoints
- Added Swagger documentation

## ⚠️ **Important Reminders**

1. **Always ask before committing** - This is the most critical preference
2. **Use package managers** - Never manually edit package.json, go.mod, etc.
3. **Test before implementing** - User prefers to test functionality before proceeding
4. **Use proper terminals** - Prefer Bash/WSL over PowerShell
5. **Explicit confirmation** - Ask before creating files or making significant changes

## 🎯 **Current Project Status**

- ✅ All applications compile successfully
- ✅ All unit tests pass
- ✅ Health check script working perfectly
- ✅ Database migration to GORM completed
- ✅ API consolidation completed
- 🔄 Some database-dependent tests temporarily skipped (documented for future improvement)

---

**Last Updated**: 2025-05-24
**AI Assistant**: Remember to always reference this file for project context and user preferences.
