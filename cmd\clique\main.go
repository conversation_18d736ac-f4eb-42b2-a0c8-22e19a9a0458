package main

import (
	"database/sql"
	"fmt"
	_ "github.com/lib/pq"
)

type App struct {
	db *sql.DB
}

func NewApp(db *sql.DB) *App {
	return &App{
		db: db,
	}
}

func main() {
	connStr := "user=postgres password=mysecretpassword dbname=dna_match_db sslmode=disable"
	db, err := sql.Open("postgres", connStr)
	if err != nil {
		fmt.Println("Error connecting to the database:", err)
		return
	}
	defer db.Close()

	app := NewApp(db)

	//vertices, err := getAllIndividualIds(db)
	vertices := []string{"1", "2", "3", "4", "5"}

	if err != nil {
		fmt.Println("Error getting individuals:", err)
		return
	}

	fmt.Println("count of individuals:", len(vertices))

	var n = make(map[string][]string)
	vertix := vertices[0]
	//for _, vertix := range vertices {
	n[vertix], _ = getAllConnectedIndividuals(db, vertix)
	//fmt.Printf("Number of connected individuals for %s: %v\n", vertix, n[vertix])
	r := []string{}
	x := []string{}
	app.BK(r, vertices, x, 0)
	//}
}

func (app *App) BK(r []string, p []string, x []string, level int) {
	level++
	connections := make(map[string][]string)
	connections["1"] = []string{"2", "3"}
	connections["2"] = []string{"1", "3"}
	connections["3"] = []string{"1", "2", "4"}
	connections["4"] = []string{"3"}
	connections["5"] = []string{}
	//fmt.Println("------------------------")
	//fmt.Printf("R: %v\n", r)
	//fmt.Printf("P: %v\n", p)
	//fmt.Printf("X: %v\n", x)

	if len(p) == 0 && len(x) == 0 {
		fmt.Printf("Clique found: %v\n", r)
		return
	}
	for _, v := range p {
		r = append(r, v)
		//nv, _ := getAllConnectedIndividuals(app.db, v)
		nv := connections[v]
		//fmt.Printf("V: %v\n", v)
		//fmt.Printf("NV: %v\n", nv)
		x = intersection(x, nv)
		p = intersection(p, nv)

		app.BK(r, p, x, level)
		x = append(x, v)
		p = remove(p, v)
		//fmt.Printf("P after removal %v\n", p)
	}
}

func intersection(s1 []string, s2 []string) (inter []string) {
	hash := make(map[string]bool)
	for _, e := range s1 {
		hash[e] = true
	}
	for _, e := range s2 {
		// If elements present in the hashmap then append intersection list.
		if hash[e] {
			inter = append(inter, e)
		}
	}

	return inter
}

func remove(l []string, item string) []string {
	result := []string{}
	for _, other := range l {
		if other != item {
			result = append(result, other)
		}
	}

	return result
}
