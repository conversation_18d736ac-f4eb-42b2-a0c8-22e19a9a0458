package internal

import (
	"time"

	log "github.com/sirupsen/logrus"
	"github.com/thlib/go-timezone-local/tzlocal"

	// load timezone data.
	_ "time/tzdata"
)

func GetRuntimeTimezone(logger *log.Logger) error {
	timezoneName, err := tzlocal.RuntimeTZ()
	if err != nil {
		// If we can't get the local timezone, use UTC
		logger.Warnf("Could not get runtime timezone: %v, using UTC", err)
		time.Local = time.UTC
		logger.WithField("timezoneName", "UTC").Debug("Runtime timezone set to UTC")
		return nil
	}

	location, err := time.LoadLocation(timezoneName)
	if err != nil {
		// If we can't load the timezone, use UTC
		logger.Warnf("Could not get timezone location: %v, using UTC", err)
		time.Local = time.UTC
		logger.WithField("timezoneName", "UTC").Debug("Runtime timezone set to UTC")
		return nil
	}

	time.Local = location

	logger.WithField("timezoneName", timezoneName).Debug("Runtime timezone set")

	return nil
}
