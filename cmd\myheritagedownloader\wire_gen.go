// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"github.com/dmagur/myheritage/internal/database"
	"github.com/dmagur/myheritage/internal/fetch"
	"github.com/dmagur/myheritage/internal/myheritage/dnamatch"
	"github.com/dmagur/myheritage/internal/myheritage/pedigree"
	"github.com/dmagur/myheritage/internal/myheritage/sharedmatches"
	"github.com/dmagur/myheritage/internal/myheritage/sharedsegments"
	"github.com/dmagur/myheritage/internal/myheritage/sharedsurnames"
	"github.com/google/wire"
	"github.com/sirupsen/logrus"
)

// Injectors from wire.go:

func CreateLocalApp(logger *logrus.Logger, baseDir ResponseBaseDir, connStr database.ConnString, bearerToken fetch.BearerToken) (App, error) {
	gormDB, err := database.NewGormDB(connStr)
	if err != nil {
		return App{}, err
	}
	myHeritageApiClient := fetch.NewMyHeritageClient(bearerToken, gormDB)
	repository := dnamatch.NewRepository(gormDB)
	handler := dnamatch.NewHandler(myHeritageApiClient, logger, repository)
	sharedmatchesRepository := sharedmatches.NewRepository(gormDB)
	sharedmatchesHandler := sharedmatches.NewHandler(myHeritageApiClient, logger, sharedmatchesRepository)
	sharedsurnamesRepository := sharedsurnames.NewRepository(gormDB)
	sharedsurnamesHandler := sharedsurnames.NewHandler(myHeritageApiClient, logger, sharedsurnamesRepository)
	sharedsegmentsRepository := sharedsegments.NewRepository(gormDB)
	sharedsegmentsHandler := sharedsegments.NewHandler(myHeritageApiClient, logger, sharedsegmentsRepository)
	pedigreeRepository := pedigree.NewRepository(gormDB)
	pedigreeHandler := pedigree.NewHandler(myHeritageApiClient, logger, pedigreeRepository)
	app := NewApp(handler, sharedmatchesHandler, sharedsurnamesHandler, sharedsegmentsHandler, pedigreeHandler, logger)
	return app, nil
}

func CreateRemoteApp(logger *logrus.Logger, connStr database.ConnString, bearerToken fetch.BearerToken) (App, error) {
	gormDB, err := database.NewGormDB(connStr)
	if err != nil {
		return App{}, err
	}
	myHeritageApiClient := fetch.NewMyHeritageClient(bearerToken, gormDB)
	repository := dnamatch.NewRepository(gormDB)
	handler := dnamatch.NewHandler(myHeritageApiClient, logger, repository)
	sharedmatchesRepository := sharedmatches.NewRepository(gormDB)
	sharedmatchesHandler := sharedmatches.NewHandler(myHeritageApiClient, logger, sharedmatchesRepository)
	sharedsurnamesRepository := sharedsurnames.NewRepository(gormDB)
	sharedsurnamesHandler := sharedsurnames.NewHandler(myHeritageApiClient, logger, sharedsurnamesRepository)
	sharedsegmentsRepository := sharedsegments.NewRepository(gormDB)
	sharedsegmentsHandler := sharedsegments.NewHandler(myHeritageApiClient, logger, sharedsegmentsRepository)
	pedigreeRepository := pedigree.NewRepository(gormDB)
	pedigreeHandler := pedigree.NewHandler(myHeritageApiClient, logger, pedigreeRepository)
	app := NewApp(handler, sharedmatchesHandler, sharedsurnamesHandler, sharedsegmentsHandler, pedigreeHandler, logger)
	return app, nil
}

// wire.go:

var (
	// DefaultSet is used for all database types
	DefaultSet = wire.NewSet(
		NewApp, database.NewGormDB, dnamatch.NewHandler, fetch.NewMyHeritageClient, dnamatch.NewRepository, sharedmatches.NewRepository, sharedmatches.NewHandler, sharedsurnames.NewHandler, sharedsurnames.NewRepository, sharedsegments.NewRepository, sharedsegments.NewHandler, pedigree.NewHandler, pedigree.NewRepository, wire.Bind(new(dnamatch.HandlerInterface), new(dnamatch.Handler)), wire.Bind(new(sharedmatches.HandlerInterface), new(sharedmatches.Handler)), wire.Bind(new(sharedsurnames.HandlerInterface), new(sharedsurnames.Handler)), wire.Bind(new(sharedsegments.HandlerInterface), new(sharedsegments.Handler)), wire.Bind(new(pedigree.HandlerInterface), new(pedigree.Handler)),
	)

	LocalSet = wire.NewSet(
		DefaultSet,
	)

	RemoteSet = wire.NewSet(
		DefaultSet,
	)
)
