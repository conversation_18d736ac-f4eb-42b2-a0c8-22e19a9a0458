package main

import (
	"database/sql"
	"fmt"
	"net/http"
	"os"
	"strings"

	"github.com/alexflint/go-arg"
	nested "github.com/antonfisher/nested-logrus-formatter"
	"github.com/dmagur/myheritage/apigen"
	"github.com/dmagur/myheritage/internal"
	"github.com/dmagur/myheritage/internal/api"
	"github.com/dmagur/myheritage/internal/args"
	"github.com/dmagur/myheritage/internal/database"
	"github.com/dmagur/myheritage/internal/dnamatch"
	"github.com/dmagur/myheritage/internal/logger"
	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	log "github.com/sirupsen/logrus"
)

const (
	version = "v1"
)

func init() {
	if err := godotenv.Load(); err != nil {
		log.Warn(".env could not be read. Using OS envs")
	}

	logger.InitLog()

	log.Infof("Application version '%s' is starting", version)
}

func main() {
	var args args.Args

	arg.MustParse(&args)

	logger := log.New()
	logger.SetFormatter(&nested.Formatter{})

	logger.SetLevel(log.DebugLevel)

	if args.TraceMode {
		logger.SetLevel(log.TraceLevel)
		logger.SetReportCaller(true)
	}

	if err := internal.GetRuntimeTimezone(logger); err != nil {
		logger.Panic(err)
	}

	// We're using our custom Swagger documentation instead of the generated one

	// Use the database factory to create a connection based on environment variables
	sqlDB := database.NewDB()

	// Skip database schema initialization as the database already exists
	logger.Info("Skipping database schema initialization as the database already exists")

	// Create a GORM database connection
	var connStr database.ConnString
	dbType := os.Getenv("DB_TYPE")
	if dbType == "mariadb" || dbType == "mysql" {
		// For MariaDB, we need to create a connection string that works with GORM
		host := os.Getenv("DB_HOST")
		if host == "" {
			host = "localhost"
		}
		user := os.Getenv("DB_USER")
		if user == "" {
			user = "myheritage"
		}
		password := os.Getenv("DB_PASSWORD")
		if password == "" {
			password = "mysecretpassword"
		}
		dbname := os.Getenv("DB_NAME")
		if dbname == "" {
			dbname = "myheritage"
		}
		connStr = database.ConnString(fmt.Sprintf("%s:%s@tcp(%s:3306)/%s", user, password, host, dbname))
	} else {
		// For PostgreSQL, use the standard connection string
		host := os.Getenv("DB_HOST")
		if host == "" {
			host = "my_postgres_db"
		}
		user := os.Getenv("DB_USER")
		if user == "" {
			user = "postgres"
		}
		password := os.Getenv("DB_PASSWORD")
		if password == "" {
			password = "postgres"
		}
		dbname := os.Getenv("DB_NAME")
		if dbname == "" {
			dbname = "myheritage"
		}
		connStr = database.ConnString(fmt.Sprintf("host=%s user=%s password=%s dbname=%s sslmode=disable", host, user, password, dbname))
	}

	gormDB, err := database.NewGormDB(connStr)
	if err != nil {
		logger.Fatalf("Failed to create GORM database connection: %v", err)
	}

	// Create a token provider to get the token from the database
	tokenProvider := api.NewTokenProvider(sqlDB, logger)

	// Get the token from the database or use the environment variable
	token := tokenProvider.GetToken()
	if token == "" {
		// No token in the database, use the environment variable
		token = os.Getenv("API_TOKEN")
		logger.Warnf("No token found in the database. Using environment variable.")
	}

	// Use the token for authorization
	_ = api.AuthToken(token) // We're using the token directly in the API implementation

	// Create a new Gin router
	router := gin.Default()

	// Configure CORS
	router.Use(cors.New(cors.Config{
		AllowOrigins:     []string{"*"},
		AllowMethods:     []string{"GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"},
		AllowHeaders:     []string{"Origin", "Content-Type", "Accept", "Authorization"},
		ExposeHeaders:    []string{"Content-Length"},
		AllowCredentials: true,
	}))

	// Create the API implementation
	dnaMatchRepo := dnamatch.NewRepository(sqlDB)
	apiImpl := api.NewAPI(logger, dnaMatchRepo)

	// Create the app
	app := &App{
		Engine:            router,
		APIImplementation: apiImpl,
	}

	// Register token management routes
	tokenHandler := api.NewTokenHandler(gormDB.DB, logger)
	tokenHandler.RegisterRoutes(router)

	// Register Swagger documentation with all endpoints
	router.GET("/swagger.json", api.CustomSwaggerJSON())
	router.GET("/swagger", api.CustomSwaggerUI())
	router.GET("/doc", api.CustomSwaggerUI())

	router.GET("/metrics", func(c *gin.Context) {
		h := promhttp.Handler()
		h.ServeHTTP(c.Writer, c.Request)
	})

	router.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status": "ok",
		})
	})

	router.GET("/liveness", func(c *gin.Context) { c.Data(http.StatusOK, "", []byte{}) })
	router.GET("/readiness", func(c *gin.Context) { c.Data(http.StatusOK, "", []byte{}) })

	apigen.RegisterHandlers(router, app.APIImplementation)

	basePath := fmt.Sprintf("/%s", version)
	publicGroup := router.Group(basePath)
	publicGroup.GET("/liveness", func(c *gin.Context) { c.Data(http.StatusOK, "", []byte{}) })
	publicGroup.GET("/readiness", func(c *gin.Context) { c.Data(http.StatusOK, "", []byte{}) })
	publicGroup.GET("/swagger.json", api.CustomSwaggerJSON())
	publicGroup.GET("/swagger", api.CustomSwaggerUI())
	publicGroup.GET("/doc", api.CustomSwaggerUI())

	publicGroup.GET("/metrics", func(c *gin.Context) {
		h := promhttp.Handler()
		h.ServeHTTP(c.Writer, c.Request)
	})

	port := api.CreatePortNumber(os.Getenv("PORT"))

	logger.Infof("Starting API server on port %s", port.GetFormatted())
	logger.Infof("API endpoints available at http://localhost%s", port.GetFormatted())
	logger.Infof("Token management endpoints available at http://localhost%s/api/v1/token", port.GetFormatted())

	err = router.Run(port.GetFormatted())
	if err != nil {
		panic(err)
	}
}

// initializeDatabase initializes the database schema for token management
func initializeDatabase(db *sql.DB) error {
	// Determine which schema file to use based on the database type
	dbType := os.Getenv("DB_TYPE")
	schemaPath := "internal/api/schema.sql" // Default to PostgreSQL schema

	if dbType == "mariadb" || dbType == "mysql" {
		schemaPath = "internal/api/schema_mariadb.sql"
	}

	// Read the schema file
	schema, err := os.ReadFile(schemaPath)
	if err != nil {
		return fmt.Errorf("failed to read schema file %s: %w", schemaPath, err)
	}

	// For MariaDB/MySQL, we need to execute each statement separately
	if dbType == "mariadb" || dbType == "mysql" {
		// Split the schema into individual statements
		// This is a simple implementation and might not handle all SQL syntax correctly
		statements := strings.Split(string(schema), ";")

		for _, stmt := range statements {
			// Skip empty statements
			stmt = strings.TrimSpace(stmt)
			if stmt == "" {
				continue
			}

			// Execute the statement
			_, err = db.Exec(stmt)
			if err != nil {
				return fmt.Errorf("failed to execute statement '%s': %w", stmt, err)
			}
		}

		return nil
	}

	// For PostgreSQL, we can execute the entire schema at once
	_, err = db.Exec(string(schema))
	if err != nil {
		return fmt.Errorf("failed to execute schema: %w", err)
	}

	return nil
}
