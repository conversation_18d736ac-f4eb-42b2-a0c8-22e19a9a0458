import React from 'react';
import { useQuery } from '@tanstack/react-query';
import {
  Box,
  Typography,
  Paper,
  Grid,
  CircularProgress,
  Alert,
  Button,
} from '@mui/material';
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  <PERSON><PERSON>hart,
  Pie,
  Cell,
  Legend,
} from 'recharts';

import { analyticsApi, MatchStats } from '../../services/api';

const DNAMatches: React.FC = () => {
  const {
    data: matchStats,
    isLoading,
    error,
    refetch,
  } = useQuery<MatchStats>({
    queryKey: ['matchStats'],
    queryFn: analyticsApi.getMatchStats,
  });

  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress size={60} />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert
        severity="error"
        action={<Button onClick={() => refetch()}>Retry</Button>}
      >
        Failed to load DNA match statistics.
      </Alert>
    );
  }

  const COLORS = ['#1976d2', '#388e3c', '#f57c00', '#7b1fa2', '#c2185b'];

  return (
    <Box>
      <Box mb={4}>
        <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
          DNA Match Analysis
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          Comprehensive analysis of DNA match data and relationships
        </Typography>
      </Box>

      {matchStats && (
        <Grid container spacing={3}>
          {/* Summary Stats */}
          <Grid item xs={12}>
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                Match Statistics Summary
              </Typography>
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6} md={3}>
                  <Box textAlign="center">
                    <Typography variant="h4" color="primary">
                      {matchStats.total_matches.toLocaleString()}
                    </Typography>
                    <Typography variant="body2">Total Matches</Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Box textAlign="center">
                    <Typography variant="h4" color="secondary">
                      {matchStats.avg_shared_cm.toFixed(1)}
                    </Typography>
                    <Typography variant="body2">Average cM</Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Box textAlign="center">
                    <Typography variant="h4" color="success.main">
                      {matchStats.max_shared_cm.toFixed(1)}
                    </Typography>
                    <Typography variant="body2">Maximum cM</Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Box textAlign="center">
                    <Typography variant="h4" color="warning.main">
                      {matchStats.min_shared_cm.toFixed(1)}
                    </Typography>
                    <Typography variant="body2">Minimum cM</Typography>
                  </Box>
                </Grid>
              </Grid>
            </Paper>
          </Grid>

          {/* Relationship Distribution */}
          <Grid item xs={12} md={6}>
            <Paper sx={{ p: 3, height: '400px' }}>
              <Typography variant="h6" gutterBottom>
                Relationship Distribution
              </Typography>
              <ResponsiveContainer width="100%" height="90%">
                <PieChart>
                  <Pie
                    data={Object.entries(matchStats.matches_by_relationship)
                      .filter(([key, value]) => key !== 'unknown' && value > 0)
                      .map(([relationship, count]) => ({
                        relationship: relationship === '' ? 'Unspecified' : relationship,
                        count,
                        percentage: (count / matchStats.total_matches) * 100
                      }))
                      .sort((a, b) => b.count - a.count)
                      .slice(0, 8) // Show top 8 relationships
                    }
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ relationship, percentage }) =>
                      `${relationship} (${percentage.toFixed(1)}%)`
                    }
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="count"
                  >
                    {Object.entries(matchStats.matches_by_relationship)
                      .filter(([key, value]) => key !== 'unknown' && value > 0)
                      .sort(([,a], [,b]) => b - a)
                      .slice(0, 8)
                      .map((_, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                  </Pie>
                  <Tooltip />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </Paper>
          </Grid>

          {/* cM Distribution */}
          <Grid item xs={12} md={6}>
            <Paper sx={{ p: 3, height: '400px' }}>
              <Typography variant="h6" gutterBottom>
                cM Range Distribution
              </Typography>
              <ResponsiveContainer width="100%" height="90%">
                <BarChart data={matchStats.cm_distribution.map(bucket => ({
                  range: `${bucket.min_cm}-${bucket.max_cm}`,
                  count: bucket.count,
                  min_cm: bucket.min_cm,
                  max_cm: bucket.max_cm
                }))}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="range" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="count" fill="#1976d2" />
                </BarChart>
              </ResponsiveContainer>
            </Paper>
          </Grid>
        </Grid>
      )}
    </Box>
  );
};

export default DNAMatches;
