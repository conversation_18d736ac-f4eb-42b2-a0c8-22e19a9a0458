import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';
import App from './App';
import reportWebVitals from './reportWebVitals';

// Suppress Three.js multiple instances warning and other console noise
const originalWarn = console.warn;
const originalLog = console.log;

console.warn = (...args) => {
  const message = args[0];
  if (message && typeof message === 'string' &&
      (message.includes('Multiple instances of Three.js') ||
       message.includes('WARNING: Multiple instances of Three.js') ||
       message.includes('THREE.') ||
       message.includes('three.js'))) {
    return; // Suppress Three.js warnings
  }
  originalWarn.apply(console, args);
};

console.log = (...args) => {
  const message = args[0];
  if (message && typeof message === 'string' &&
      (message.includes('WARNING: Multiple instances of Three.js') ||
       message.includes('THREE.'))) {
    return; // Suppress Three.js logs
  }
  originalLog.apply(console, args);
};

// Suppress browser extension runtime errors
window.addEventListener('error', (event) => {
  if (event.message && event.message.includes('runtime.lastError')) {
    event.preventDefault();
    return false;
  }
});

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);
root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals();
