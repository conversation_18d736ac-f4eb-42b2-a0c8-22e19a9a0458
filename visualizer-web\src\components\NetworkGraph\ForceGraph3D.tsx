import React, { useEffect, useRef } from 'react';
import ForceGraph3D from '3d-force-graph';
import { NetworkGraph as NetworkGraphData } from '../../services/api';

interface ForceGraph3DProps {
  data: NetworkGraphData;
  type: '3d' | '2d';
  width: string;
  height: string;
}

const ForceGraph3DComponent: React.FC<ForceGraph3DProps> = ({
  data,
  type,
  width,
  height,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const graphRef = useRef<any>(null);

  useEffect(() => {
    if (!containerRef.current || !data) {
      console.log('ForceGraph3D: Missing container or data', { containerRef: !!containerRef.current, data: !!data });
      return;
    }

    try {
      // Clear previous graph
      if (graphRef.current) {
        containerRef.current.innerHTML = '';
        graphRef.current = null;
      }

      console.log('ForceGraph3D: Processing data', {
        nodes: data.nodes?.length || 0,
        edges: data.edges?.length || 0,
        stats: data.stats
      });

      // Check if we have valid data
      if (!data.nodes || !data.edges || data.nodes.length === 0) {
        containerRef.current.innerHTML = `<div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #666;">
          <div style="text-align: center;">
            <h3>No Network Data</h3>
            <p>No nodes or connections found. Try adjusting the filters.</p>
            <p>Nodes: ${data.nodes?.length || 0}, Edges: ${data.edges?.length || 0}</p>
          </div>
        </div>`;
        return;
      }

      // Create a set of valid node IDs for validation
      const nodeIds = new Set(data.nodes.map(node => node.id));

      // Filter edges to only include those with valid source and target nodes
      const validEdges = data.edges.filter(edge => {
        const hasValidNodes = nodeIds.has(edge.source) && nodeIds.has(edge.target);
        if (!hasValidNodes) {
          console.warn('ForceGraph3D: Invalid edge found', edge);
        }
        return hasValidNodes;
      });

      console.log('ForceGraph3D: Filtered edges', {
        original: data.edges.length,
        valid: validEdges.length
      });

      // Prepare data for 3d-force-graph
      const graphData = {
        nodes: data.nodes.map(node => ({
          id: node.id,
          name: node.name || node.label || node.id,
          group: node.group || node.type || 'default',
          size: Math.max(1, node.size || 1),
          color: node.color || '#1976d2',
        })),
        links: validEdges.map(edge => ({
          source: edge.source,
          target: edge.target,
          value: Math.max(0.1, edge.value || edge.weight || 0),
          relationship: edge.relationship || 'unknown',
        })),
      };

      console.log('ForceGraph3D: Final graph data', {
        nodes: graphData.nodes.length,
        links: graphData.links.length
      });

      // Validate that we have both nodes and links
      if (graphData.nodes.length === 0) {
        containerRef.current.innerHTML = `<div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #666;">
          <div style="text-align: center;">
            <h3>No Nodes Found</h3>
            <p>The network data contains no valid nodes.</p>
          </div>
        </div>`;
        return;
      }

      // Create the graph with error handling
      console.log('ForceGraph3D: Creating 3D force graph...');
      const Graph = new ForceGraph3D(containerRef.current);

      // Set graph data first
      Graph.graphData(graphData);

      // Configure graph appearance and behavior
      Graph
        .nodeLabel('name')
        .nodeAutoColorBy('group')
        .nodeVal((node: any) => Math.max(1, node.size || 1))
        .linkLabel((link: any) => `${link.relationship}: ${link.value} cM`)
        .linkWidth((link: any) => Math.max(0.5, link.value / 1000)) // Adjusted scaling
        .linkOpacity(0.6)
        .onNodeClick((node: any) => {
          console.log('Node clicked:', node);
        })
        .onLinkClick((link: any) => {
          console.log('Link clicked:', link);
        })
        .backgroundColor('#f5f5f5')
        .showNavInfo(false);

      // Configure 3D specific settings
      if (type === '3d') {
        Graph
          .enableNodeDrag(true)
          .enableNavigationControls(true);
      }

      // Store reference
      graphRef.current = Graph;

      // Handle resize
      const handleResize = () => {
        if (graphRef.current && containerRef.current) {
          const rect = containerRef.current.getBoundingClientRect();
          graphRef.current.width(rect.width).height(rect.height);
        }
      };

      window.addEventListener('resize', handleResize);

      // Initial resize with delay to ensure DOM is ready
      setTimeout(() => {
        handleResize();
        console.log('ForceGraph3D: Graph initialized successfully');
      }, 100);

      return () => {
        window.removeEventListener('resize', handleResize);
        if (containerRef.current) {
          containerRef.current.innerHTML = '';
        }
      };
    } catch (error) {
      console.error('Error creating 3D force graph:', error);
      if (containerRef.current) {
        containerRef.current.innerHTML = `<div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #666;">
          <div>
            <h3>Error loading 3D graph</h3>
            <p>Please check the browser console for details.</p>
          </div>
        </div>`;
      }
    }
  }, [data, type]);

  return (
    <div
      ref={containerRef}
      style={{
        width: width,
        height: height,
        border: '1px solid #e0e0e0',
        borderRadius: '8px',
        overflow: 'hidden',
        position: 'relative',
      }}
    />
  );
};

export default ForceGraph3DComponent;
