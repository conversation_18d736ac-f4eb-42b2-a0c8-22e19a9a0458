//go:generate go run github.com/google/wire/cmd/wire ./...
//go:build wireinject

package main

import (
	"github.com/dmagur/myheritage/internal/database"
	"github.com/dmagur/myheritage/internal/fetch"
	"github.com/dmagur/myheritage/internal/myheritage/dnamatch"
	"github.com/dmagur/myheritage/internal/myheritage/pedigree"
	"github.com/dmagur/myheritage/internal/myheritage/sharedmatches"
	"github.com/dmagur/myheritage/internal/myheritage/sharedsegments"
	"github.com/dmagur/myheritage/internal/myheritage/sharedsurnames"
	"github.com/google/wire"
	"github.com/sirupsen/logrus"
)

var (
	// DefaultSet is used for all database types
	DefaultSet = wire.NewSet(
		NewApp,
		database.NewGormDB, // Use GORM-based database abstraction
		dnamatch.NewHandler,
		fetch.NewMyHeritageClient,
		dnamatch.NewRepository,
		sharedmatches.NewRepository,
		sharedmatches.NewHandler,
		sharedsurnames.NewHandler,
		sharedsurnames.NewRepository,
		sharedsegments.NewRepository,
		sharedsegments.NewHandler,
		pedigree.NewHandler,
		pedigree.NewRepository,

		wire.Bind(new(dnamatch.HandlerInterface), new(dnamatch.Handler)),
		wire.Bind(new(sharedmatches.HandlerInterface), new(sharedmatches.Handler)),
		wire.Bind(new(sharedsurnames.HandlerInterface), new(sharedsurnames.Handler)),
		wire.Bind(new(sharedsegments.HandlerInterface), new(sharedsegments.Handler)),
		wire.Bind(new(pedigree.HandlerInterface), new(pedigree.Handler)),
	)

	LocalSet = wire.NewSet(
		DefaultSet,
	)

	RemoteSet = wire.NewSet(
		DefaultSet,
	)
)

func CreateLocalApp(logger *logrus.Logger, baseDir ResponseBaseDir, connStr database.ConnString, bearerToken fetch.BearerToken) (App, error) {
	wire.Build(LocalSet)

	return App{}, nil
}

func CreateRemoteApp(logger *logrus.Logger, connStr database.ConnString, bearerToken fetch.BearerToken) (App, error) {
	wire.Build(RemoteSet)

	return App{}, nil
}
