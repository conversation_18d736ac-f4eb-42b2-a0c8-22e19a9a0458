package api

import (
	"database/sql"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	log "github.com/sirupsen/logrus"
)

// mockDBHandler implements the DBHandler interface for testing
type mockDBHandler struct {
	queryRowFunc func(query string, args ...interface{}) RowScanner
	beginFunc    func() (TxHandler, error)
}

func (m *mockDBHandler) QueryRow(query string, args ...interface{}) RowScanner {
	return m.queryRowFunc(query, args...)
}

func (m *mockDBHandler) Begin() (TxHandler, error) {
	return m.beginFunc()
}

// mockTxHandler implements the TxHandler interface for testing
type mockTxHandler struct {
	execFunc     func(query string, args ...interface{}) (sql.Result, error)
	commitFunc   func() error
	rollbackFunc func() error
}

func (m *mockTxHandler) Exec(query string, args ...interface{}) (sql.Result, error) {
	return m.execFunc(query, args...)
}

func (m *mockTxHandler) Commit() error {
	return m.commitFunc()
}

func (m *mockTxHandler) Rollback() error {
	return m.rollbackFunc()
}

// mockRowScanner implements the RowScanner interface for testing
type mockRowScanner struct {
	scanFunc func(dest ...interface{}) error
}

func (m *mockRowScanner) Scan(dest ...interface{}) error {
	return m.scanFunc(dest...)
}

// mockResult implements the sql.Result interface for testing
type mockResult struct {
	lastInsertIDFunc func() (int64, error)
	rowsAffectedFunc func() (int64, error)
}

func (m *mockResult) LastInsertId() (int64, error) {
	return m.lastInsertIDFunc()
}

func (m *mockResult) RowsAffected() (int64, error) {
	return m.rowsAffectedFunc()
}

func TestTokenHandler_GetToken_Success(t *testing.T) {
	// Create a mock row that returns a token
	row := &mockRowScanner{
		scanFunc: func(dest ...interface{}) error {
			// Set the token, created_at, and is_active values
			*dest[0].(*string) = "test-token-12345"
			*dest[1].(*time.Time) = time.Now()
			*dest[2].(*bool) = true
			return nil
		},
	}

	// Create a mock DB that returns the mock row
	db := &mockDBHandler{
		queryRowFunc: func(query string, args ...interface{}) RowScanner {
			return row
		},
	}

	// Create a logger
	logger := log.New()

	// Create a token handler with the mock DB
	handler := &TokenHandler{
		db:     db,
		logger: logger,
	}

	// Create a Gin router
	gin.SetMode(gin.TestMode)
	router := gin.New()
	router.GET("/api/v1/token", handler.GetToken)

	// Create a test request
	req, _ := http.NewRequest("GET", "/api/v1/token", nil)
	resp := httptest.NewRecorder()

	// Serve the request
	router.ServeHTTP(resp, req)

	// Check the response status code
	if resp.Code != http.StatusOK {
		t.Errorf("Expected status code %d, got %d", http.StatusOK, resp.Code)
	}

	// Parse the response body
	var response map[string]interface{}
	if err := json.Unmarshal(resp.Body.Bytes(), &response); err != nil {
		t.Fatalf("Failed to parse response body: %v", err)
	}

	// Check the token in the response
	token, ok := response["token"].(map[string]interface{})
	if !ok {
		t.Fatalf("Expected token in response, got %v", response)
	}

	// Check that the token is masked
	tokenValue, ok := token["token"].(string)
	if !ok {
		t.Fatalf("Expected token value in response, got %v", token)
	}

	if !strings.Contains(tokenValue, "...") {
		t.Errorf("Expected masked token, got '%s'", tokenValue)
	}
}

func TestTokenHandler_GetToken_NoRows(t *testing.T) {
	// Create a mock row that returns sql.ErrNoRows
	row := &mockRowScanner{
		scanFunc: func(dest ...interface{}) error {
			return sql.ErrNoRows
		},
	}

	// Create a mock DB that returns the mock row
	db := &mockDBHandler{
		queryRowFunc: func(query string, args ...interface{}) RowScanner {
			return row
		},
	}

	// Create a logger
	logger := log.New()

	// Create a token handler with the mock DB
	handler := &TokenHandler{
		db:     db,
		logger: logger,
	}

	// Create a Gin router
	gin.SetMode(gin.TestMode)
	router := gin.New()
	router.GET("/api/v1/token", handler.GetToken)

	// Create a test request
	req, _ := http.NewRequest("GET", "/api/v1/token", nil)
	resp := httptest.NewRecorder()

	// Serve the request
	router.ServeHTTP(resp, req)

	// Check the response status code
	if resp.Code != http.StatusOK {
		t.Errorf("Expected status code %d, got %d", http.StatusOK, resp.Code)
	}

	// Parse the response body
	var response map[string]interface{}
	if err := json.Unmarshal(resp.Body.Bytes(), &response); err != nil {
		t.Fatalf("Failed to parse response body: %v", err)
	}

	// Check the message in the response
	message, ok := response["message"].(string)
	if !ok {
		t.Fatalf("Expected message in response, got %v", response)
	}

	if message != "No active token found" {
		t.Errorf("Expected message 'No active token found', got '%s'", message)
	}
}

func TestTokenHandler_UpdateToken_Success(t *testing.T) {
	// Create a mock result
	result := &mockResult{
		rowsAffectedFunc: func() (int64, error) {
			return 1, nil
		},
	}

	// Create a mock transaction
	tx := &mockTxHandler{
		execFunc: func(query string, args ...interface{}) (sql.Result, error) {
			return result, nil
		},
		commitFunc: func() error {
			return nil
		},
		rollbackFunc: func() error {
			return nil
		},
	}

	// Create a mock DB that returns the mock transaction
	db := &mockDBHandler{
		beginFunc: func() (TxHandler, error) {
			return tx, nil
		},
	}

	// Create a logger
	logger := log.New()

	// Create a token handler with the mock DB
	handler := &TokenHandler{
		db:     db,
		logger: logger,
	}

	// Create a Gin router
	gin.SetMode(gin.TestMode)
	router := gin.New()
	router.POST("/api/v1/token", handler.UpdateToken)

	// Create a test request with a token
	reqBody := `{"token":"new-test-token"}`
	req, _ := http.NewRequest("POST", "/api/v1/token", strings.NewReader(reqBody))
	req.Header.Set("Content-Type", "application/json")
	resp := httptest.NewRecorder()

	// Serve the request
	router.ServeHTTP(resp, req)

	// Check the response status code
	if resp.Code != http.StatusOK {
		t.Errorf("Expected status code %d, got %d", http.StatusOK, resp.Code)
	}

	// Parse the response body
	var response map[string]interface{}
	if err := json.Unmarshal(resp.Body.Bytes(), &response); err != nil {
		t.Fatalf("Failed to parse response body: %v", err)
	}

	// Check the message in the response
	message, ok := response["message"].(string)
	if !ok {
		t.Fatalf("Expected message in response, got %v", response)
	}

	if message != "Token updated successfully" {
		t.Errorf("Expected message 'Token updated successfully', got '%s'", message)
	}
}

func TestTokenHandler_UpdateToken_InvalidRequest(t *testing.T) {
	// Create a logger
	logger := log.New()

	// Create a token handler with a nil DB (won't be used)
	handler := &TokenHandler{
		db:     nil, // Won't be used for this test
		logger: logger,
	}

	// Create a Gin router
	gin.SetMode(gin.TestMode)
	router := gin.New()
	router.POST("/api/v1/token", handler.UpdateToken)

	// Create a test request with an invalid body
	reqBody := `{"invalid":"json"`
	req, _ := http.NewRequest("POST", "/api/v1/token", strings.NewReader(reqBody))
	req.Header.Set("Content-Type", "application/json")
	resp := httptest.NewRecorder()

	// Serve the request
	router.ServeHTTP(resp, req)

	// Check the response status code
	if resp.Code != http.StatusBadRequest {
		t.Errorf("Expected status code %d, got %d", http.StatusBadRequest, resp.Code)
	}
}

func TestTokenHandler_UpdateToken_TokenTooShort(t *testing.T) {
	// Create a logger
	logger := log.New()

	// Create a token handler with a nil DB (won't be used)
	handler := &TokenHandler{
		db:     nil, // Won't be used for this test
		logger: logger,
	}

	// Create a Gin router
	gin.SetMode(gin.TestMode)
	router := gin.New()
	router.POST("/api/v1/token", handler.UpdateToken)

	// Create a test request with a token that's too short
	reqBody := `{"token":"short"}`
	req, _ := http.NewRequest("POST", "/api/v1/token", strings.NewReader(reqBody))
	req.Header.Set("Content-Type", "application/json")
	resp := httptest.NewRecorder()

	// Serve the request
	router.ServeHTTP(resp, req)

	// Check the response status code
	if resp.Code != http.StatusBadRequest {
		t.Errorf("Expected status code %d, got %d", http.StatusBadRequest, resp.Code)
	}

	// Parse the response body
	var response map[string]interface{}
	if err := json.Unmarshal(resp.Body.Bytes(), &response); err != nil {
		t.Fatalf("Failed to parse response body: %v", err)
	}

	// Check the error in the response
	errorMsg, ok := response["error"].(string)
	if !ok {
		t.Fatalf("Expected error in response, got %v", response)
	}

	if errorMsg != "Token is too short" {
		t.Errorf("Expected error 'Token is too short', got '%s'", errorMsg)
	}
}

func TestTokenHandler_UpdateToken_TransactionError(t *testing.T) {
	// Create a mock DB that returns an error on Begin
	db := &mockDBHandler{
		beginFunc: func() (TxHandler, error) {
			return nil, errors.New("transaction error")
		},
	}

	// Create a logger
	logger := log.New()

	// Create a token handler with the mock DB
	handler := &TokenHandler{
		db:     db,
		logger: logger,
	}

	// Create a Gin router
	gin.SetMode(gin.TestMode)
	router := gin.New()
	router.POST("/api/v1/token", handler.UpdateToken)

	// Create a test request with a token
	reqBody := `{"token":"valid-test-token"}`
	req, _ := http.NewRequest("POST", "/api/v1/token", strings.NewReader(reqBody))
	req.Header.Set("Content-Type", "application/json")
	resp := httptest.NewRecorder()

	// Serve the request
	router.ServeHTTP(resp, req)

	// Check the response status code
	if resp.Code != http.StatusInternalServerError {
		t.Errorf("Expected status code %d, got %d", http.StatusInternalServerError, resp.Code)
	}
}

func TestTokenHandler_UpdateToken_DeactivateError(t *testing.T) {
	// Create a mock transaction that returns an error on Exec
	tx := &mockTxHandler{
		execFunc: func(query string, args ...interface{}) (sql.Result, error) {
			return nil, errors.New("exec error")
		},
		rollbackFunc: func() error {
			return nil
		},
	}

	// Create a mock DB that returns the mock transaction
	db := &mockDBHandler{
		beginFunc: func() (TxHandler, error) {
			return tx, nil
		},
	}

	// Create a logger
	logger := log.New()

	// Create a token handler with the mock DB
	handler := &TokenHandler{
		db:     db,
		logger: logger,
	}

	// Create a Gin router
	gin.SetMode(gin.TestMode)
	router := gin.New()
	router.POST("/api/v1/token", handler.UpdateToken)

	// Create a test request with a token
	reqBody := `{"token":"valid-test-token"}`
	req, _ := http.NewRequest("POST", "/api/v1/token", strings.NewReader(reqBody))
	req.Header.Set("Content-Type", "application/json")
	resp := httptest.NewRecorder()

	// Serve the request
	router.ServeHTTP(resp, req)

	// Check the response status code
	if resp.Code != http.StatusInternalServerError {
		t.Errorf("Expected status code %d, got %d", http.StatusInternalServerError, resp.Code)
	}
}
