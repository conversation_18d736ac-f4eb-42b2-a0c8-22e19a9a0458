package database

import (
	"database/sql"

	_ "github.com/lib/pq"
)

// ConnString represents a database connection string
type ConnString string

// NewPgSql creates a new PostgreSQL connection with the given connection string
// This function is kept for backward compatibility with existing code that still uses raw SQL
// New code should use NewGormDB instead
func NewPgSql(connStr ConnString) *sql.DB {
	db, err := sql.Open("postgres", string(connStr))
	if err != nil {
		panic(err)
	}

	return db
}
