package database

import (
	"testing"
)

func TestNewPgSql(t *testing.T) {
	// This test is more of an integration test and requires a real database connection
	// For unit testing, we can just verify that the function doesn't panic with a valid connection string
	defer func() {
		if r := recover(); r != nil {
			t.<PERSON><PERSON>rf("NewPgSql panicked: %v", r)
		}
	}()

	// Create a mock connection string
	connStr := ConnString("user=postgres password=mysecretpassword dbname=dna_match_db sslmode=disable")

	// Call the NewPgSql function
	// This will attempt to open a connection, but we're not actually connecting to a database
	// We're just verifying that the function doesn't panic
	_ = NewPgSql(connStr)

	// Note: In a real test, we would close the connection to avoid resource leaks
	// but since we're not actually connecting to a database, we don't need to do that
}
