package api

import "time"

// OverviewResponse represents the database overview statistics
type OverviewResponse struct {
	TotalDNAMatches     int64     `json:"total_dna_matches"`
	TotalIndividuals    int64     `json:"total_individuals"`
	TotalTrees          int64     `json:"total_trees"`
	TotalSharedSegments int64     `json:"total_shared_segments"`
	TotalSubmitters     int64     `json:"total_submitters"`
	TotalFamilies       int64     `json:"total_families"`
	TotalSurnames       int64     `json:"total_surnames"`
	LastUpdated         time.Time `json:"last_updated"`
}

// MatchStatsResponse represents DNA match statistics
type MatchStatsResponse struct {
	TotalMatches          int64                  `json:"total_matches"`
	AvgSharedCM           float64                `json:"avg_shared_cm"`
	MaxSharedCM           float64                `json:"max_shared_cm"`
	MinSharedCM           float64                `json:"min_shared_cm"`
	MatchesByConfidence   map[string]int64       `json:"matches_by_confidence"`
	MatchesByRelationship map[string]int64       `json:"matches_by_relationship"`
	CMDistribution        []CMDistributionBucket `json:"cm_distribution"`
}

// CMDistributionBucket represents a bucket in the cM distribution
type CMDistributionBucket struct {
	MinCM float64 `json:"min_cm"`
	MaxCM float64 `json:"max_cm"`
	Count int64   `json:"count"`
}

// TopCountriesResponse represents the top countries by DNA matches
type TopCountriesResponse struct {
	Countries []CountryStats `json:"countries"`
	Total     int64          `json:"total"`
}

// CountryStats represents statistics for a specific country
type CountryStats struct {
	Country string `json:"country"`
	Count   int64  `json:"count"`
}

// IndividualStatsResponse represents individual demographics
type IndividualStatsResponse struct {
	TotalIndividuals     int64            `json:"total_individuals"`
	GenderDistribution   map[string]int64 `json:"gender_distribution"`
	AgeGroupDistribution map[string]int64 `json:"age_group_distribution"`
	TopSurnames          []SurnameStats   `json:"top_surnames"`
	IndividualsWithTrees int64            `json:"individuals_with_trees"`
}

// SurnameStats represents statistics for a surname
type SurnameStats struct {
	Surname string `json:"surname"`
	Count   int64  `json:"count"`
}

// NetworkGraphResponse represents network graph data for visualization
type NetworkGraphResponse struct {
	Nodes []NetworkNode `json:"nodes"`
	Edges []NetworkEdge `json:"edges"`
	Stats NetworkStats  `json:"stats"`
}

// NetworkNode represents a node in the network graph
type NetworkNode struct {
	ID    string `json:"id"`
	Label string `json:"label"`
	Type  string `json:"type"`
}

// NetworkEdge represents an edge in the network graph
type NetworkEdge struct {
	Source       string  `json:"source"`
	Target       string  `json:"target"`
	Weight       float64 `json:"weight"`
	Relationship string  `json:"relationship"`
}

// NetworkStats represents statistics about the network graph
type NetworkStats struct {
	NodeCount int     `json:"node_count"`
	EdgeCount int     `json:"edge_count"`
	MinCM     float64 `json:"min_cm"`
	Depth     int     `json:"depth"`
}

// NetworkCliquesResponse represents clique analysis data
type NetworkCliquesResponse struct {
	Cliques []NetworkClique `json:"cliques"`
	Stats   CliqueStats     `json:"stats"`
}

// NetworkClique represents a clique in the network
type NetworkClique struct {
	ID      int      `json:"id"`
	Members []string `json:"members"`
	Size    int      `json:"size"`
	AvgCM   float64  `json:"avg_cm"`
}

// CliqueStats represents statistics about cliques
type CliqueStats struct {
	TotalCliques int     `json:"total_cliques"`
	AvgSize      float64 `json:"avg_size"`
	MaxSize      int     `json:"max_size"`
	MinSize      int     `json:"min_size"`
}

// MatchesResponse represents filtered DNA matches
type MatchesResponse struct {
	Matches []MatchData `json:"matches"`
	Total   int64       `json:"total"`
	Page    int         `json:"page"`
	Limit   int         `json:"limit"`
}

// MatchData represents a DNA match record
type MatchData struct {
	ID           string  `json:"id"`
	Person1ID    string  `json:"person_1_id"`
	Person2ID    string  `json:"person_2_id"`
	SharedCM     float64 `json:"shared_cm"`
	Relationship string  `json:"relationship"`
	Confidence   string  `json:"confidence"`
}

// IndividualsResponse represents filtered individuals
type IndividualsResponse struct {
	Individuals []IndividualData `json:"individuals"`
	Total       int64            `json:"total"`
	Page        int              `json:"page"`
	Limit       int              `json:"limit"`
}

// IndividualData represents an individual record
type IndividualData struct {
	ID       string `json:"id"`
	TreeID   string `json:"tree_id"`
	Gender   string `json:"gender"`
	AgeGroup string `json:"age_group"`
	Country  string `json:"country"`
}

// SegmentsResponse represents filtered DNA segments
type SegmentsResponse struct {
	Segments []SegmentData `json:"segments"`
	Total    int64         `json:"total"`
	Page     int           `json:"page"`
	Limit    int           `json:"limit"`
}

// SegmentData represents a DNA segment record
type SegmentData struct {
	ID         string  `json:"id"`
	MatchID    string  `json:"match_id"`
	Chromosome string  `json:"chromosome"`
	StartPos   int64   `json:"start_pos"`
	EndPos     int64   `json:"end_pos"`
	Length     float64 `json:"length"`
}
