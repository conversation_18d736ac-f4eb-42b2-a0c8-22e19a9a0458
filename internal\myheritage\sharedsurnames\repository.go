package sharedsurnames

import (
	"fmt"
	"time"

	"github.com/dmagur/myheritage/internal/database"
)

type Repository struct {
	db *database.GormDB
}

type Result struct {
	ID                string
	MatchIndividualID string
}

func NewRepository(db *database.GormDB) *Repository {
	return &Repository{db}
}

func (r *Repository) GetDnaMatchesWithoutSharedSurnames(offset int, limit int) ([]Result, error) {
	// Slice to hold the results
	var dnaMatches []Result

	// Execute the query using GORM's query builder instead of raw SQL
	result := r.db.DB.Table("dna_matches").
		Select("id, match_individual_id").
		Where("link is not null and (surnames_count is null or places_count is null)").
		Order("total_shared_segments_length_in_cm desc").
		Offset(offset).
		Limit(limit).
		Scan(&dnaMatches)

	if result.Error != nil {
		return nil, result.Error
	}

	return dnaMatches, nil
}

func (r Repository) saveDnaMatcheSurnames(surnames []SurnameData, individualId string) (int, int, error) {

	surnameCount := 0
	for _, surname := range surnames {
		if surname.OtherSurnames == nil {
			continue
		}

		for _, otherSurname := range surname.OtherSurnames {
			surnameId, err := r.UpsertSurname(otherSurname)

			if err != nil {
				return 0, 0, err
			}

			_, err = r.UpsertIndividualSurname(surnameId, individualId)

			if err != nil {
				return 0, 0, err
			}

			surnameCount++
		}
	}

	placeCount := 0
	for _, surname := range surnames {
		if surname.OtherPlaces == nil {
			continue
		}

		for _, otherPlace := range surname.OtherPlaces {
			surnameId, err := r.UpsertPlace(otherPlace)

			if err != nil {
				return 0, 0, err
			}

			_, err = r.UpsertIndividualPlace(surnameId, individualId)

			if err != nil {
				return 0, 0, err
			}

			placeCount++
		}
	}

	return surnameCount, placeCount, nil
}

func (r *Repository) UpsertSurname(surname string) (int, error) {
	// Define the surname data
	surnameRecord := struct {
		ID      int    `gorm:"primaryKey;column:id"`
		Surname string `gorm:"column:surname"`
	}{
		Surname: surname,
	}

	// Try to find an existing record
	var existingRecord struct{ ID int }
	result := r.db.DB.Table("surname").
		Where("surname = ?", surname).
		First(&existingRecord)

	// If the record exists, return its ID
	if result.Error == nil {
		return existingRecord.ID, nil
	}

	// If the record doesn't exist, create a new one
	result = r.db.DB.Table("surname").Create(&surnameRecord)
	if result.Error != nil {
		fmt.Printf("surname: %s\n", surname)
		return 0, fmt.Errorf("failed to upsert surname: %v", result.Error)
	}

	return surnameRecord.ID, nil
}

func (r *Repository) UpsertIndividualSurname(surnameId int, individualId string) (int, error) {
	// Define the individual surname data
	individualSurname := struct {
		ID           int    `gorm:"primaryKey;column:id"`
		SurnameID    int    `gorm:"column:surname_id"`
		IndividualID string `gorm:"column:individual_id"`
	}{
		SurnameID:    surnameId,
		IndividualID: individualId,
	}

	// Try to find an existing record
	var existingRecord struct{ ID int }
	result := r.db.DB.Table("individual_surname").
		Where("surname_id = ? AND individual_id = ?", surnameId, individualId).
		First(&existingRecord)

	// If the record exists, return its ID
	if result.Error == nil {
		return existingRecord.ID, nil
	}

	// If the record doesn't exist, create a new one
	result = r.db.DB.Table("individual_surname").Create(&individualSurname)
	if result.Error != nil {
		return 0, fmt.Errorf("failed to upsert individual surname: %v", result.Error)
	}

	return individualSurname.ID, nil
}

func (r *Repository) updateSurnamesCount(matchId string, count int) error {
	currentTime := time.Now()

	// Update the record using GORM
	result := r.db.DB.Model(&database.DNAMatch{}).
		Where("id = ?", matchId).
		Updates(map[string]interface{}{
			"surnames_count": count,
			"updated_at":     currentTime.Format("2006-01-02 15:04:05"),
		})

	if result.Error != nil {
		return result.Error
	}

	return nil
}

func (r *Repository) UpsertPlace(place PlaceData) (int, error) {
	// Define the place data
	placeRecord := struct {
		ID                  int    `gorm:"primaryKey;column:id"`
		Country             string `gorm:"column:country"`
		StateOrProvince     string `gorm:"column:state_or_province"`
		CountryCode         string `gorm:"column:country_code"`
		StateOrProvinceCode string `gorm:"column:state_or_province_code"`
	}{
		Country:             place.Country,
		StateOrProvince:     place.StateOrProvince,
		CountryCode:         place.CountryCode,
		StateOrProvinceCode: place.StateOrProvinceCode,
	}

	// Try to find an existing record
	var existingRecord struct{ ID int }
	result := r.db.DB.Table("place").
		Where("country = ? AND state_or_province = ? AND country_code = ? AND state_or_province_code = ?",
			place.Country, place.StateOrProvince, place.CountryCode, place.StateOrProvinceCode).
		First(&existingRecord)

	// If the record exists, return its ID
	if result.Error == nil {
		return existingRecord.ID, nil
	}

	// If the record doesn't exist, create a new one
	result = r.db.DB.Table("place").Create(&placeRecord)
	if result.Error != nil {
		fmt.Printf("place: %v\n", place)
		return 0, fmt.Errorf("failed to upsert place: %v", result.Error)
	}

	return placeRecord.ID, nil
}

func (r *Repository) UpsertIndividualPlace(placeId int, individualId string) (int, error) {
	// Define the individual place data
	individualPlace := struct {
		ID           int    `gorm:"primaryKey;column:id"`
		PlaceID      int    `gorm:"column:place_id"`
		IndividualID string `gorm:"column:individual_id"`
	}{
		PlaceID:      placeId,
		IndividualID: individualId,
	}

	// Try to find an existing record
	var existingRecord struct{ ID int }
	result := r.db.DB.Table("individual_place").
		Where("place_id = ? AND individual_id = ?", placeId, individualId).
		First(&existingRecord)

	// If the record exists, return its ID
	if result.Error == nil {
		return existingRecord.ID, nil
	}

	// If the record doesn't exist, create a new one
	result = r.db.DB.Table("individual_place").Create(&individualPlace)
	if result.Error != nil {
		return 0, fmt.Errorf("failed to upsert individual place: %v", result.Error)
	}

	return individualPlace.ID, nil
}

func (r *Repository) updatePlacesCount(matchId string, count int) error {
	currentTime := time.Now()

	// Update the record using GORM
	result := r.db.DB.Model(&database.DNAMatch{}).
		Where("id = ?", matchId).
		Updates(map[string]interface{}{
			"places_count": count,
			"updated_at":   currentTime.Format("2006-01-02 15:04:05"),
		})

	if result.Error != nil {
		return result.Error
	}

	return nil
}
