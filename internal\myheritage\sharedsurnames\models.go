package sharedsurnames

type ApiResponse struct {
	Data DNAData `json:"data"`
}

type DNAData struct {
	DNAMatch DNAMatch `json:"dna_match"`
}

type DNAMatch struct {
	SurnameMatches SurnameMatches `json:"surname_matches"`
	SurnameList    SurnameList    `json:"surname_list"`
}

type SurnameMatches struct {
	Count int                `json:"count"`
	Data  []SurnameMatchData `json:"data"`
}

type SurnameMatchData struct {
	Surnames                 []string     `json:"surnames"`
	IndividualAncestors      AncestorData `json:"individual_ancestors"`
	OtherIndividualAncestors AncestorData `json:"other_individual_ancestors"`
}

type AncestorData struct {
	Count int            `json:"count"`
	Data  []AncestorInfo `json:"data"`
}

type AncestorInfo struct {
	RelationshipDescription string     `json:"relationship_description"`
	Individual              Individual `json:"individual"`
}

type Individual struct {
	ID            string      `json:"id"`
	Name          string      `json:"name"`
	Gender        string      `json:"gender"`
	AgeGroup      string      `json:"age_group"`
	PersonalPhoto interface{} `json:"personal_photo"`
}

type SurnameList struct {
	Count int           `json:"count"`
	Data  []SurnameData `json:"data"`
}

type SurnameData struct {
	Surnames      []string    `json:"surnames"`
	OtherSurnames []string    `json:"other_surnames"`
	Places        []PlaceData `json:"places,omitempty"`
	OtherPlaces   []PlaceData `json:"other_places,omitempty"`
}

type PlaceData struct {
	Country             string `json:"country"`
	StateOrProvince     string `json:"state_or_province"`
	CountryCode         string `json:"country_code"`
	StateOrProvinceCode string `json:"state_or_province_code"`
}

func main() {
	// Example usage of the structs
}
