<head>
    <style> body { margin: 0; } </style>

    <script src="3d-force-graph.min.js"></script>
    <!--<script src="../../dist/3d-force-graph.js"></script>-->
</head>

<body>
<div id="3d-graph"></div>

<script>
    const gData = {
        "nodes": [
            {
                "id": "Віктор Заноха",
                "group": "individual-1592334192-1500079"
            }
        ],
        "links": [
            {
                "source": "<PERSON>",
                "target": "<PERSON>",
                "value": 13
            }
        ]
    }

    const Graph = ForceGraph3D()
    (document.getElementById('3d-graph'))
        .graphData(gData)
        .nodeLabel('id')
        .nodeAutoColorBy('group');
</script>
</body>