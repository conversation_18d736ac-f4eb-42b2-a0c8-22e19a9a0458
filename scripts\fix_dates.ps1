# PowerShell script to replace invalid dates in SQL file

# Parameters
param (
    [Parameter(Mandatory=$false)]
    [string]$InputFilePath = "data\dna_matches-insert.sql",
    
    [Parameter(Mandatory=$false)]
    [string]$OutputFilePath = "data\dna_matches-fixed-dates.sql",
    
    [Parameter(Mandatory=$false)]
    [string]$OldDate = "'0001-01-01 00:00:00'",
    
    [Parameter(Mandatory=$false)]
    [string]$NewDate = "'2024-01-01 00:00:00'"
)

# Check if the input file exists
if (-not (Test-Path $InputFilePath)) {
    Write-Host "Error: Input file not found: $InputFilePath" -ForegroundColor Red
    exit
}

$fileSize = (Get-Item $InputFilePath).Length
Write-Host "Input file found. File size: $fileSize bytes"

# Process the file in streaming mode to handle large files
$reader = [System.IO.File]::OpenText($InputFilePath)
$writer = [System.IO.File]::CreateText($OutputFilePath)

try {
    $lineCount = 0
    $replacementCount = 0
    
    # Process the file line by line
    while (($line = $reader.ReadLine()) -ne $null) {
        $lineCount++
        
        # Show progress every 100,000 lines
        if ($lineCount % 100000 -eq 0) {
            Write-Host "Processed $lineCount lines, replaced $replacementCount occurrences..."
        }
        
        # Replace the date
        $newLine = $line
        $occurrences = ([regex]::Matches($line, [regex]::Escape($OldDate))).Count
        if ($occurrences -gt 0) {
            $newLine = $line -replace [regex]::Escape($OldDate), $NewDate
            $replacementCount += $occurrences
        }
        
        # Write the line to the output file
        $writer.WriteLine($newLine)
    }
    
    Write-Host "Replacement completed! Processed $lineCount lines, replaced $replacementCount occurrences."
    Write-Host "The fixed SQL file is available at: $OutputFilePath"
} finally {
    $reader.Close()
    $writer.Close()
}
