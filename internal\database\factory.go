package database

import (
	"database/sql"
)

// NewDB creates a new database connection based on the DB_TYPE environment variable
func NewDB() *sql.DB {
	dbType := getEnv("DB_TYPE", "mariadb")

	switch dbType {
	case "mariadb", "mysql":
		return NewMariaDB()
	case "postgres", "postgresql":
		connStr := ConnString(getPostgresConnString())
		return NewPgSql(connStr)
	default:
		// Default to MariaDB
		return NewMariaDB()
	}
}

// getPostgresConnString returns the PostgreSQL connection string from environment variables or a default value
func getPostgresConnString() string {
	host := getEnv("DB_HOST", "my_postgres_db")
	user := getEnv("DB_USER", "postgres")
	password := getEnv("DB_PASSWORD", "mysecretpassword")
	dbname := getEnv("DB_NAME", "dna_match_db")

	return "host=" + host + " user=" + user + " password=" + password + " dbname=" + dbname + " sslmode=disable"
}
