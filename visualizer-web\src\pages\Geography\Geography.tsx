import React from 'react';
import { useQuery } from '@tanstack/react-query';
import {
  Box,
  Typography,
  Paper,
  Grid,
  CircularProgress,
  Alert,
  Button,
} from '@mui/material';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from 'recharts';

import { analyticsApi, CountryStats } from '../../services/api';

const Geography: React.FC = () => {
  const {
    data: countryData,
    isLoading,
    error,
    refetch,
  } = useQuery<CountryStats[]>({
    queryKey: ['topCountries'],
    queryFn: () => analyticsApi.getTopCountries(15),
  });

  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress size={60} />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert
        severity="error"
        action={<Button onClick={() => refetch()}>Retry</Button>}
      >
        Failed to load geographic data.
      </Alert>
    );
  }

  return (
    <Box>
      <Box mb={4}>
        <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
          Geographic Distribution
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          Global distribution of DNA matches and individuals
        </Typography>
      </Box>

      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Paper sx={{ p: 3, height: '500px' }}>
            <Typography variant="h6" gutterBottom>
              Top Countries by Match Count
            </Typography>
            {countryData && (
              <ResponsiveContainer width="100%" height="90%">
                <BarChart data={countryData} layout="horizontal">
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis type="number" />
                  <YAxis dataKey="country" type="category" width={100} />
                  <Tooltip />
                  <Bar dataKey="count" fill="#1976d2" />
                </BarChart>
              </ResponsiveContainer>
            )}
          </Paper>
        </Grid>

        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Alert severity="info">
              Interactive world map visualization is under development. 
              This will include heat maps, migration patterns, and regional clustering.
            </Alert>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Geography;
