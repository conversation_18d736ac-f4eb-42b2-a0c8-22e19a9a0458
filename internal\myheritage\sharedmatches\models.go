package sharedmatches

import (
	"encoding/json"
)

// Define the structs

type ApiResponse struct {
	Data DNAData `json:"data"`
}

type DNAData struct {
	DnaMatch DnaMatchData `json:"dna_match"`
}

type DnaMatchData struct {
	DnaSharedMatches DnaSharedMatches `json:"dna_shared_matches"`
}

type DnaSharedMatches struct {
	Count int                     `json:"count"`
	Data  []DnaSharedMatchDetails `json:"data"`
}

type DnaSharedMatchDetails struct {
	DnaMatchesClusterSharedSegmentsCount int              `json:"dna_matches_cluster_shared_segments_count"`
	IsObfuscated                         bool             `json:"is_obfuscated"`
	SharedMember                         SharedMember     `json:"shared_member"`
	SharedIndividual                     SharedIndividual `json:"shared_individual"`
	DnaMatch                             DnaMatch         `json:"dna_match"`
	OtherDnaMatch                        OtherDnaMatch    `json:"other_dna_match"`
	SharedProfilePersonalImage           *PersonalPhoto   `json:"shared_profile_personal_image"`
}

type SharedMember struct {
	PersonalPhoto *PersonalPhoto `json:"personal_photo"`
	AgeGroup      string         `json:"age_group"`
	Gender        string         `json:"gender"`
	Name          string         `json:"name"`
	ID            string         `json:"id"`
}

type SharedIndividual struct {
	PersonalPhoto      *PersonalPhoto `json:"personal_photo"`
	AgeGroup           string         `json:"age_group"`
	Gender             string         `json:"gender"`
	Name               string         `json:"name"`
	ID                 string         `json:"id"`
	LinkInPedigreeTree string         `json:"link_in_pedigree_tree"`
	Tree               Tree           `json:"tree"`
}

type PersonalPhoto struct {
	Thumbnails []Thumbnail `json:"thumbnails"`
}

type Thumbnail struct {
	URL string `json:"url"`
}

type Tree struct {
	IndividualCount int  `json:"individual_count"`
	Site            Site `json:"site"`
}

type Site struct {
	Creator Creator `json:"creator"`
}

type Creator struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	CountryCode string `json:"country_code"`
}

type DnaMatch struct {
	ID                            string            `json:"id"`
	RefinedDnaRelationships       []DnaRelationship `json:"refined_dna_relationships"`
	ExactDnaRelationship          *string           `json:"exact_dna_relationship"`
	PercentageOfSharedSegments    float64           `json:"percentage_of_shared_segments"`
	TotalSharedSegmentsLengthInCm float64           `json:"total_shared_segments_length_in_cm"`
	Link                          string            `json:"link"`
	DnaCmExplainer                DnaCmExplainer    `json:"dna_cm_explainer"`
	IsFavorite                    bool              `json:"is_favorite"`
	MatchNotes                    *json.RawMessage  `json:"match_notes"`  // assuming it can be any JSON structure
	MatchLabels                   *json.RawMessage  `json:"match_labels"` // assuming it can be any JSON structure
}

type OtherDnaMatch struct {
	ID                            string            `json:"id"`
	RefinedDnaRelationships       []DnaRelationship `json:"refined_dna_relationships"`
	ExactDnaRelationship          *string           `json:"exact_dna_relationship"`
	PercentageOfSharedSegments    float64           `json:"percentage_of_shared_segments"`
	TotalSharedSegmentsLengthInCm float64           `json:"total_shared_segments_length_in_cm"`
	DnaCmExplainer                DnaCmExplainer    `json:"dna_cm_explainer"`
}

type DnaRelationship struct {
	RelationshipType   int    `json:"relationship_type"`
	RelationshipDegree string `json:"relationship_degree"`
}

type DnaCmExplainer struct {
	MostProbableRelationships []ProbableRelationship `json:"most_probable_relationships"`
	CalculationStrategy       string                 `json:"calculation_strategy"`
}

type ProbableRelationship struct {
	RelationshipType                          int     `json:"relationship_type"`
	RelationshipClass                         string  `json:"relationship_class"`
	PathType                                  string  `json:"path_type"`
	Probability                               float64 `json:"probability"`
	MostRecentCommonAncestorRelationshipType  int     `json:"most_recent_common_ancestor_relationship_type"`
	MostRecentCommonAncestorRelationshipClass string  `json:"most_recent_common_ancestor_relationship_class"`
}
