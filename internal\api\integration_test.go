package api_test

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"testing"
)

// TestTokenAPIIntegration tests the token API endpoints
// This is an integration test that requires the token API server to be running
// Set the environment variable RUN_INTEGRATION_TESTS=true to run this test
func TestTokenAPIIntegration(t *testing.T) {
	// Skip if not running integration tests
	if os.Getenv("RUN_INTEGRATION_TESTS") != "true" {
		t.Skip("Skipping integration tests. Set RUN_INTEGRATION_TESTS=true to run")
	}

	// Check if the server is already running
	_, err := http.Get("http://localhost:8080/health")
	if err != nil {
		// Server is not running, so we'll skip the tests
		t.<PERSON>p("Token API server is not running. Please start it with 'go run cmd/tokenapi/main.go' before running the tests.")
	} else {
		t.Log("Server is already running")
	}

	// Base URL for the API
	baseURL := "http://localhost:8080"

	// Test the health endpoint
	t.Run("Health", func(t *testing.T) {
		resp, err := http.Get(fmt.Sprintf("%s/health", baseURL))
		if err != nil {
			t.Fatalf("Failed to get health: %v", err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			t.Errorf("Expected status code %d, got %d", http.StatusOK, resp.StatusCode)
		}

		var response map[string]interface{}
		if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
			t.Fatalf("Failed to parse response body: %v", err)
		}

		status, ok := response["status"].(string)
		if !ok {
			t.Fatalf("Expected status in response, got %v", response)
		}

		if status != "ok" {
			t.Errorf("Expected status 'ok', got '%s'", status)
		}
	})

	// Test getting the current token state
	t.Run("GetCurrentTokenState", func(t *testing.T) {
		resp, err := http.Get(fmt.Sprintf("%s/api/v1/token", baseURL))
		if err != nil {
			t.Fatalf("Failed to get token: %v", err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			t.Errorf("Expected status code %d, got %d", http.StatusOK, resp.StatusCode)
		}

		var response map[string]interface{}
		if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
			t.Fatalf("Failed to parse response body: %v", err)
		}

		// Check if we have a token or a message
		if _, ok := response["token"]; ok {
			t.Log("Token exists in the database")
		} else if message, ok := response["message"].(string); ok {
			if message != "No active token found" {
				t.Errorf("Expected message 'No active token found', got '%s'", message)
			}
		} else {
			t.Fatalf("Expected token or message in response, got %v", response)
		}
	})

	// Test creating a token
	t.Run("CreateToken", func(t *testing.T) {
		reqBody := map[string]string{
			"token": "my-test-token-12345",
		}
		reqBodyBytes, err := json.Marshal(reqBody)
		if err != nil {
			t.Fatalf("Failed to marshal request body: %v", err)
		}

		resp, err := http.Post(
			fmt.Sprintf("%s/api/v1/token", baseURL),
			"application/json",
			bytes.NewBuffer(reqBodyBytes),
		)
		if err != nil {
			t.Fatalf("Failed to create token: %v", err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			t.Errorf("Expected status code %d, got %d", http.StatusOK, resp.StatusCode)
		}

		var response map[string]interface{}
		if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
			t.Fatalf("Failed to parse response body: %v", err)
		}

		message, ok := response["message"].(string)
		if !ok {
			t.Fatalf("Expected message in response, got %v", response)
		}

		if message != "Token updated successfully" {
			t.Errorf("Expected message 'Token updated successfully', got '%s'", message)
		}
	})

	// Test getting the token after creating it
	t.Run("GetTokenAfterCreating", func(t *testing.T) {
		resp, err := http.Get(fmt.Sprintf("%s/api/v1/token", baseURL))
		if err != nil {
			t.Fatalf("Failed to get token: %v", err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			t.Errorf("Expected status code %d, got %d", http.StatusOK, resp.StatusCode)
		}

		var response map[string]interface{}
		if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
			t.Fatalf("Failed to parse response body: %v", err)
		}

		tokenObj, ok := response["token"].(map[string]interface{})
		if !ok {
			t.Fatalf("Expected token in response, got %v", response)
		}

		tokenValue, ok := tokenObj["token"].(string)
		if !ok {
			t.Fatalf("Expected token value in response, got %v", tokenObj)
		}

		// Check that the token is masked
		if tokenValue != "my-te...12345" {
			t.Errorf("Expected masked token 'my-te...12345', got '%s'", tokenValue)
		}

		isActive, ok := tokenObj["is_active"].(bool)
		if !ok {
			t.Fatalf("Expected is_active in response, got %v", tokenObj)
		}

		if !isActive {
			t.Errorf("Expected is_active to be true, got %v", isActive)
		}
	})

	// Test updating the token
	t.Run("UpdateToken", func(t *testing.T) {
		reqBody := map[string]string{
			"token": "my-new-token-67890",
		}
		reqBodyBytes, err := json.Marshal(reqBody)
		if err != nil {
			t.Fatalf("Failed to marshal request body: %v", err)
		}

		resp, err := http.Post(
			fmt.Sprintf("%s/api/v1/token", baseURL),
			"application/json",
			bytes.NewBuffer(reqBodyBytes),
		)
		if err != nil {
			t.Fatalf("Failed to update token: %v", err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			t.Errorf("Expected status code %d, got %d", http.StatusOK, resp.StatusCode)
		}

		var response map[string]interface{}
		if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
			t.Fatalf("Failed to parse response body: %v", err)
		}

		message, ok := response["message"].(string)
		if !ok {
			t.Fatalf("Expected message in response, got %v", response)
		}

		if message != "Token updated successfully" {
			t.Errorf("Expected message 'Token updated successfully', got '%s'", message)
		}
	})

	// Test getting the token after updating it
	t.Run("GetTokenAfterUpdating", func(t *testing.T) {
		resp, err := http.Get(fmt.Sprintf("%s/api/v1/token", baseURL))
		if err != nil {
			t.Fatalf("Failed to get token: %v", err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			t.Errorf("Expected status code %d, got %d", http.StatusOK, resp.StatusCode)
		}

		var response map[string]interface{}
		if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
			t.Fatalf("Failed to parse response body: %v", err)
		}

		tokenObj, ok := response["token"].(map[string]interface{})
		if !ok {
			t.Fatalf("Expected token in response, got %v", response)
		}

		tokenValue, ok := tokenObj["token"].(string)
		if !ok {
			t.Fatalf("Expected token value in response, got %v", tokenObj)
		}

		// Check that the token is masked
		if tokenValue != "my-ne...67890" {
			t.Errorf("Expected masked token 'my-ne...67890', got '%s'", tokenValue)
		}

		isActive, ok := tokenObj["is_active"].(bool)
		if !ok {
			t.Fatalf("Expected is_active in response, got %v", tokenObj)
		}

		if !isActive {
			t.Errorf("Expected is_active to be true, got %v", isActive)
		}
	})

	// Test updating the token with an invalid value (too short)
	t.Run("UpdateTokenWithInvalidValue", func(t *testing.T) {
		reqBody := map[string]string{
			"token": "short",
		}
		reqBodyBytes, err := json.Marshal(reqBody)
		if err != nil {
			t.Fatalf("Failed to marshal request body: %v", err)
		}

		resp, err := http.Post(
			fmt.Sprintf("%s/api/v1/token", baseURL),
			"application/json",
			bytes.NewBuffer(reqBodyBytes),
		)
		if err != nil {
			t.Fatalf("Failed to update token: %v", err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusBadRequest {
			t.Errorf("Expected status code %d, got %d", http.StatusBadRequest, resp.StatusCode)
		}

		var response map[string]interface{}
		if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
			t.Fatalf("Failed to parse response body: %v", err)
		}

		errorMsg, ok := response["error"].(string)
		if !ok {
			t.Fatalf("Expected error in response, got %v", response)
		}

		if errorMsg != "Token is too short" {
			t.Errorf("Expected error 'Token is too short', got '%s'", errorMsg)
		}
	})

	// Test updating the token with an invalid JSON
	t.Run("UpdateTokenWithInvalidJSON", func(t *testing.T) {
		reqBody := "{invalid json}"
		resp, err := http.Post(
			fmt.Sprintf("%s/api/v1/token", baseURL),
			"application/json",
			bytes.NewBufferString(reqBody),
		)
		if err != nil {
			t.Fatalf("Failed to update token: %v", err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusBadRequest {
			t.Errorf("Expected status code %d, got %d", http.StatusBadRequest, resp.StatusCode)
		}

		var response map[string]interface{}
		if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
			t.Fatalf("Failed to parse response body: %v", err)
		}

		errorMsg, ok := response["error"].(string)
		if !ok {
			t.Fatalf("Expected error in response, got %v", response)
		}

		if errorMsg != "Invalid request format" {
			t.Errorf("Expected error 'Invalid request format', got '%s'", errorMsg)
		}
	})
}
