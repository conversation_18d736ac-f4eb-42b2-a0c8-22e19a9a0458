# MyHeritage API Server

This repository contains the API server for the MyHeritage application.

## Features

- DNA Matches API
- Token Management API
- Health, Liveness, and Readiness endpoints
- Metrics endpoint
- API Documentation (Swagger)

## Getting Started

### Prerequisites

- Go 1.16 or higher
- PostgreSQL database

### Environment Variables

The following environment variables can be set:

- `PORT`: The port to run the API server on (default: 1231)
- `API_TOKEN`: The API token to use if no token is found in the database

### Running the API Server

#### Option 1: Run directly with Go

```bash
# Run the API server
go run cmd/apiserver/main.go cmd/apiserver/app.go
```

#### Option 2: Run with Docker

```bash
# Windows
.\scripts\run_docker.ps1

# Linux/Mac
./scripts/run_docker.sh
```

Or manually with Docker Compose:

```bash
# Build and start the containers
docker-compose up -d --build

# Check the logs
docker-compose logs -f apiserver

# Stop the containers
docker-compose down
```

The API server will be available at http://localhost:1231.

## API Endpoints

### DNA Matches API

- `GET /dna-matches`: Get all DNA matches

### Token Management API

- `GET /api/v1/token`: Get the current API token
- `POST /api/v1/token`: Update the API token

### Health, Liveness, and Readiness Endpoints

- `GET /health`: Health check endpoint
- `GET /liveness`: Liveness check endpoint
- `GET /readiness`: Readiness check endpoint

### Metrics Endpoint

- `GET /metrics`: Prometheus metrics endpoint

### API Documentation

- `GET /doc`: API documentation
- `GET /swagger`: Swagger UI

## Testing

### Running Unit Tests

```bash
# Run all unit tests
go test ./...

# Run specific unit tests
go test -v ./internal/api -run TestTokenHandler
```

### Running Integration Tests

```bash
# Run integration tests (requires the API server to be running)
# Windows
.\scripts\run_api_tests.ps1

# Linux/Mac
./scripts/run_api_tests.sh
```

## Development

### Project Structure

- `cmd/apiserver`: API server entry point
- `internal/api`: API implementation
- `internal/dnamatch`: DNA match repository
- `internal/database`: Database utilities
- `scripts`: Utility scripts

### Adding a New API Endpoint

1. Define the endpoint in the OpenAPI specification
2. Generate the API code using the OpenAPI generator
3. Implement the endpoint in the appropriate package
4. Add tests for the endpoint

### Database Schema

The database schema is defined in `internal/api/schema.sql`. This schema is automatically applied when the API server starts.

## Useful Commands

```bash
# Dump the database
docker-compose exec db bash
pg_dump --dbname=dna_match_db --schema=public --file=./root/data/dna_match_db-2024_11_22-dump.sql --username=postgres

pg_dump --dbname=dna_match_db --schema=public --file=./root/data/dna_matches-dump.sql --username=postgres --table=dna_matches

# Generate OpenAPI code
docker run --rm -v ./:/local openapitools/openapi-generator-cli generate -c /local/openapi-yaml.yml -g openapi-yaml -i /local/oapi/openapi.yml -o /local/doc
oapi-codegen --config oapi-config.yml doc/openapi.yml
```

mysql -h ************* -u myheritage -p'Xq(RDl*5C7lkocf1' myheritage -e 'source /data/dna_matches-insert.sql'