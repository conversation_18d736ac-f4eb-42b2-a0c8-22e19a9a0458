# Analytics Endpoints Optimization Guide

## Current Performance Baseline

Based on testing with a large dataset (1.4M+ DNA matches, 313K+ individuals), here are the current response times:

| Endpoint | Current Response Time | Status | Priority |
|----------|----------------------|--------|----------|
| Overview | 36.7s | 🔴 Slow | High |
| DNA Match Stats | 27.8s | 🔴 Slow | High |
| Individual Stats | 12.4s | 🟡 Medium | Medium |
| Top Countries | 0.8s | 🟢 Fast | Low |

## Optimization Strategies

### 1. Database Indexing 🚀

**High Impact, Low Effort**

Add strategic indexes to improve query performance:

```sql
-- For DNA matches queries
CREATE INDEX idx_dna_matches_cm ON dna_matches(total_shared_segments_length_in_cm);
CREATE INDEX idx_dna_matches_confidence ON dna_matches(confidence_level);
CREATE INDEX idx_dna_matches_relationship ON dna_matches(exact_dna_relationship);

-- For individuals queries
CREATE INDEX idx_individuals_gender ON individuals(gender);
CREATE INDEX idx_individuals_age_group ON individuals(age_group);
CREATE INDEX idx_individuals_tree_id ON individuals(tree_id);

-- For trees queries
CREATE INDEX idx_trees_country ON trees(site_creator_country);

-- For shared segments
CREATE INDEX idx_shared_segment_match_id ON shared_segment(match_id);

-- For families and surnames
CREATE INDEX idx_family_tree_id ON family(tree_id);
CREATE INDEX idx_surname_tree_id ON surname(tree_id);
```

**Expected Impact**: 50-80% reduction in query time

### 2. Redis Caching 🔄

**High Impact, Medium Effort**

Implement Redis caching for expensive queries:

```go
// Cache configuration
type CacheConfig struct {
    OverviewTTL     time.Duration // 1 hour
    MatchStatsTTL   time.Duration // 30 minutes
    CountriesTTL    time.Duration // 6 hours
    IndividualsTTL  time.Duration // 2 hours
}

// Cache keys
const (
    CacheKeyOverview    = "analytics:overview"
    CacheKeyMatchStats  = "analytics:match_stats"
    CacheKeyCountries   = "analytics:countries"
    CacheKeyIndividuals = "analytics:individuals"
)
```

**Implementation Priority**:
1. Overview endpoint (highest impact)
2. DNA Match Stats
3. Individual Stats
4. Top Countries (already fast)

**Expected Impact**: 95%+ reduction in response time for cached requests

### 3. Background Job Pre-computation 🔧

**High Impact, High Effort**

Create scheduled jobs to pre-calculate statistics:

```go
// Cron jobs for statistics calculation
// Run every hour for overview stats
// Run every 30 minutes for match stats
// Run every 6 hours for geographic data
// Run every 2 hours for individual stats

type StatisticsJob struct {
    scheduler *cron.Cron
    db        *gorm.DB
    cache     *redis.Client
}

func (s *StatisticsJob) CalculateOverviewStats() {
    // Pre-calculate and store in cache
    // Update database summary table if needed
}
```

**Expected Impact**: Sub-second response times for all endpoints

### 4. Database Query Optimization 📊

**Medium Impact, Low Effort**

Optimize existing queries:

```sql
-- Instead of multiple COUNT(*) subqueries, use a single query with UNION ALL
-- For overview endpoint
SELECT 'dna_matches' as table_name, COUNT(*) as count FROM dna_matches
UNION ALL
SELECT 'individuals' as table_name, COUNT(*) as count FROM individuals
UNION ALL
SELECT 'trees' as table_name, COUNT(*) as count FROM trees
-- ... etc

-- Use EXPLAIN ANALYZE to identify slow queries
EXPLAIN ANALYZE SELECT COUNT(*) FROM dna_matches;
```

### 5. Materialized Views 📈

**High Impact, Medium Effort**

Create materialized views for complex aggregations:

```sql
-- Materialized view for match statistics
CREATE MATERIALIZED VIEW mv_match_stats AS
SELECT 
    COUNT(*) as total_matches,
    AVG(total_shared_segments_length_in_cm) as avg_cm,
    MAX(total_shared_segments_length_in_cm) as max_cm,
    MIN(total_shared_segments_length_in_cm) as min_cm
FROM dna_matches 
WHERE total_shared_segments_length_in_cm > 0;

-- Refresh strategy (can be automated)
REFRESH MATERIALIZED VIEW mv_match_stats;
```

### 6. Connection Pooling & Database Tuning ⚙️

**Medium Impact, Medium Effort**

Optimize database connection and configuration:

```go
// Database connection optimization
db.DB().SetMaxOpenConns(25)
db.DB().SetMaxIdleConns(10)
db.DB().SetConnMaxLifetime(5 * time.Minute)

// Query timeout configuration
ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
defer cancel()
```

### 7. Pagination & Streaming 📄

**Medium Impact, Low Effort**

Implement pagination for large result sets:

```go
// Add pagination to endpoints
type PaginationParams struct {
    Page     int `form:"page" binding:"min=1"`
    PageSize int `form:"page_size" binding:"min=1,max=1000"`
}

// Stream large datasets
func (h *AnalyticsHandler) GetMatchesStream(c *gin.Context) {
    // Use cursor-based pagination for large datasets
    // Return results in chunks
}
```

## Implementation Roadmap

### Phase 1: Quick Wins (1-2 weeks)
1. ✅ Add database indexes
2. ✅ Implement basic Redis caching for overview endpoint
3. ✅ Optimize SQL queries

**Expected Result**: 50-70% improvement in response times

### Phase 2: Caching Layer (2-3 weeks)
1. ✅ Full Redis implementation for all endpoints
2. ✅ Cache invalidation strategy
3. ✅ Cache warming on application startup

**Expected Result**: 90%+ improvement for cached requests

### Phase 3: Advanced Optimization (4-6 weeks)
1. ✅ Background job implementation
2. ✅ Materialized views
3. ✅ Database tuning and monitoring

**Expected Result**: Sub-second response times for all endpoints

### Phase 4: Monitoring & Scaling (Ongoing)
1. ✅ Performance monitoring dashboard
2. ✅ Automated cache warming
3. ✅ Database performance alerts
4. ✅ Horizontal scaling preparation

## Monitoring & Metrics

### Key Performance Indicators (KPIs)
- Response time per endpoint
- Cache hit ratio
- Database query execution time
- Memory usage
- CPU utilization

### Alerting Thresholds
- Response time > 5 seconds
- Cache hit ratio < 80%
- Database connection pool exhaustion
- Memory usage > 80%

## Cost-Benefit Analysis

| Strategy | Implementation Cost | Performance Gain | Maintenance Overhead |
|----------|-------------------|------------------|---------------------|
| Database Indexing | Low | High | Low |
| Redis Caching | Medium | Very High | Medium |
| Background Jobs | High | Very High | High |
| Query Optimization | Low | Medium | Low |
| Materialized Views | Medium | High | Medium |

## Recommended Starting Point

1. **Start with database indexing** - highest impact, lowest effort
2. **Implement Redis caching for overview endpoint** - addresses the slowest endpoint
3. **Add query timeouts and connection pooling** - improves reliability
4. **Monitor and measure** - establish baseline metrics

This approach will provide immediate improvements while building foundation for more advanced optimizations.
