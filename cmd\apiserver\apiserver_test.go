package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"strings"
	"testing"
	"time"
)

// TestAPIIntegration tests the API endpoints
// This is an integration test that requires the API server to be running
// Set the environment variable RUN_INTEGRATION_TESTS=true to run this test
func TestAPIIntegration(t *testing.T) {
	// Skip if not running integration tests
	if os.Getenv("RUN_INTEGRATION_TESTS") != "true" {
		t.Skip("Skipping integration tests. Set RUN_INTEGRATION_TESTS=true to run")
	}

	// Base URL for the API
	baseURL := "http://localhost:1231"

	// Test the health endpoint
	t.Run("Health", func(t *testing.T) {
		resp, err := http.Get(fmt.Sprintf("%s/health", baseURL))
		if err != nil {
			t.Fatalf("Failed to get health: %v", err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			t.<PERSON>rrorf("Expected status code %d, got %d", http.StatusOK, resp.StatusCode)
		}

		var response map[string]interface{}
		if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
			t.Fatalf("Failed to parse response body: %v", err)
		}

		status, ok := response["status"].(string)
		if !ok {
			t.Fatalf("Expected status in response, got %v", response)
		}

		if status != "ok" {
			t.Errorf("Expected status 'ok', got '%s'", status)
		}
	})

	// Test the liveness endpoint
	t.Run("Liveness", func(t *testing.T) {
		resp, err := http.Get(fmt.Sprintf("%s/liveness", baseURL))
		if err != nil {
			t.Fatalf("Failed to get liveness: %v", err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			t.Errorf("Expected status code %d, got %d", http.StatusOK, resp.StatusCode)
		}
	})

	// Test the readiness endpoint
	t.Run("Readiness", func(t *testing.T) {
		resp, err := http.Get(fmt.Sprintf("%s/readiness", baseURL))
		if err != nil {
			t.Fatalf("Failed to get readiness: %v", err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			t.Errorf("Expected status code %d, got %d", http.StatusOK, resp.StatusCode)
		}
	})

	// Test the token management endpoints
	t.Run("TokenManagement", func(t *testing.T) {
		// Test getting the current token state
		t.Run("GetCurrentTokenState", func(t *testing.T) {
			resp, err := http.Get(fmt.Sprintf("%s/api/v1/token", baseURL))
			if err != nil {
				t.Fatalf("Failed to get token: %v", err)
			}
			defer resp.Body.Close()

			if resp.StatusCode != http.StatusOK {
				t.Errorf("Expected status code %d, got %d", http.StatusOK, resp.StatusCode)
			}

			var response map[string]interface{}
			if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
				t.Fatalf("Failed to parse response body: %v", err)
			}

			// Check if we have a token or a message
			if tokenObj, ok := response["token"].(map[string]interface{}); ok {
				t.Logf("Token exists in the database: %v", tokenObj)

				// Check that the token is masked
				tokenValue, ok := tokenObj["token"].(string)
				if !ok {
					t.Fatalf("Expected token value in response, got %v", tokenObj)
				}

				if len(tokenValue) > 0 && !containsThreeDots(tokenValue) {
					t.Errorf("Expected masked token (containing '...'), got '%s'", tokenValue)
				}
			} else if message, ok := response["message"].(string); ok {
				if message != "No active token found" {
					t.Errorf("Expected message 'No active token found', got '%s'", message)
				}
			} else {
				t.Fatalf("Expected token or message in response, got %v", response)
			}
		})

		// Test updating the token
		t.Run("UpdateToken", func(t *testing.T) {
			// Generate a unique token for testing
			testToken := fmt.Sprintf("test-token-%d", time.Now().UnixNano())

			reqBody := map[string]string{
				"token": testToken,
			}
			reqBodyBytes, err := json.Marshal(reqBody)
			if err != nil {
				t.Fatalf("Failed to marshal request body: %v", err)
			}

			resp, err := http.Post(
				fmt.Sprintf("%s/api/v1/token", baseURL),
				"application/json",
				bytes.NewBuffer(reqBodyBytes),
			)
			if err != nil {
				t.Fatalf("Failed to update token: %v", err)
			}
			defer resp.Body.Close()

			if resp.StatusCode != http.StatusOK {
				t.Errorf("Expected status code %d, got %d", http.StatusOK, resp.StatusCode)
			}

			var response map[string]interface{}
			if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
				t.Fatalf("Failed to parse response body: %v", err)
			}

			message, ok := response["message"].(string)
			if !ok {
				t.Fatalf("Expected message in response, got %v", response)
			}

			if message != "Token updated successfully" {
				t.Errorf("Expected message 'Token updated successfully', got '%s'", message)
			}
		})

		// Test getting the token after updating it
		t.Run("GetTokenAfterUpdating", func(t *testing.T) {
			resp, err := http.Get(fmt.Sprintf("%s/api/v1/token", baseURL))
			if err != nil {
				t.Fatalf("Failed to get token: %v", err)
			}
			defer resp.Body.Close()

			if resp.StatusCode != http.StatusOK {
				t.Errorf("Expected status code %d, got %d", http.StatusOK, resp.StatusCode)
			}

			var response map[string]interface{}
			if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
				t.Fatalf("Failed to parse response body: %v", err)
			}

			tokenObj, ok := response["token"].(map[string]interface{})
			if !ok {
				t.Fatalf("Expected token in response, got %v", response)
			}

			tokenValue, ok := tokenObj["token"].(string)
			if !ok {
				t.Fatalf("Expected token value in response, got %v", tokenObj)
			}

			// Check that the token is masked
			if !containsThreeDots(tokenValue) {
				t.Errorf("Expected masked token (containing '...'), got '%s'", tokenValue)
			}

			isActive, ok := tokenObj["is_active"].(bool)
			if !ok {
				t.Fatalf("Expected is_active in response, got %v", tokenObj)
			}

			if !isActive {
				t.Errorf("Expected is_active to be true, got %v", isActive)
			}
		})

		// Test updating the token with an invalid value (too short)
		t.Run("UpdateTokenWithInvalidValue", func(t *testing.T) {
			reqBody := map[string]string{
				"token": "short",
			}
			reqBodyBytes, err := json.Marshal(reqBody)
			if err != nil {
				t.Fatalf("Failed to marshal request body: %v", err)
			}

			resp, err := http.Post(
				fmt.Sprintf("%s/api/v1/token", baseURL),
				"application/json",
				bytes.NewBuffer(reqBodyBytes),
			)
			if err != nil {
				t.Fatalf("Failed to update token: %v", err)
			}
			defer resp.Body.Close()

			if resp.StatusCode != http.StatusBadRequest {
				t.Errorf("Expected status code %d, got %d", http.StatusBadRequest, resp.StatusCode)
			}

			var response map[string]interface{}
			if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
				t.Fatalf("Failed to parse response body: %v", err)
			}

			errorMsg, ok := response["error"].(string)
			if !ok {
				t.Fatalf("Expected error in response, got %v", response)
			}

			if errorMsg != "Token is too short" {
				t.Errorf("Expected error 'Token is too short', got '%s'", errorMsg)
			}
		})

		// Test updating the token with an invalid JSON
		t.Run("UpdateTokenWithInvalidJSON", func(t *testing.T) {
			reqBody := "{invalid json}"
			resp, err := http.Post(
				fmt.Sprintf("%s/api/v1/token", baseURL),
				"application/json",
				bytes.NewBufferString(reqBody),
			)
			if err != nil {
				t.Fatalf("Failed to update token: %v", err)
			}
			defer resp.Body.Close()

			if resp.StatusCode != http.StatusBadRequest {
				t.Errorf("Expected status code %d, got %d", http.StatusBadRequest, resp.StatusCode)
			}

			var response map[string]interface{}
			if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
				t.Fatalf("Failed to parse response body: %v", err)
			}

			errorMsg, ok := response["error"].(string)
			if !ok {
				t.Fatalf("Expected error in response, got %v", response)
			}

			if errorMsg != "Invalid request format" {
				t.Errorf("Expected error 'Invalid request format', got '%s'", errorMsg)
			}
		})
	})

	// Test the DNA matches endpoint
	t.Run("DNAMatches", func(t *testing.T) {
		resp, err := http.Get(fmt.Sprintf("%s/dna-matches", baseURL))
		if err != nil {
			t.Fatalf("Failed to get DNA matches: %v", err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			t.Errorf("Expected status code %d, got %d", http.StatusOK, resp.StatusCode)
		}

		// Check that the response is a valid JSON array
		var matches []interface{}
		if err := json.NewDecoder(resp.Body).Decode(&matches); err != nil {
			t.Fatalf("Failed to parse response body: %v", err)
		}

		// Check that we got some matches
		if len(matches) == 0 {
			t.Logf("No DNA matches found. This might be expected in a test environment.")
		} else {
			t.Logf("Found %d DNA matches", len(matches))
		}
	})
}

// Helper function to check if a string contains three dots
func containsThreeDots(s string) bool {
	return len(s) >= 3 && (strings.Contains(s, "..."))
}

// TestStartServer tests that the server can be started
// This is a unit test that doesn't require the server to be running
func TestStartServer(t *testing.T) {
	// Skip if running integration tests
	if os.Getenv("RUN_INTEGRATION_TESTS") == "true" {
		t.Skip("Skipping unit tests when running integration tests")
	}

	// Test that the database schema file exists
	t.Run("SchemaFileExists", func(t *testing.T) {
		schemaPath := "../../internal/api/schema.sql"
		_, err := os.Stat(schemaPath)
		if os.IsNotExist(err) {
			t.Errorf("Schema file does not exist: %s", schemaPath)
		}
	})
}
