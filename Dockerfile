FROM golang:1.23-alpine AS builder

WORKDIR /app

# Copy go.mod and go.sum files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy the source code
COPY . .

# Build the application
RUN CGO_ENABLED=0 GOOS=linux go build -o apiserver cmd/apiserver/main.go cmd/apiserver/app.go

# Create a minimal image
FROM alpine:latest

WORKDIR /app

# Install tzdata for timezone support
RUN apk add --no-cache tzdata

# Copy the binary from the builder stage
COPY --from=builder /app/apiserver .
COPY --from=builder /app/internal/api/schema.sql ./internal/api/schema.sql
COPY --from=builder /app/internal/api/schema_mariadb.sql ./internal/api/schema_mariadb.sql

# Expose the port
EXPOSE 1231

# Set environment variables
ENV PORT=1231

# Run the application
CMD ["./apiserver"]
