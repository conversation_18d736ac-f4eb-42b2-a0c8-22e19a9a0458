#!/bin/bash

# MyHeritage Application Health Check Script
# Tests that all applications can start and run without errors

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
TIMEOUT=10
VERBOSE=false
QUICK=false
SKIP_DOCKER=false

# Application configurations
declare -A APPS=(
    ["apiserver"]="cmd/apiserver"
    ["clique"]="cmd/clique"
    ["myheritagedownloader"]="cmd/myheritagedownloader"
    ["graphjson-v1"]="cmd/graphjson/v1"
    ["graphjson-v2"]="cmd/graphjson/v2"
    ["graphjson-v3"]="cmd/graphjson/v3"
    ["graphjson-v4"]="cmd/graphjson/v4"
)

# Docker configurations
declare -A DOCKER_CONFIGS=(
    ["myheritage-downloader"]="Dockerfile.downloader"
    ["myheritage-apiserver"]="Dockerfile.apiserver"
)

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    case $status in
        "INFO")  echo -e "${BLUE}ℹ️  $message${NC}" ;;
        "PASS")  echo -e "${GREEN}✅ $message${NC}" ;;
        "FAIL")  echo -e "${RED}❌ $message${NC}" ;;
        "WARN")  echo -e "${YELLOW}⚠️  $message${NC}" ;;
        "SKIP")  echo -e "${YELLOW}⏭️  $message${NC}" ;;
    esac
}

# Function to show help
show_help() {
    cat << EOF
MyHeritage Application Health Check

Usage: $0 [OPTIONS]

Options:
  -h, --help       Show this help message
  -v, --verbose    Show verbose output including build logs
  -q, --quick      Run only compilation tests (skip startup tests)
  -t, --timeout N  Set timeout for application startup tests (default: 10s)
  --no-docker      Skip Docker build tests
  --list-apps      List all applications that will be tested

Examples:
  $0                    # Run all health checks
  $0 --quick            # Run only compilation tests
  $0 --verbose          # Show detailed output
  $0 --timeout 5        # Use 5 second timeout for startup tests
  $0 --no-docker        # Skip Docker tests

EOF
}

# Function to list applications
list_apps() {
    echo "Applications to be tested:"
    for app in "${!APPS[@]}"; do
        echo "  - $app (${APPS[$app]})"
    done
    echo ""
    echo "Docker configurations:"
    for docker in "${!DOCKER_CONFIGS[@]}"; do
        echo "  - $docker (${DOCKER_CONFIGS[$docker]})"
    done
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -q|--quick)
            QUICK=true
            shift
            ;;
        -t|--timeout)
            TIMEOUT="$2"
            shift 2
            ;;
        --no-docker)
            SKIP_DOCKER=true
            shift
            ;;
        --list-apps)
            list_apps
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Function to test Go module dependencies
test_dependencies() {
    print_status "INFO" "Testing Go module dependencies..."
    
    if go mod verify > /dev/null 2>&1; then
        print_status "PASS" "Go module dependencies verified"
    else
        print_status "FAIL" "Go module verification failed"
        return 1
    fi
    
    if go mod tidy -diff > /dev/null 2>&1; then
        print_status "PASS" "Go module is tidy"
    else
        print_status "WARN" "Go module needs tidying (run 'go mod tidy')"
    fi
}

# Function to test compilation
test_compilation() {
    print_status "INFO" "Testing application compilation..."
    local failed=0
    
    for app in "${!APPS[@]}"; do
        local app_path="${APPS[$app]}"
        
        if [[ $VERBOSE == true ]]; then
            print_status "INFO" "Building $app from $app_path..."
        fi
        
        if go build -o "/tmp/$app" "./$app_path" > /dev/null 2>&1; then
            print_status "PASS" "$app compiles successfully"
            rm -f "/tmp/$app"
        else
            print_status "FAIL" "$app compilation failed"
            if [[ $VERBOSE == true ]]; then
                echo "Build output:"
                go build -o "/tmp/$app" "./$app_path" 2>&1 | sed 's/^/  /'
            fi
            ((failed++))
        fi
    done
    
    return $failed
}

# Function to test application startup
test_startup() {
    print_status "INFO" "Testing application startup (timeout: ${TIMEOUT}s)..."
    local failed=0
    
    for app in "${!APPS[@]}"; do
        local app_path="${APPS[$app]}"
        
        # Build the application
        if ! go build -o "/tmp/$app" "./$app_path" > /dev/null 2>&1; then
            print_status "FAIL" "$app failed to build for startup test"
            ((failed++))
            continue
        fi
        
        # Test startup with timeout
        if [[ $VERBOSE == true ]]; then
            print_status "INFO" "Testing $app startup..."
        fi
        
        # Start the application in background and capture output
        timeout "$TIMEOUT" "/tmp/$app" --help > /tmp/${app}_output.log 2>&1 &
        local pid=$!
        
        # Wait for the process to complete or timeout
        if wait $pid 2>/dev/null; then
            # Check if help output looks reasonable
            if grep -q -i "usage\|help\|command" /tmp/${app}_output.log 2>/dev/null; then
                print_status "PASS" "$app starts and shows help"
            else
                print_status "WARN" "$app starts but help output unclear"
                if [[ $VERBOSE == true ]]; then
                    echo "Output:"
                    cat /tmp/${app}_output.log | head -10 | sed 's/^/  /'
                fi
            fi
        else
            # Try a different approach - just check if it starts without crashing immediately
            timeout 2 "/tmp/$app" > /tmp/${app}_quick.log 2>&1 &
            local quick_pid=$!
            sleep 1
            
            if kill -0 $quick_pid 2>/dev/null; then
                print_status "PASS" "$app starts without immediate crash"
                kill $quick_pid 2>/dev/null || true
            else
                print_status "FAIL" "$app crashes on startup"
                if [[ $VERBOSE == true ]]; then
                    echo "Error output:"
                    cat /tmp/${app}_quick.log | head -10 | sed 's/^/  /'
                fi
                ((failed++))
            fi
        fi
        
        # Cleanup
        rm -f "/tmp/$app" /tmp/${app}_output.log /tmp/${app}_quick.log
    done
    
    return $failed
}

# Function to test Docker builds
test_docker() {
    if [[ $SKIP_DOCKER == true ]]; then
        print_status "SKIP" "Docker tests skipped"
        return 0
    fi
    
    if ! command -v docker &> /dev/null; then
        print_status "SKIP" "Docker not available, skipping Docker tests"
        return 0
    fi
    
    print_status "INFO" "Testing Docker builds..."
    local failed=0
    
    for image in "${!DOCKER_CONFIGS[@]}"; do
        local dockerfile="${DOCKER_CONFIGS[$image]}"
        
        if [[ ! -f "$dockerfile" ]]; then
            print_status "SKIP" "Dockerfile $dockerfile not found, skipping $image"
            continue
        fi
        
        if [[ $VERBOSE == true ]]; then
            print_status "INFO" "Building Docker image $image from $dockerfile..."
            if docker build -f "$dockerfile" -t "$image" . ; then
                print_status "PASS" "Docker image $image built successfully"
            else
                print_status "FAIL" "Docker image $image build failed"
                ((failed++))
            fi
        else
            if docker build -f "$dockerfile" -t "$image" . > /dev/null 2>&1; then
                print_status "PASS" "Docker image $image built successfully"
            else
                print_status "FAIL" "Docker image $image build failed"
                ((failed++))
            fi
        fi
    done
    
    return $failed
}

# Function to run unit tests
test_units() {
    print_status "INFO" "Running unit tests..."
    
    if go test ./... -short > /dev/null 2>&1; then
        print_status "PASS" "Unit tests pass"
        return 0
    else
        print_status "FAIL" "Unit tests failed"
        if [[ $VERBOSE == true ]]; then
            echo "Test output:"
            go test ./... -short 2>&1 | sed 's/^/  /'
        fi
        return 1
    fi
}

# Main execution
main() {
    echo "🏥 === MyHeritage Application Health Check ==="
    echo ""
    
    local total_failures=0
    
    # Test dependencies
    test_dependencies || ((total_failures++))
    echo ""
    
    # Test compilation
    test_compilation || ((total_failures++))
    echo ""
    
    # Test unit tests
    test_units || ((total_failures++))
    echo ""
    
    # Test startup (unless quick mode)
    if [[ $QUICK == false ]]; then
        test_startup || ((total_failures++))
        echo ""
    else
        print_status "SKIP" "Startup tests skipped (quick mode)"
        echo ""
    fi
    
    # Test Docker builds (unless quick mode or skipped)
    if [[ $QUICK == false ]]; then
        test_docker || ((total_failures++))
        echo ""
    else
        print_status "SKIP" "Docker tests skipped (quick mode)"
        echo ""
    fi
    
    # Summary
    echo "📊 === Health Check Summary ==="
    if [[ $total_failures -eq 0 ]]; then
        print_status "PASS" "All health checks passed! 🎉"
        exit 0
    else
        print_status "FAIL" "$total_failures test category(ies) failed"
        exit 1
    fi
}

# Run main function
main "$@"
