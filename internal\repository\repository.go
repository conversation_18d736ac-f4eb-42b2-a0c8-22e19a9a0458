package repository

import (
	"context"
	"github.com/dmagur/myheritage/internal/model"
)

type Interface[M model.Interface] interface {
	FindAll(ctx context.Context, offset int, limit int) (*MultiResult[M], error)
	//FindOne(ctx context.Context, key string) (M, error)
	//Insert(ctx context.Context, model M) (M, error)
	//Update(ctx context.Context, model M) (M, error)
	//Delete(ctx context.Context, key string) error
}

type MultiResult[M model.Interface] struct {
	Count  uint
	Models []M
}
