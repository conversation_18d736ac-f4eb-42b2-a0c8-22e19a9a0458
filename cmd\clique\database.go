package main

import "database/sql"

func getAllIndividualIds(db *sql.DB) ([]string, error) {
	query := `
		SELECT id
		FROM individuals`

	// Execute the query
	rows, err := db.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	// Slice to hold the results
	var individuals []string

	// Loop through the rows and store them in the slice
	for rows.Next() {
		var individual string
		err := rows.Scan(&individual)
		if err != nil {
			return nil, err
		}
		individuals = append(individuals, individual)
	}

	// Check for errors after loop completion
	if err := rows.Err(); err != nil {
		return nil, err
	}

	return individuals, nil
}

func getAllConnectedIndividuals(db *sql.DB, individualId string) ([]string, error) {
	query := `SELECT source_individual_id from dna_matches WHERE match_individual_id=$1`
	rows, err := db.Query(query, individualId)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	// Slice to hold the results
	var individuals []string

	// Loop through the rows and store them in the slice
	for rows.Next() {
		var individual string
		err := rows.Scan(&individual)
		if err != nil {
			return nil, err
		}
		individuals = append(individuals, individual)
	}

	// Check for errors after loop completion
	if err := rows.Err(); err != nil {
		return nil, err
	}

	query = `SELECT match_individual_id from dna_matches WHERE source_individual_id=$1`
	rows, err = db.Query(query, individualId)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	// Loop through the rows and store them in the slice
	for rows.Next() {
		var individual string
		err := rows.Scan(&individual)
		if err != nil {
			return nil, err
		}
		individuals = append(individuals, individual)
	}

	// Check for errors after loop completion
	if err := rows.Err(); err != nil {
		return nil, err
	}

	var unique []string
	m := map[string]bool{}
	for _, v := range individuals {
		if !m[v] {
			m[v] = true
			unique = append(unique, v)
		}
	}

	return unique, nil
}
