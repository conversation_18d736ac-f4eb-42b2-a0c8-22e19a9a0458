[1mdiff --git a/internal/fetch/fetch.go b/internal/fetch/fetch.go[m
[1mindex 23d8ea0..71a4c75 100644[m
[1m--- a/internal/fetch/fetch.go[m
[1m+++ b/internal/fetch/fetch.go[m
[36m@@ -2,29 +2,45 @@[m [mpackage fetch[m
 [m
 import ([m
 	"bytes"[m
[31m-	"database/sql"[m
 	"fmt"[m
 	"io"[m
 	"net/http"[m
 	"strings"[m
 	"time"[m
[32m+[m
[32m+[m	[32m"github.com/dmagur/myheritage/internal/database"[m
[32m+[m	[32mlog "github.com/sirupsen/logrus"[m
 )[m
 [m
 type BearerToken string[m
 [m
 type MyHeritageApiClient struct {[m
 	bearerToken BearerToken[m
[31m-	db          *sql.DB[m
[32m+[m	[32mdb          *database.GormDB[m
[32m+[m	[32mlogger      *log.Logger[m
 }[m
 [m
[31m-func NewMyHeritageClient(bearerToken BearerToken, db *sql.DB) *MyHeritageApiClient {[m
[32m+[m[32mfunc NewMyHeritageClient(bearerToken BearerToken, db *database.GormDB) *MyHeritageApiClient {[m
[32m+[m	[32m// Create a default logger if none is provided[m
[32m+[m	[32mlogger := log.New()[m
[32m+[m	[32mlogger.SetFormatter(&log.TextFormatter{})[m
[32m+[m
 	return &MyHeritageApiClient{[m
 		bearerToken: bearerToken,[m
 		db:          db,[m
[32m+[m		[32mlogger:      logger,[m
 	}[m
 }[m
 [m
 func (c MyHeritageApiClient) Fetch(url string, query string, queryName string) ([]byte, error) {[m
[32m+[m	[32m// Log the full token being used[m
[32m+[m	[32mtokenStr := string(c.bearerToken)[m
[32m+[m	[32mif len(tokenStr) > 0 {[m
[32m+[m		[32mc.logger.Infof("Using bearer token for API request: %s (FULL TOKEN)", tokenStr)[m
[32m+[m	[32m} else {[m
[32m+[m		[32mc.logger.Warn("No bearer token provided for API request!")[m
[32m+[m	[32m}[m
[32m+[m
 	boundary := "------WebKitFormBoundary4t41amcgIlOuUB7u"[m
 	// Define the multipart form data payload[m
 	payload := fmt.Sprintf(`%s[m
[36m@@ -84,20 +100,32 @@[m [mfcc2609ff52f638fd60ce1be66b204ac[m
 	}[m
 [m
 	currentTime := time.Now()[m
[31m-	_, err = c.db.Exec(`INSERT INTO api_calls(url,response,created_at,query) VALUES($1,$2,$3,$4)`, url, string(body), currentTime.Format("2006-01-02 15:04:05"), query)[m
 [m
[31m-	if err != nil {[m
[31m-		fmt.Println("Error saving api call in db:", err)[m
[31m-		return nil, err[m
[32m+[m	[32m// Create a new API call record[m
[32m+[m	[32mapiCall := database.ApiCall{[m
[32m+[m		[32mURL:       url,[m
[32m+[m		[32mResponse:  string(body),[m
[32m+[m		[32mCreatedAt: currentTime.Format("2006-01-02 15:04:05"),[m
[32m+[m		[32mQuery:     query,[m
[32m+[m	[32m}[m
[32m+[m
[32m+[m	[32m// Save the API call to the database using GORM[m
[32m+[m	[32mresult := c.db.DB.Create(&apiCall)[m
[32m+[m	[32mif result.Error != nil {[m
[32m+[m		[32mfmt.Println("Error saving api call in db:", result.Error)[m
[32m+[m		[32mreturn nil, result.Error[m
 	}[m
 [m
 	if strings.Contains(string(body), "exceeded the allowed rate limit") {[m
[32m+[m		[32mc.logger.Error("API request failed: exceeded the allowed rate limit")[m
 		return nil, fmt.Errorf("exceeded the allowed rate limit")[m
 	}[m
 [m
 	if strings.Contains(string(body), "Invalid OAuth2 access token") {[m
[32m+[m		[32mc.logger.Errorf("API request failed: Invalid OAuth2 access token. Token used: %s (FULL TOKEN)", string(c.bearerToken))[m
 		return nil, fmt.Errorf("Invalid OAuth2 access token")[m
 	}[m
 [m
[32m+[m	[32mc.logger.Info("API request successful")[m
 	return body, nil[m
 }[m
