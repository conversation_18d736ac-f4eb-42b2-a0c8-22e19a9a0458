//go:generate mockgen -destination ../../tests/mocks/attributedescription_mock/dynamodb_repository.go -source ../repository/repository.go Interface
package dnamatch

import (
	"context"
	"database/sql"
	"github.com/dmagur/myheritage/apigen"
	"github.com/dmagur/myheritage/internal/repository"
)

type Repository struct {
	db *sql.DB
}

func NewRepository(db *sql.DB) *Repository {
	return &Repository{db}
}

func (r *Repository) FindAll(ctx context.Context, offset int, limit int) (
	*repository.MultiResult[*apigen.DnaMatch],
	error,
) {
	//Prepare the SQL query
	query := `
		SELECT m.id, m.created_at, m.match_individual_id, i1.name, m.total_shared_segments_length_in_cm, m.shared_matches_count, m.source_individual_id, i2.name
		FROM dna_matches m
		INNER JOIN individuals i1 on m.match_individual_id=i1.id
		INNER JOIN individuals i2 on m.source_individual_id=i2.id
		ORDER BY m.total_shared_segments_length_in_cm desc
		OFFSET $1
		LIMIT $2
	`

	// Execute the query
	rows, err := r.db.Query(query, offset, limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	// Slice to hold the results
	results := new(repository.MultiResult[*apigen.DnaMatch])

	// Loop through the rows and store them in the slice
	for rows.Next() {
		var dnaMatch apigen.DnaMatch
		err := rows.Scan(&dnaMatch.Id, &dnaMatch.CreatedTime, &dnaMatch.MatchIndividualId, &dnaMatch.MatchName, &dnaMatch.SharedLength, &dnaMatch.SharedMatchesCount, &dnaMatch.SourceIndividualId, &dnaMatch.SourceName)
		if err != nil {
			return nil, err
		}
		results.Models = append(results.Models, &dnaMatch)
	}

	// Check for errors after loop completion
	if err := rows.Err(); err != nil {
		return nil, err
	}

	results.Count = uint(len(results.Models))

	return results, nil
}
