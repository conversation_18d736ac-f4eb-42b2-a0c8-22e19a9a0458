package api

import (
	"database/sql/driver"
	"fmt"
	"time"
)

// MySQLTime is a custom type to handle MySQL/MariaDB datetime fields
type MySQLTime struct {
	time.Time
}

// <PERSON>an implements the sql.Scanner interface
func (t *MySQLTime) Scan(value interface{}) error {
	if value == nil {
		t.Time = time.Time{}
		return nil
	}

	switch v := value.(type) {
	case time.Time:
		t.Time = v
		return nil
	case []byte:
		parsedTime, err := time.Parse("2006-01-02 15:04:05", string(v))
		if err != nil {
			return err
		}
		t.Time = parsedTime
		return nil
	case string:
		parsedTime, err := time.Parse("2006-01-02 15:04:05", v)
		if err != nil {
			return err
		}
		t.Time = parsedTime
		return nil
	}

	return fmt.Errorf("cannot scan type %T into MySQLTime", value)
}

// Value implements the driver.Valuer interface
func (t MySQLTime) Value() (driver.Value, error) {
	if t.Time.IsZero() {
		return nil, nil
	}
	return t.Time.Format("2006-01-02 15:04:05"), nil
}

// ApiToken represents the api_tokens table
type ApiToken struct {
	ID        uint      `gorm:"primaryKey;column:id;autoIncrement"`
	Token     string    `gorm:"column:token;type:text;not null"`
	CreatedAt MySQLTime `gorm:"column:created_at;not null"`
	IsActive  bool      `gorm:"column:is_active;not null;default:true"`
}

// TableName overrides the table name
func (ApiToken) TableName() string {
	return "api_tokens"
}
