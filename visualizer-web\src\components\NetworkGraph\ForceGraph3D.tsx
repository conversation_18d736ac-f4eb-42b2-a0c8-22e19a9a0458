import React, { useEffect, useRef } from 'react';
import ForceGraph3D from '3d-force-graph';
import { NetworkGraph as NetworkGraphData } from '../../services/api';

interface ForceGraph3DProps {
  data: NetworkGraphData;
  type: '3d' | '2d';
  width: string;
  height: string;
}

const ForceGraph3DComponent: React.FC<ForceGraph3DProps> = ({
  data,
  type,
  width,
  height,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const graphRef = useRef<any>(null);

  useEffect(() => {
    if (!containerRef.current || !data) return;

    try {
      // Clear previous graph
      if (graphRef.current) {
        containerRef.current.innerHTML = '';
      }

      // Check if we have valid data
      if (!data.nodes || !data.edges || data.nodes.length === 0) {
        containerRef.current.innerHTML = `<div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #666;">
          <div style="text-align: center;">
            <h3>No Network Data</h3>
            <p>No nodes or connections found. Try adjusting the filters.</p>
          </div>
        </div>`;
        return;
      }

      // Prepare data for 3d-force-graph
      const graphData = {
        nodes: data.nodes.map(node => ({
          id: node.id,
          name: node.name,
          group: node.group || 'default',
          size: node.size || 1,
          color: node.color || '#1976d2',
        })),
        links: data.edges.map(edge => ({
          source: edge.source,
          target: edge.target,
          value: edge.value,
          relationship: edge.relationship || 'unknown',
        })),
      };

      // Create the graph
      const Graph = new ForceGraph3D(containerRef.current)
        .graphData(graphData)
        .nodeLabel('name')
        .nodeAutoColorBy('group')
        .nodeVal(node => Math.max(1, (node as any).size || 1))
        .linkLabel(link => `${(link as any).relationship}: ${(link as any).value} cM`)
        .linkWidth(link => Math.max(0.5, (link as any).value / 50))
        .linkOpacity(0.6)
        .onNodeClick((node: any) => {
          console.log('Node clicked:', node);
          // You can add more interaction logic here
        })
        .onLinkClick((link: any) => {
          console.log('Link clicked:', link);
          // You can add more interaction logic here
        })
        .backgroundColor('#f5f5f5')
        .showNavInfo(false);

      // Configure 3D specific settings
      if (type === '3d') {
        Graph
          .enableNodeDrag(true)
          .enableNavigationControls(true);
      }

      // Store reference
      graphRef.current = Graph;

      // Handle resize
      const handleResize = () => {
        if (graphRef.current && containerRef.current) {
          const rect = containerRef.current.getBoundingClientRect();
          graphRef.current.width(rect.width).height(rect.height);
        }
      };

      window.addEventListener('resize', handleResize);

      // Initial resize
      setTimeout(handleResize, 100);

      return () => {
        window.removeEventListener('resize', handleResize);
        if (containerRef.current) {
          containerRef.current.innerHTML = '';
        }
      };
    } catch (error) {
      console.error('Error creating 3D force graph:', error);
      if (containerRef.current) {
        containerRef.current.innerHTML = `<div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #666;">
          <div>
            <h3>Error loading 3D graph</h3>
            <p>Please check the browser console for details.</p>
          </div>
        </div>`;
      }
    }
  }, [data, type]);

  return (
    <div
      ref={containerRef}
      style={{
        width: width,
        height: height,
        border: '1px solid #e0e0e0',
        borderRadius: '8px',
        overflow: 'hidden',
        position: 'relative',
      }}
    />
  );
};

export default ForceGraph3DComponent;
