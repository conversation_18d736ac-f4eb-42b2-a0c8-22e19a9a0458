package database

import (
	"database/sql"
	"fmt"
)

// DBAdapter is an interface for database operations
type DBAdapter interface {
	// Execute a query that doesn't return rows
	Exec(query string, args ...interface{}) (sql.Result, error)

	// Execute a query that returns rows
	Query(query string, args ...interface{}) (*sql.Rows, error)

	// Execute a query that returns at most one row
	QueryRow(query string, args ...interface{}) *sql.Row

	// Get the underlying database connection
	DB() *sql.DB

	// Close the database connection
	Close() error
}

// PostgreSQLAdapter is an adapter for PostgreSQL
type PostgreSQLAdapter struct {
	db *sql.DB
}

// MariaDBAdapter is an adapter for MariaDB
type MariaDBAdapter struct {
	db *sql.DB
}

// NewDBAdapter creates a new database adapter based on the DB_TYPE environment variable
func NewDBAdapter(connStr ConnString) (DBAdapter, error) {
	dbType := getEnv("DB_TYPE", "postgres")

	switch dbType {
	case "mariadb", "mysql":
		db, err := sql.Open("mysql", string(connStr))
		if err != nil {
			return nil, fmt.Errorf("failed to connect to MariaDB: %w", err)
		}

		// Test the connection
		if err := db.Ping(); err != nil {
			return nil, fmt.Errorf("failed to ping MariaDB: %w", err)
		}

		return &MariaDBAdapter{db: db}, nil

	case "postgres", "postgresql":
		db, err := sql.Open("postgres", string(connStr))
		if err != nil {
			return nil, fmt.Errorf("failed to connect to PostgreSQL: %w", err)
		}

		// Test the connection
		if err := db.Ping(); err != nil {
			return nil, fmt.Errorf("failed to ping PostgreSQL: %w", err)
		}

		return &PostgreSQLAdapter{db: db}, nil

	default:
		// Default to PostgreSQL
		db, err := sql.Open("postgres", string(connStr))
		if err != nil {
			return nil, fmt.Errorf("failed to connect to PostgreSQL: %w", err)
		}

		// Test the connection
		if err := db.Ping(); err != nil {
			return nil, fmt.Errorf("failed to ping PostgreSQL: %w", err)
		}

		return &PostgreSQLAdapter{db: db}, nil
	}
}

// Exec executes a query without returning any rows
func (a *PostgreSQLAdapter) Exec(query string, args ...interface{}) (sql.Result, error) {
	return a.db.Exec(query, args...)
}

// Query executes a query that returns rows
func (a *PostgreSQLAdapter) Query(query string, args ...interface{}) (*sql.Rows, error) {
	return a.db.Query(query, args...)
}

// QueryRow executes a query that is expected to return at most one row
func (a *PostgreSQLAdapter) QueryRow(query string, args ...interface{}) *sql.Row {
	return a.db.QueryRow(query, args...)
}

// DB returns the underlying database connection
func (a *PostgreSQLAdapter) DB() *sql.DB {
	return a.db
}

// Close closes the database connection
func (a *PostgreSQLAdapter) Close() error {
	return a.db.Close()
}

// Exec executes a query without returning any rows
func (a *MariaDBAdapter) Exec(query string, args ...interface{}) (sql.Result, error) {
	// Convert PostgreSQL-style $n parameters to MariaDB-style ? parameters
	adaptedQuery := convertPgParamsToMariaDB(query)
	return a.db.Exec(adaptedQuery, args...)
}

// Query executes a query that returns rows
func (a *MariaDBAdapter) Query(query string, args ...interface{}) (*sql.Rows, error) {
	// Convert PostgreSQL-style $n parameters to MariaDB-style ? parameters
	adaptedQuery := convertPgParamsToMariaDB(query)
	return a.db.Query(adaptedQuery, args...)
}

// QueryRow executes a query that is expected to return at most one row
func (a *MariaDBAdapter) QueryRow(query string, args ...interface{}) *sql.Row {
	// Convert PostgreSQL-style $n parameters to MariaDB-style ? parameters
	adaptedQuery := convertPgParamsToMariaDB(query)
	return a.db.QueryRow(adaptedQuery, args...)
}

// DB returns the underlying database connection
func (a *MariaDBAdapter) DB() *sql.DB {
	return a.db
}

// Close closes the database connection
func (a *MariaDBAdapter) Close() error {
	return a.db.Close()
}
