package main

import (
	"database/sql"
	"encoding/json"
	"fmt"
	_ "github.com/lib/pq"
	"os"
)

type GraphNode struct {
	Id    string `json:"id"`
	Group string `json:"group"`
}

type GraphLink struct {
	Source string `json:"source"`
	Target string `json:"target"`
	Value  int    `json:"value"`
}

type GraphData struct {
	Nodes []GraphNode `json:"nodes"`
	Links []GraphLink `json:"links"`
}

func main() {
	connStr := "user=postgres password=mysecretpassword dbname=dna_match_db sslmode=disable"
	db, err := sql.Open("postgres", connStr)
	if err != nil {
		fmt.Println("Error connecting to the database:", err)
		return
	}
	defer db.Close()

	// Pavel
	/*
		offset := 0
		sourceIndividualId := "individual-32361932-1000006"
		dnaMatchIDs, err := getDnaMatchesByIndividualId(db, sourceIndividualId, offset, 500)
		bearerToken := "4.cbdd04f731661d487d728136bd8331a0.122085562.1725369381.1440.7345153..s0gts5f0.881b70f1bb9aaa4b3d9ce42ab04ca654004f194b2c7b8298a45976d057f6195d"
	*/

	// Elena

	//offset := 0
	//sourceIndividualId := "individual-32361932-1000002"
	//dnaMatchIDs, err := getDnaMatchesByIndividualId(db, sourceIndividualId, offset, 500)
	//bearerToken := os.Getenv("BEARER_TOKEN")

	//Dmitrii
	offset := 0
	//sourceIndividualId := "individual-32361932-1000003"
	//sourceName := "Дмитрий Magur (geb. Магур)"

	sourceIndividualId := "individual-1592334192-1500079"
	sourceName := "Віктор Заноха"

	dnaMatchIDs, err := getDnaMatchesByIndividualId(db, sourceIndividualId, offset, 10000)

	graphData := GraphData{}
	graphData.Nodes = append(graphData.Nodes, GraphNode{Id: sourceName, Group: sourceIndividualId})

	//individuals, err := getIndividuals(db, 0, 4000)
	individuals, err := getIndividualsBySourceIndividualId(db, sourceIndividualId)
	if err != nil {
		fmt.Println("Error fetching individual from db:", err)
	}

	nodesHashMap := make(map[string]int)
	nodesHashMap[sourceIndividualId] = 0

	for _, individual := range individuals {
		//graphData.Nodes = append(graphData.Nodes, GraphNode{Id: individual.Name, Group: individual.ID})
		nodesHashMap[individual.ID] = 0
	}

	linksHashMap := make(map[string]map[string]GraphLink)

	// Now loop through the slice of DnaMatch
	for _, match := range dnaMatchIDs {
		if match.MatchIndividualID == "" {
			continue
		}

		_, ok := nodesHashMap[match.MatchIndividualID]
		if !ok {
			continue
		}

		_, ok2 := linksHashMap[sourceIndividualId][match.MatchIndividualID]
		_, ok3 := linksHashMap[match.MatchIndividualID][sourceIndividualId]

		if !ok2 && !ok3 {
			//graphData.Links = append(graphData.Links, GraphLink{Source: sourceName, Target: match.Name, Value: int(match.SharedLength)})
			_, ok4 := linksHashMap[sourceIndividualId]
			if !ok4 {
				linksHashMap[sourceIndividualId] = make(map[string]GraphLink)
			}
			linksHashMap[sourceIndividualId][match.MatchIndividualID] = GraphLink{Source: sourceName, Target: match.Name, Value: int(match.SharedLength)}
			nodesHashMap[match.MatchIndividualID]++
			nodesHashMap[sourceIndividualId]++
		}

		linksHashMap, nodesHashMap = getSharedMatches(db, match, &graphData, 2, linksHashMap, nodesHashMap)
	}

	minLinks := 1
	for _, individual := range individuals {
		if nodesHashMap[individual.ID] > minLinks {
			graphData.Nodes = append(graphData.Nodes, GraphNode{Id: individual.Name, Group: individual.ID})
			for linkedIndividualId, link := range linksHashMap[individual.ID] {
				if nodesHashMap[linkedIndividualId] > minLinks && linkedIndividualId != sourceIndividualId {
					graphData.Links = append(graphData.Links, link)
				}
			}
		}
	}

	//for linkedIndividualId, link := range linksHashMap[sourceIndividualId] {
	//	if nodesHashMap[linkedIndividualId] > minLinks {
	//		graphData.Links = append(graphData.Links, link)
	//	}
	//}

	// Convert the struct to JSON
	jsonData, err := json.MarshalIndent(graphData, "", "  ")
	if err != nil {
		fmt.Println("Error converting struct to JSON:", err)
		return
	}

	//fmt.Printf("%v\n", jsonData)

	// Save the JSON data to a file
	file, err := os.Create("person.json")
	if err != nil {
		fmt.Println("Error creating JSON file:", err)
		return
	}
	defer file.Close()

	_, err = file.Write(jsonData)
	if err != nil {
		fmt.Println("Error writing to JSON file:", err)
		return
	}

	fmt.Println("JSON data saved to person.json")
}

func getSharedMatches(db *sql.DB, match Result, graphData *GraphData, level int, linksHashMap map[string]map[string]GraphLink, nodesHashMap map[string]int) (map[string]map[string]GraphLink, map[string]int) {
	if level > 5 {
		return linksHashMap, nodesHashMap
	}
	level++

	sharedMatches, err := getSharedDnaMatchesByIndividualId(db, match.MatchIndividualID, 0, 1000)
	fmt.Printf("level: %d\n", level)
	if err != nil {
		fmt.Println("Error fetching shared matches:", err)
		return linksHashMap, nodesHashMap
	}
	for _, sharedMatch := range sharedMatches {
		if sharedMatch.MatchIndividualID == "" {
			continue
		}

		_, ok := nodesHashMap[sharedMatch.MatchIndividualID]
		if !ok {
			continue
		}

		_, ok2 := linksHashMap[sharedMatch.MatchIndividualID][match.MatchIndividualID]
		_, ok3 := linksHashMap[match.MatchIndividualID][sharedMatch.MatchIndividualID]

		if !ok2 && !ok3 {
			//graphData.Links = append(graphData.Links, GraphLink{Source: match.Name, Target: sharedMatch.Name, Value: int(sharedMatch.SharedLength)})
			_, ok4 := linksHashMap[sharedMatch.MatchIndividualID]
			if !ok4 {
				linksHashMap[sharedMatch.MatchIndividualID] = make(map[string]GraphLink)
			}
			linksHashMap[sharedMatch.MatchIndividualID][match.MatchIndividualID] = GraphLink{Source: match.Name, Target: sharedMatch.Name, Value: int(sharedMatch.SharedLength)}
			nodesHashMap[sharedMatch.MatchIndividualID]++
			nodesHashMap[match.MatchIndividualID]++
		}

		linksHashMap, nodesHashMap = getSharedMatches(db, sharedMatch, graphData, level, linksHashMap, nodesHashMap)
	}

	return linksHashMap, nodesHashMap
}
