version: '3.8'

services:
  # API Server (existing)
  apiserver:
    build: .
    container_name: myheritage-api
    ports:
      - "1231:1231"
    environment:
      - PORT=1231
      - DB_HOST=${DB_HOST}
      - DB_PORT=${DB_PORT}
      - DB_USER=${DB_USER}
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_NAME=${DB_NAME}
    env_file:
      - .env
    networks:
      - myheritage-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:1231/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Visualizer Web App
  visualizer-web:
    build: ./visualizer-web
    container_name: myheritage-visualizer
    ports:
      - "3000:80"
    environment:
      - REACT_APP_API_URL=http://localhost:1231
    depends_on:
      - apiserver
    networks:
      - myheritage-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis (optional, for caching)
  redis:
    image: redis:7-alpine
    container_name: myheritage-redis
    ports:
      - "6379:6379"
    networks:
      - myheritage-network
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - redis-data:/data

networks:
  myheritage-network:
    driver: bridge

volumes:
  redis-data:
    driver: local
