openapi: 3.0.0
info:
  title: Myheritage Downloader - API
  version: 0.3.0
servers:
- description: Development
  url: http://localhost:1231
security:
- ApiKeyAuth: []
tags:
- description: Myheritage Downloader
  name: Myheritage Downloader
paths:
  /dna-matches:
    get:
      operationId: getMatches
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/DnaMatch'
                type: array
          description: Ok
        default:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
          description: Error response
      summary: Get dna matches list
      tags:
      - Matches
components:
  responses:
    Error:
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
      description: Error response
  schemas:
    DnaMatch:
      example:
        matchName: matchName
        matchIndividualId: matchIndividualId
        sharedLength: 0.8008281904610115
        createdTime: 2000-01-23T04:56:07.000+00:00
        id: id
        sourceName: sourceName
        sharedMatchesCount: 6
        sourceIndividualId: sourceIndividualId
      properties:
        id:
          type: string
        sharedLength:
          type: number
        createdTime:
          format: date-time
          type: string
        matchName:
          type: string
        matchIndividualId:
          type: string
        sourceName:
          type: string
        sourceIndividualId:
          type: string
        sharedMatchesCount:
          nullable: true
          type: integer
      required:
      - createdTime
      - id
      - matchIndividualId
      - matchName
      - sharedLength
      - sharedMatchesCount
      - sourceIndividualId
      - sourceName
      type: object
    ErrorResponse:
      example:
        trace:
        - trace
        - trace
        message: message
      properties:
        message:
          type: string
        trace:
          items:
            type: string
          type: array
      required:
      - message
