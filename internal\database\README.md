# Database Package

This package provides database connectivity and models for the MyHeritage application.

## Current State (Post-GORM Migration)

The database package has been migrated from raw SQL to GORM. This README documents the current state and cleanup status.

## Active Files

### Core GORM Files (Primary)
- **`gorm_db.go`** - Main GORM database connection and models
  - Contains all GORM models (APICall, DNAMatch, Pedigree, etc.)
  - Provides `NewGormDB()` function for creating GORM connections
  - Handles database migrations and table creation
  - **Status**: ✅ Active - Primary database interface

### Legacy SQL Files (Backward Compatibility)
- **`factory.go`** - Database factory functions
  - `NewDB()` - Creates raw SQL connections (legacy)
  - `GetConnectionString()` - Helper for GORM connections
  - **Status**: ⚠️ Legacy - Kept for backward compatibility

- **`mariadb.go`** - MariaDB connection functions
  - `NewMariaDB()` - Creates MariaDB connections (legacy)
  - `NewSqlDB()` - Database type-based connection factory (legacy)
  - **Status**: ⚠️ Legacy - Kept for backward compatibility

- **`pgsql.go`** - PostgreSQL connection functions
  - `ConnString` type definition
  - `NewPgSql()` - Creates PostgreSQL connections (legacy)
  - **Status**: ⚠️ Legacy - Kept for backward compatibility

- **`utils.go`** - Utility functions
  - `getEnv()` - Environment variable helper (still used)
  - **Status**: ✅ Active - Contains utility functions

### Obsolete Files
- **`db_adapter.go`** - Database adapter interface
  - **Status**: ❌ Obsolete - Marked for removal
  - Contains old DBAdapter interface and implementations
  - All functionality replaced by GORM

## Migration Status

### ✅ Completed
- All MyHeritage modules migrated to GORM
- All models defined in GORM
- Database connections use GORM
- Comprehensive logging added
- Type-safe database operations

### ⚠️ Legacy Support
- Raw SQL functions kept for backward compatibility
- Will be removed in future cleanup phases
- New code should use GORM exclusively

### ❌ To Be Removed
- `db_adapter.go` - Can be safely removed once all references are updated
- Parameter conversion functions in `utils.go` - No longer needed with GORM

## Usage Guidelines

### For New Code
```go
// Use GORM connection
db := database.NewGormDB()

// Use GORM models
var matches []database.DNAMatch
db.Find(&matches)
```

### For Legacy Code (Temporary)
```go
// Legacy raw SQL (avoid in new code)
db := database.NewDB()
```

## Environment Variables

The package uses these environment variables:

- `DB_TYPE` - Database type ("postgres", "mariadb", "mysql")
- `DB_HOST` - Database host
- `DB_USER` - Database username  
- `DB_PASSWORD` - Database password
- `DB_NAME` - Database name

## Next Steps

1. **Phase 1**: ✅ Complete GORM migration
2. **Phase 2**: ✅ Add comprehensive logging
3. **Phase 3**: ✅ Clean up obsolete code (current)
4. **Phase 4**: Remove legacy SQL functions (future)
5. **Phase 5**: Remove obsolete files (future)

## Dependencies

- `gorm.io/gorm` - ORM framework
- `gorm.io/driver/postgres` - PostgreSQL driver
- `gorm.io/driver/mysql` - MySQL/MariaDB driver
- `github.com/lib/pq` - PostgreSQL driver (legacy)
- `github.com/go-sql-driver/mysql` - MySQL driver (legacy)
