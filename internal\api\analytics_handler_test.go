package api

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/dmagur/myheritage/internal/database"
	"github.com/gin-gonic/gin"
	log "github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// setupTestDB creates an in-memory SQLite database for testing
func setupTestDB(t *testing.T) *gorm.DB {
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	require.NoError(t, err)

	// Auto-migrate the schema
	err = db.AutoMigrate(
		&database.DNAMatch{},
		&database.Individual{},
		&database.Tree{},
		&database.SharedSegment{},
		&database.Submitter{},
		&database.Family{},
		&database.Surname{},
	)
	require.NoError(t, err)

	return db
}

// seedTestData populates the test database with sample data
func seedTestData(t *testing.T, db *gorm.DB) {
	// Create test individuals
	individuals := []database.Individual{
		{ID: "individual-1", Gender: "M", AgeGroup: "A", TreeID: "tree-1", BirthPlace: "USA"},
		{ID: "individual-2", Gender: "F", AgeGroup: "A", TreeID: "tree-1", BirthPlace: "Canada"},
		{ID: "individual-3", Gender: "M", AgeGroup: "C", TreeID: "", BirthPlace: "UK"},
		{ID: "individual-4", Gender: "F", AgeGroup: "A", TreeID: "tree-2", BirthPlace: "Germany"},
		{ID: "individual-5", Gender: "U", AgeGroup: "A", TreeID: "", BirthPlace: "France"},
	}
	for _, individual := range individuals {
		require.NoError(t, db.Create(&individual).Error)
	}

	// Create test DNA matches
	matches := []database.DNAMatch{
		{
			ID:                            "match-1",
			SourceIndividualID:            "individual-1",
			MatchIndividualID:             "individual-2",
			TotalSharedSegmentsLengthInCm: 150.5,
			ExactDnaRelationship:          "cousin",
			ConfidenceLevel:               "high",
		},
		{
			ID:                            "match-2",
			SourceIndividualID:            "individual-1",
			MatchIndividualID:             "individual-3",
			TotalSharedSegmentsLengthInCm: 75.2,
			ExactDnaRelationship:          "distant",
			ConfidenceLevel:               "medium",
		},
		{
			ID:                            "match-3",
			SourceIndividualID:            "individual-2",
			MatchIndividualID:             "individual-4",
			TotalSharedSegmentsLengthInCm: 200.8,
			ExactDnaRelationship:          "sibling",
			ConfidenceLevel:               "high",
		},
		{
			ID:                            "match-4",
			SourceIndividualID:            "individual-3",
			MatchIndividualID:             "individual-5",
			TotalSharedSegmentsLengthInCm: 25.1,
			ExactDnaRelationship:          "",
			ConfidenceLevel:               "low",
		},
	}
	for _, match := range matches {
		require.NoError(t, db.Create(&match).Error)
	}

	// Create test trees
	trees := []database.Tree{
		{ID: "tree-1", SiteCreatorCountry: "USA"},
		{ID: "tree-2", SiteCreatorCountry: "Canada"},
		{ID: "tree-3", SiteCreatorCountry: "UK"},
	}
	for _, tree := range trees {
		require.NoError(t, db.Create(&tree).Error)
	}

	// Create test shared segments
	segments := []database.SharedSegment{
		{ID: 1, MatchID: "match-1", ChromosomeID: 1, StartPosition: 1000, EndPosition: 2000, LengthInCentimorgans: 50.5},
		{ID: 2, MatchID: "match-1", ChromosomeID: 2, StartPosition: 3000, EndPosition: 4000, LengthInCentimorgans: 75.2},
		{ID: 3, MatchID: "match-2", ChromosomeID: 1, StartPosition: 5000, EndPosition: 6000, LengthInCentimorgans: 30.1},
		{ID: 4, MatchID: "match-3", ChromosomeID: 3, StartPosition: 7000, EndPosition: 8000, LengthInCentimorgans: 100.8},
	}
	for _, segment := range segments {
		require.NoError(t, db.Create(&segment).Error)
	}

	// Create test submitters
	submitters := []database.Submitter{
		{ID: "submitter-1"},
		{ID: "submitter-2"},
	}
	for _, submitter := range submitters {
		require.NoError(t, db.Create(&submitter).Error)
	}

	// Create test families
	families := []database.Family{
		{ID: "family-1"},
		{ID: "family-2"},
		{ID: "family-3"},
	}
	for _, family := range families {
		require.NoError(t, db.Create(&family).Error)
	}

	// Create test surnames
	surnames := []database.Surname{
		{ID: 1, Surname: "Smith"},
		{ID: 2, Surname: "Johnson"},
		{ID: 3, Surname: "Williams"},
	}
	for _, surname := range surnames {
		require.NoError(t, db.Create(&surname).Error)
	}
}

// setupTestRouter creates a test router with the analytics handler
func setupTestRouter(t *testing.T) (*gin.Engine, *AnalyticsHandler) {
	gin.SetMode(gin.TestMode)

	db := setupTestDB(t)
	seedTestData(t, db)

	logger := log.New()
	logger.SetLevel(log.ErrorLevel) // Reduce noise in tests

	handler := NewAnalyticsHandler(db, logger)

	router := gin.New()
	handler.RegisterRoutes(router)

	return router, handler
}

func TestAnalyticsHandler_GetOverview(t *testing.T) {
	router, _ := setupTestRouter(t)

	req, err := http.NewRequest("GET", "/api/v1/analytics/overview", nil)
	require.NoError(t, err)

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var response OverviewResponse
	err = json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	// Verify the response contains expected data
	assert.Equal(t, int64(4), response.TotalDNAMatches)
	assert.Equal(t, int64(5), response.TotalIndividuals)
	assert.Equal(t, int64(3), response.TotalTrees)
	assert.Equal(t, int64(4), response.TotalSharedSegments)
	assert.Equal(t, int64(2), response.TotalSubmitters)
	assert.Equal(t, int64(3), response.TotalFamilies)
	assert.Equal(t, int64(3), response.TotalSurnames)
	assert.NotZero(t, response.LastUpdated)
}

func TestAnalyticsHandler_GetMatchStats(t *testing.T) {
	router, _ := setupTestRouter(t)

	req, err := http.NewRequest("GET", "/api/v1/analytics/matches/stats", nil)
	require.NoError(t, err)

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var response MatchStatsResponse
	err = json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	// Verify the response structure and basic data
	assert.Equal(t, int64(4), response.TotalMatches)
	assert.Greater(t, response.AvgSharedCM, 0.0)
	assert.Greater(t, response.MaxSharedCM, 0.0)
	assert.Greater(t, response.MinSharedCM, 0.0)
	assert.NotNil(t, response.MatchesByConfidence)
	assert.NotNil(t, response.MatchesByRelationship)

	// Check that we have confidence levels
	assert.Contains(t, response.MatchesByConfidence, "high")
	assert.Contains(t, response.MatchesByConfidence, "medium")
	assert.Contains(t, response.MatchesByConfidence, "low")

	// Check that we have relationships
	assert.Contains(t, response.MatchesByRelationship, "cousin")
	assert.Contains(t, response.MatchesByRelationship, "sibling")
}

func TestAnalyticsHandler_GetTopCountries(t *testing.T) {
	router, _ := setupTestRouter(t)

	req, err := http.NewRequest("GET", "/api/v1/analytics/geography/top-countries", nil)
	require.NoError(t, err)

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var response TopCountriesResponse
	err = json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	// Verify the response structure
	assert.NotNil(t, response.Countries)
	assert.Greater(t, response.Total, int64(0))
	assert.GreaterOrEqual(t, len(response.Countries), 1)

	// Check that countries have the expected structure
	if len(response.Countries) > 0 {
		country := response.Countries[0]
		assert.NotEmpty(t, country.Country)
		assert.Greater(t, country.Count, int64(0))
	}
}

func TestAnalyticsHandler_GetIndividualStats(t *testing.T) {
	router, _ := setupTestRouter(t)

	req, err := http.NewRequest("GET", "/api/v1/analytics/individuals/stats", nil)
	require.NoError(t, err)

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var response IndividualStatsResponse
	err = json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	// Verify the response structure and data
	assert.Equal(t, int64(5), response.TotalIndividuals)
	assert.Equal(t, int64(3), response.IndividualsWithTrees) // 3 individuals have tree_id
	assert.NotNil(t, response.GenderDistribution)
	assert.NotNil(t, response.AgeGroupDistribution)

	// Check gender distribution
	assert.Contains(t, response.GenderDistribution, "M")
	assert.Contains(t, response.GenderDistribution, "F")
	assert.Contains(t, response.GenderDistribution, "U")

	// Check age group distribution
	assert.Contains(t, response.AgeGroupDistribution, "A")
	assert.Contains(t, response.AgeGroupDistribution, "C")
}

func TestAnalyticsHandler_GetNetworkGraph(t *testing.T) {
	router, _ := setupTestRouter(t)

	tests := []struct {
		name           string
		queryParams    string
		expectedStatus int
	}{
		{
			name:           "default parameters",
			queryParams:    "",
			expectedStatus: http.StatusOK,
		},
		{
			name:           "with min_cm filter",
			queryParams:    "?min_cm=50",
			expectedStatus: http.StatusOK,
		},
		{
			name:           "with limit",
			queryParams:    "?limit=2",
			expectedStatus: http.StatusOK,
		},
		{
			name:           "with multiple parameters",
			queryParams:    "?min_cm=30&limit=5&depth=3",
			expectedStatus: http.StatusOK,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req, err := http.NewRequest("GET", "/api/v1/analytics/network/graph"+tt.queryParams, nil)
			require.NoError(t, err)

			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)

			if tt.expectedStatus == http.StatusOK {
				var response NetworkGraphResponse
				err = json.Unmarshal(w.Body.Bytes(), &response)
				require.NoError(t, err)

				// Verify response structure
				assert.NotNil(t, response.Nodes)
				assert.NotNil(t, response.Edges)
				assert.NotNil(t, response.Stats)
				assert.GreaterOrEqual(t, response.Stats.NodeCount, 0)
				assert.GreaterOrEqual(t, response.Stats.EdgeCount, 0)
				assert.GreaterOrEqual(t, response.Stats.MinCM, 0.0)
				assert.GreaterOrEqual(t, response.Stats.Depth, 1)

				// If we have edges, verify their structure
				for _, edge := range response.Edges {
					assert.NotEmpty(t, edge.Source)
					assert.NotEmpty(t, edge.Target)
					assert.GreaterOrEqual(t, edge.Weight, 0.0)
				}

				// If we have nodes, verify their structure
				for _, node := range response.Nodes {
					assert.NotEmpty(t, node.ID)
					assert.NotEmpty(t, node.Label)
					assert.Equal(t, "individual", node.Type)
				}
			}
		})
	}
}

func TestAnalyticsHandler_GetNetworkCliques(t *testing.T) {
	router, _ := setupTestRouter(t)

	tests := []struct {
		name           string
		queryParams    string
		expectedStatus int
	}{
		{
			name:           "default parameters",
			queryParams:    "",
			expectedStatus: http.StatusOK,
		},
		{
			name:           "with min_size filter",
			queryParams:    "?min_size=2",
			expectedStatus: http.StatusOK,
		},
		{
			name:           "with min_cm filter",
			queryParams:    "?min_cm=100",
			expectedStatus: http.StatusOK,
		},
		{
			name:           "with limit",
			queryParams:    "?limit=10",
			expectedStatus: http.StatusOK,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req, err := http.NewRequest("GET", "/api/v1/analytics/network/cliques"+tt.queryParams, nil)
			require.NoError(t, err)

			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)

			if tt.expectedStatus == http.StatusOK {
				var response NetworkCliquesResponse
				err = json.Unmarshal(w.Body.Bytes(), &response)
				require.NoError(t, err)

				// Verify response structure
				assert.NotNil(t, response.Cliques)
				assert.NotNil(t, response.Stats)
				assert.GreaterOrEqual(t, response.Stats.TotalCliques, 0)
				assert.GreaterOrEqual(t, response.Stats.AvgSize, 0.0)
				assert.GreaterOrEqual(t, response.Stats.MaxSize, 0)
				assert.GreaterOrEqual(t, response.Stats.MinSize, 0)

				// If we have cliques, verify their structure
				for _, clique := range response.Cliques {
					assert.Greater(t, clique.ID, 0)
					assert.NotNil(t, clique.Members)
					assert.Greater(t, clique.Size, 0)
					assert.GreaterOrEqual(t, clique.AvgCM, 0.0)
				}
			}
		})
	}
}

func TestAnalyticsHandler_GetMatches(t *testing.T) {
	router, _ := setupTestRouter(t)

	tests := []struct {
		name           string
		queryParams    string
		expectedStatus int
		minMatches     int
	}{
		{
			name:           "default parameters",
			queryParams:    "",
			expectedStatus: http.StatusOK,
			minMatches:     4, // We have 4 test matches
		},
		{
			name:           "with min_cm filter",
			queryParams:    "?min_cm=100",
			expectedStatus: http.StatusOK,
			minMatches:     2, // Should filter to matches with >= 100 cM
		},
		{
			name:           "with relationship filter",
			queryParams:    "?relationship=cousin",
			expectedStatus: http.StatusOK,
			minMatches:     1, // Should find 1 cousin match
		},
		{
			name:           "with confidence filter",
			queryParams:    "?confidence=high",
			expectedStatus: http.StatusOK,
			minMatches:     2, // Should find 2 high confidence matches
		},
		{
			name:           "with pagination",
			queryParams:    "?page=1&limit=2",
			expectedStatus: http.StatusOK,
			minMatches:     0, // Could be 0-2 depending on filters
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req, err := http.NewRequest("GET", "/api/v1/analytics/matches"+tt.queryParams, nil)
			require.NoError(t, err)

			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)

			if tt.expectedStatus == http.StatusOK {
				var response MatchesResponse
				err = json.Unmarshal(w.Body.Bytes(), &response)
				require.NoError(t, err)

				// Verify response structure
				assert.NotNil(t, response.Matches)
				assert.GreaterOrEqual(t, response.Total, int64(0))
				assert.GreaterOrEqual(t, response.Page, 1)
				assert.GreaterOrEqual(t, response.Limit, 1)

				// Verify matches structure
				for _, match := range response.Matches {
					assert.NotEmpty(t, match.ID)
					assert.NotEmpty(t, match.Person1ID)
					assert.NotEmpty(t, match.Person2ID)
					assert.GreaterOrEqual(t, match.SharedCM, 0.0)
				}
			}
		})
	}
}

func TestAnalyticsHandler_GetIndividuals(t *testing.T) {
	router, _ := setupTestRouter(t)

	tests := []struct {
		name           string
		queryParams    string
		expectedStatus int
	}{
		{
			name:           "default parameters",
			queryParams:    "",
			expectedStatus: http.StatusOK,
		},
		{
			name:           "with gender filter",
			queryParams:    "?gender=M",
			expectedStatus: http.StatusOK,
		},
		{
			name:           "with age group filter",
			queryParams:    "?age_group=A",
			expectedStatus: http.StatusOK,
		},
		{
			name:           "with birth place filter",
			queryParams:    "?birth_place=USA",
			expectedStatus: http.StatusOK,
		},
		{
			name:           "with pagination",
			queryParams:    "?page=1&limit=3",
			expectedStatus: http.StatusOK,
		},
		{
			name:           "with multiple filters",
			queryParams:    "?gender=F&age_group=A&limit=2",
			expectedStatus: http.StatusOK,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req, err := http.NewRequest("GET", "/api/v1/analytics/individuals"+tt.queryParams, nil)
			require.NoError(t, err)

			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)

			if tt.expectedStatus == http.StatusOK {
				var response IndividualsResponse
				err = json.Unmarshal(w.Body.Bytes(), &response)
				require.NoError(t, err)

				// Verify response structure
				assert.NotNil(t, response.Individuals)
				assert.GreaterOrEqual(t, response.Total, int64(0))
				assert.GreaterOrEqual(t, response.Page, 1)
				assert.GreaterOrEqual(t, response.Limit, 1)

				// Verify individuals structure
				for _, individual := range response.Individuals {
					assert.NotEmpty(t, individual.ID)
					assert.Contains(t, []string{"M", "F", "U"}, individual.Gender)
					assert.NotEmpty(t, individual.AgeGroup)
				}
			}
		})
	}
}

func TestAnalyticsHandler_GetSegments(t *testing.T) {
	router, _ := setupTestRouter(t)

	tests := []struct {
		name           string
		queryParams    string
		expectedStatus int
	}{
		{
			name:           "default parameters",
			queryParams:    "",
			expectedStatus: http.StatusOK,
		},
		{
			name:           "with min_cm filter",
			queryParams:    "?min_cm=50",
			expectedStatus: http.StatusOK,
		},
		{
			name:           "with chromosome filter",
			queryParams:    "?chromosome=1",
			expectedStatus: http.StatusOK,
		},
		{
			name:           "with match_id filter",
			queryParams:    "?match_id=match-1",
			expectedStatus: http.StatusOK,
		},
		{
			name:           "with pagination",
			queryParams:    "?page=1&limit=2",
			expectedStatus: http.StatusOK,
		},
		{
			name:           "with multiple filters",
			queryParams:    "?min_cm=30&chromosome=1&limit=5",
			expectedStatus: http.StatusOK,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req, err := http.NewRequest("GET", "/api/v1/analytics/segments"+tt.queryParams, nil)
			require.NoError(t, err)

			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)

			if tt.expectedStatus == http.StatusOK {
				var response SegmentsResponse
				err = json.Unmarshal(w.Body.Bytes(), &response)
				require.NoError(t, err)

				// Verify response structure
				assert.NotNil(t, response.Segments)
				assert.GreaterOrEqual(t, response.Total, int64(0))
				assert.GreaterOrEqual(t, response.Page, 1)
				assert.GreaterOrEqual(t, response.Limit, 1)

				// Verify segments structure
				for _, segment := range response.Segments {
					assert.NotEmpty(t, segment.ID)
					assert.NotEmpty(t, segment.MatchID)
					assert.NotEmpty(t, segment.Chromosome)
					assert.Greater(t, segment.StartPos, int64(0))
					assert.Greater(t, segment.EndPos, segment.StartPos)
					assert.GreaterOrEqual(t, segment.Length, 0.0)
				}
			}
		})
	}
}

// Test error cases
func TestAnalyticsHandler_ErrorCases(t *testing.T) {
	router, _ := setupTestRouter(t)

	tests := []struct {
		name           string
		endpoint       string
		queryParams    string
		expectedStatus int
	}{
		{
			name:           "invalid page parameter",
			endpoint:       "/api/v1/analytics/matches",
			queryParams:    "?page=0",
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "invalid limit parameter",
			endpoint:       "/api/v1/analytics/individuals",
			queryParams:    "?limit=0",
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "invalid min_cm parameter",
			endpoint:       "/api/v1/analytics/segments",
			queryParams:    "?min_cm=-1",
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "invalid chromosome parameter",
			endpoint:       "/api/v1/analytics/segments",
			queryParams:    "?chromosome=0",
			expectedStatus: http.StatusBadRequest,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req, err := http.NewRequest("GET", tt.endpoint+tt.queryParams, nil)
			require.NoError(t, err)

			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)

			if tt.expectedStatus == http.StatusBadRequest {
				var response map[string]interface{}
				err = json.Unmarshal(w.Body.Bytes(), &response)
				require.NoError(t, err)
				assert.Contains(t, response, "error")
			}
		})
	}
}

// Benchmark tests for performance
func BenchmarkAnalyticsHandler_GetOverview(b *testing.B) {
	router, _ := setupTestRouter(&testing.T{})

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		req, _ := http.NewRequest("GET", "/api/v1/analytics/overview", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)
	}
}

func BenchmarkAnalyticsHandler_GetMatches(b *testing.B) {
	router, _ := setupTestRouter(&testing.T{})

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		req, _ := http.NewRequest("GET", "/api/v1/analytics/matches?limit=10", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)
	}
}

func BenchmarkAnalyticsHandler_GetNetworkGraph(b *testing.B) {
	router, _ := setupTestRouter(&testing.T{})

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		req, _ := http.NewRequest("GET", "/api/v1/analytics/network/graph?limit=5", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)
	}
}
