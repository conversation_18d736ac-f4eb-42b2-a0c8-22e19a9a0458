package api

import (
	"database/sql"

	"github.com/dmagur/myheritage/internal/fetch"
	log "github.com/sirupsen/logrus"
)

// DBQuerier is an interface for database query operations
type DBQuerier interface {
	QueryRow(query string, args ...interface{}) RowScanner
}

// RowScanner is an interface for scanning rows
type RowScanner interface {
	Scan(dest ...interface{}) error
}

// sqlDBWrapper wraps sql.DB to implement DBQuerier
type sqlDBWrapper struct {
	db *sql.DB
}

func (w *sqlDBWrapper) QueryRow(query string, args ...interface{}) RowScanner {
	return w.db.QueryRow(query, args...)
}

// TokenProvider retrieves tokens from the database
type TokenProvider struct {
	db     DBQuerier
	logger *log.Logger
}

// NewTokenProvider creates a new TokenProvider
func NewTokenProvider(db *sql.DB, logger *log.Logger) *TokenProvider {
	return &TokenProvider{
		db:     &sqlDBWrapper{db: db},
		logger: logger,
	}
}

// GetToken retrieves the current active token from the database
func (p *TokenProvider) GetToken() string {
	var token string

	// Try with boolean syntax for PostgreSQL
	p.logger.Info("Attempting to retrieve token from database with PostgreSQL syntax")
	err := p.db.QueryRow(`
		SELECT token
		FROM api_tokens
		WHERE is_active = true
		ORDER BY created_at DESC
		LIMIT 1
	`).Scan(&token)

	if err != nil {
		if err != sql.ErrNoRows {
			p.logger.Warnf("Error retrieving token with PostgreSQL syntax: %v", err)

			// Try with numeric syntax for MariaDB/MySQL (1 = true)
			p.logger.Info("Attempting to retrieve token from database with MariaDB syntax")
			err = p.db.QueryRow(`
				SELECT token
				FROM api_tokens
				WHERE is_active = 1
				ORDER BY created_at DESC
				LIMIT 1
			`).Scan(&token)

			if err != nil {
				if err != sql.ErrNoRows {
					p.logger.Errorf("Error retrieving token with MariaDB syntax: %v", err)
				} else {
					p.logger.Warn("No token found in database (MariaDB query returned no rows)")
				}
				// Return empty string if no token is found
				return ""
			} else {
				p.logger.Infof("Successfully retrieved token from database with MariaDB syntax: %s (FULL TOKEN)", token)
			}
		} else {
			// Return empty string if no rows found
			p.logger.Warn("No token found in database (PostgreSQL query returned no rows)")
			return ""
		}
	} else {
		p.logger.Infof("Successfully retrieved token from database with PostgreSQL syntax: %s (FULL TOKEN)", token)
	}

	return token
}

// GetBearerToken returns a fetch.BearerToken
func (p *TokenProvider) GetBearerToken() fetch.BearerToken {
	return fetch.BearerToken(p.GetToken())
}
