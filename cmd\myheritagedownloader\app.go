package main

import (
	"fmt"
	"github.com/dmagur/myheritage/internal/myheritage/dnamatch"
	"github.com/dmagur/myheritage/internal/myheritage/pedigree"
	"github.com/dmagur/myheritage/internal/myheritage/sharedmatches"
	"github.com/dmagur/myheritage/internal/myheritage/sharedsegments"
	"github.com/dmagur/myheritage/internal/myheritage/sharedsurnames"

	"github.com/sirupsen/logrus"
)

type App struct {
	dnamatchHandler       dnamatch.HandlerInterface
	sharedmatchesHandler  sharedmatches.HandlerInterface
	sharedsurnamesHandler sharedsurnames.HandlerInterface
	sharedsegmentsHandler sharedsegments.HandlerInterface
	pedigreeHandler       pedigree.HandlerInterface
	logger                *logrus.Logger
}

func NewApp(
	dnamatchHandler dnamatch.HandlerInterface,
	sharedmatchesHandler sharedmatches.HandlerInterface,
	sharedsurnamesHandler sharedsurnames.HandlerInterface,
	sharedsegmentsHandler sharedsegments.HandlerInterface,
	pedigreeHandler pedigree.HandlerInterface,
	logger *logrus.Logger,
) App {
	return App{
		dnamatchHandler:       dnamatchHandler,
		sharedmatchesHandler:  sharedmatchesHandler,
		sharedsurnamesHandler: sharedsurnamesHandler,
		sharedsegmentsHandler: sharedsegmentsHandler,
		pedigreeHandler:       pedigreeHandler,
		logger:                logger,
	}
}

func (a App) Run() error {
	if err := a.dnamatchHandler.Handle(); err != nil {
		return fmt.Errorf("dna matches handler errors: %w", err)
	}

	if err := a.pedigreeHandler.Handle(); err != nil {

		return fmt.Errorf("pedigree handler errors: %w", err)
	}

	if err := a.sharedsurnamesHandler.Handle(); err != nil {
		return fmt.Errorf("shared surnames handler errors: %w", err)
	}

	if err := a.sharedsegmentsHandler.Handle(); err != nil {
		return fmt.Errorf("shared segments handler errors: %w", err)
	}

	if err := a.sharedmatchesHandler.Handle(); err != nil {
		return fmt.Errorf("shared matches handler errors: %w", err)
	}

	return nil
}
