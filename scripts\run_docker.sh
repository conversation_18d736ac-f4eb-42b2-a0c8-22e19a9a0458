#!/bin/bash

# Run the API server with Docker
echo "Building and starting the API server with Docker..."

# Build and start the containers
docker-compose up -d --build

# Wait for the API server to start
echo "Waiting for the API server to start..."
MAX_RETRIES=10
RETRY_COUNT=0
SERVER_STARTED=false

while [ "$SERVER_STARTED" = false ] && [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
    if curl -s http://localhost:1231/health > /dev/null 2>&1; then
        echo "API server started successfully"
        SERVER_STARTED=true
    else
        echo "Waiting for API server to start... ($(($RETRY_COUNT + 1))/$MAX_RETRIES)"
        sleep 1
        RETRY_COUNT=$((RETRY_COUNT + 1))
    fi
done

if [ "$SERVER_STARTED" = false ]; then
    echo "Failed to start API server within the timeout period"
    echo "Check the logs with: docker-compose logs apiserver"
    exit 1
fi

# Show the available endpoints
echo ""
echo "API server is running at http://localhost:1231"
echo ""
echo "Available endpoints:"
echo "- API Documentation: http://localhost:1231/swagger"
echo "- Token Management: http://localhost:1231/api/v1/token"
echo "- DNA Matches: http://localhost:1231/dna-matches"
echo "- Health: http://localhost:1231/health"
echo ""
echo "To stop the API server, run: docker-compose down"
