package sharedsegments

import (
	"encoding/json"
	"fmt"
	"github.com/dmagur/myheritage/internal/fetch"
	log "github.com/sirupsen/logrus"
	"math/rand"
	"time"
)

type HandlerInterface interface {
	Handle() error
}

type Handler struct {
	client     *fetch.MyHeritageApiClient
	logger     *log.Logger
	repository *Repository
}

func NewHandler(
	client *fetch.MyHeritageApiClient,
	logger *log.Logger,
	repository *Repository,
) Handler {
	return Handler{
		client:     client,
		logger:     logger,
		repository: repository,
	}
}

func (h Handler) Handle() error {
	dnaMatchIDs, err := h.repository.GetDnaMatchesWithoutSharedSegments(0, 1500)

	if err != nil {
		return fmt.Errorf("error getting DNA matches from DB: %w", err)
	}

	offset := 0
	// Now loop through the slice of DnaMatch
	for _, match := range dnaMatchIDs {
		// Output the match details
		fmt.Println("-Shared segments-")
		fmt.Printf("Offset: %d\n", offset)
		fmt.Printf("Match ID: %s\n", match.ID)
		fmt.Printf("Match Individual ID: %s\n", match.MatchIndividualID)
		offset++

		sharedCount := 0
		apiResponse, err := h.Fetch(match.ID)

		if err != nil {
			return fmt.Errorf("error fetching DNA matches: %w", err)
		}

		fmt.Printf("Shared segments count: %d\n", apiResponse.Data.DnaMatch.DnaSharedSegments.Count)

		err = h.repository.saveDnaMatchSharedSegments(apiResponse.Data.DnaMatch.DnaSharedSegments.Data, match.ID)

		if err != nil {
			return fmt.Errorf("error upserting shared segments: %w", err)
		}

		randomNumber := rand.Intn(10) + 1
		time.Sleep(time.Duration(randomNumber) * time.Second)

		fmt.Printf("Surname count: %d\n", sharedCount)
		err = h.repository.updateSharedSegmentsCount(match.ID, apiResponse.Data.DnaMatch.DnaSharedSegments.Count)
		if err != nil {
			return fmt.Errorf("error updating shared segments count: %w", err)
		}
	}

	return nil
}

func (h *Handler) Fetch(dnaMatchId string) (*ApiResponse, error) {
	url := "https://familygraphql.myheritage.com/dna_single_match_get_shared_segments/"

	query := fmt.Sprintf(`"{system(id:\"system-0\"){dna_enums{data{chromosomes,not_sampled_chromosome_segments}}}dna_match(id:\"%s\"){id can_view_shared_segments dna_kit{eligibility_variant}dna_shared_segments{count data{chromosome_id start_position end_position start_rsid end_rsid length_in_centimorgans snp_count}}}}"`,
		dnaMatchId)

	body, err := h.client.Fetch(url, query, "DNA Single Match - get shared segments")
	if err != nil {
		fmt.Println("Error fetching response:", err)
		return nil, err
	}

	var apiResponse ApiResponse
	err = json.Unmarshal(body, &apiResponse)
	if err != nil {
		// Print the response body if JSON unmarshalling fails
		fmt.Println("Error unmarshalling JSON response:", err)
		fmt.Printf("Response body: %s\n", string(body))
		return nil, err
	}

	return &apiResponse, nil
}
