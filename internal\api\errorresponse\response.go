package errorresponse

import (
	"errors"
	"fmt"
	"net/http"
	"runtime/debug"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"

	"golang.org/x/text/cases"
	"golang.org/x/text/language"
)

var caser = cases.Title(language.English) //nolint:gochecknoglobals

// APIErrorResponse Structure taken from https://gist.github.com/igorjs/407ffc3126f6ef2a6fe8f918a0673b59
type APIErrorResponse struct {
	Message string   `json:"message"`
	Trace   []string `json:"trace"`
}

// APIValidationErrorResponse Structure taken from https://gist.github.com/igorjs/407ffc3126f6ef2a6fe8f918a0673b59
type APIValidationErrorResponse struct {
	Errors  []validationErrMap `json:"errors"`
	Message string             `json:"message"`
	Trace   []string           `json:"trace"`
}

type validationErrMap map[string]fieldValidationErrorResponse

type fieldValidationErrorResponse struct {
	Message string `json:"message"`
}

func ErrorHandler(context *gin.Context, err any) {
	var responseErr ResponseError

	var validationErrs validator.ValidationErrors

	stringErr, isStr := err.(string)
	assertedErr, ok := err.(error)

	if !ok {
		return
	}

	switch {
	case isStr:
		httpResponse := APIErrorResponse{
			Message: caser.String(stringErr),
			Trace:   getStackTrace(),
		}
		context.AbortWithStatusJSON(http.StatusInternalServerError, httpResponse)

		return
	case errors.As(assertedErr, &responseErr):
		handleResponseErr(context, responseErr)

		return
	case errors.As(assertedErr, &validationErrs):
		handleValidationErr(context, validationErrs)

		return
	default:
		httpResponse := APIErrorResponse{
			Message: caser.String(assertedErr.Error()),
			Trace:   getStackTrace(),
		}
		context.AbortWithStatusJSON(http.StatusInternalServerError, httpResponse)
	}
}

func handleResponseErr(c *gin.Context, responseErr ResponseError) {
	httpResponse := APIErrorResponse{
		Message: caser.String(responseErr.Error()),
		Trace:   getStackTrace(),
	}
	c.AbortWithStatusJSON(int(responseErr.Code), httpResponse)
}

func handleValidationErr(context *gin.Context, validationErrs validator.ValidationErrors) {
	errList := make([]validationErrMap, 0)

	for _, fieldErr := range validationErrs {
		errList = append(errList, validationErrMap{
			fieldErr.StructNamespace(): fieldValidationErrorResponse{
				Message: fmt.Sprintf(
					"Field validation failed on the '%s' tag",
					fieldErr.Tag(),
				),
			},
		})
	}

	httpResponse := APIValidationErrorResponse{
		Errors:  errList,
		Message: "Validation errors in your request",
		Trace:   getStackTrace(),
	}

	context.AbortWithStatusJSON(http.StatusBadRequest, httpResponse)
}

func getStackTrace() []string {
	if gin.Mode() == gin.ReleaseMode {
		return nil
	}

	trace := string(debug.Stack())
	trace = strings.ReplaceAll(trace, "\t", "")

	return strings.Split(trace, "\n")
}
