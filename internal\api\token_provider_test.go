package api

import (
	"database/sql"
	"errors"
	"testing"

	log "github.com/sirupsen/logrus"
)

// mockDB implements the DBQuerier interface for testing
type mockDB struct {
	queryRowFunc func(query string, args ...interface{}) RowScanner
}

func (db *mockDB) QueryRow(query string, args ...interface{}) RowScanner {
	return db.queryRowFunc(query, args...)
}

// mockRow implements the RowScanner interface for testing
type mockRow struct {
	scanFunc func(dest ...interface{}) error
}

func (r *mockRow) Scan(dest ...interface{}) error {
	return r.scanFunc(dest...)
}

func TestTokenProvider_GetToken_Success(t *testing.T) {
	// Create a mock row that returns a token
	row := &mockRow{
		scanFunc: func(dest ...interface{}) error {
			// Set the token value
			*dest[0].(*string) = "test-token"
			return nil
		},
	}

	// Create a mock DB that returns the mock row
	db := &mockDB{
		queryRowFunc: func(query string, args ...interface{}) RowScanner {
			return row
		},
	}

	// Create a logger
	logger := log.New()

	// Create a token provider with the mock DB
	provider := &TokenProvider{
		db:     db,
		logger: logger,
	}

	// Call the GetToken method
	token := provider.GetToken()

	// Check the result
	if token != "test-token" {
		t.Errorf("Expected token 'test-token', got '%s'", token)
	}
}

func TestTokenProvider_GetToken_NoRows(t *testing.T) {
	// Create a mock row that returns sql.ErrNoRows
	row := &mockRow{
		scanFunc: func(dest ...interface{}) error {
			return sql.ErrNoRows
		},
	}

	// Create a mock DB that returns the mock row
	db := &mockDB{
		queryRowFunc: func(query string, args ...interface{}) RowScanner {
			return row
		},
	}

	// Create a logger
	logger := log.New()

	// Create a token provider with the mock DB
	provider := &TokenProvider{
		db:     db,
		logger: logger,
	}

	// Call the GetToken method
	token := provider.GetToken()

	// Check the result
	if token != "" {
		t.Errorf("Expected empty token, got '%s'", token)
	}
}

func TestTokenProvider_GetToken_Error(t *testing.T) {
	// Create a mock row that returns an error
	row := &mockRow{
		scanFunc: func(dest ...interface{}) error {
			return errors.New("database error")
		},
	}

	// Create a mock DB that returns the mock row
	db := &mockDB{
		queryRowFunc: func(query string, args ...interface{}) RowScanner {
			return row
		},
	}

	// Create a logger
	logger := log.New()

	// Create a token provider with the mock DB
	provider := &TokenProvider{
		db:     db,
		logger: logger,
	}

	// Call the GetToken method
	token := provider.GetToken()

	// Check the result
	if token != "" {
		t.Errorf("Expected empty token, got '%s'", token)
	}
}

func TestTokenProvider_GetBearerToken(t *testing.T) {
	// Create a mock row that returns a token
	row := &mockRow{
		scanFunc: func(dest ...interface{}) error {
			// Set the token value
			*dest[0].(*string) = "test-token"
			return nil
		},
	}

	// Create a mock DB that returns the mock row
	db := &mockDB{
		queryRowFunc: func(query string, args ...interface{}) RowScanner {
			return row
		},
	}

	// Create a logger
	logger := log.New()

	// Create a token provider with the mock DB
	provider := &TokenProvider{
		db:     db,
		logger: logger,
	}

	// Call the GetBearerToken method
	bearerToken := provider.GetBearerToken()

	// Check the result
	if string(bearerToken) != "test-token" {
		t.Errorf("Expected bearer token 'test-token', got '%s'", string(bearerToken))
	}
}
