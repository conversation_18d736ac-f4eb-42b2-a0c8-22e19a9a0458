package database

import (
	"database/sql"
	"fmt"

	_ "github.com/go-sql-driver/mysql"
)

// NewMariaDB creates a new MariaDB connection using environment variables
// This function is kept for backward compatibility with existing code that still uses raw SQL
// New code should use NewGormDB instead
func NewMariaDB() *sql.DB {
	host := getEnv("DB_HOST", "localhost")
	user := getEnv("DB_USER", "myheritage")
	password := getEnv("DB_PASSWORD", "mysecretpassword")
	dbname := getEnv("DB_NAME", "dna_match_db")

	// Format: username:password@tcp(host:port)/dbname
	connStr := fmt.Sprintf("%s:%s@tcp(%s:3306)/%s", user, password, host, dbname)

	db, err := sql.Open("mysql", connStr)
	if err != nil {
		panic(err)
	}

	return db
}

// NewMariaDBWithConnStr creates a new MariaDB connection with the given connection string
// This function is kept for backward compatibility with existing code that still uses raw SQL
// New code should use NewGormDB instead
func NewMariaDBWithConnStr(connStr ConnString) *sql.DB {
	db, err := sql.Open("mysql", string(connStr))
	if err != nil {
		panic(err)
	}

	return db
}

// NewSqlDB creates a database connection based on the DB_TYPE environment variable
// This function is kept for backward compatibility with existing code that still uses raw SQL
// New code should use NewGormDB instead
func NewSqlDB(connStr ConnString) *sql.DB {
	dbType := getEnv("DB_TYPE", "postgres")

	switch dbType {
	case "mariadb", "mysql":
		return NewMariaDBWithConnStr(connStr)
	case "postgres", "postgresql":
		return NewPgSql(connStr)
	default:
		// Default to PostgreSQL
		return NewPgSql(connStr)
	}
}
