# MyHeritage Database Visualizer App - Comprehensive Plan

## 🎯 **Project Overview**

A comprehensive web-based visualizer application to analyze and visualize the contents of the MyHeritage DNA database, providing insights into DNA matches, family relationships, geographic patterns, and data quality.

## 📊 **Database Overview (What We're Working With)**

**Major Data Tables:**
- **dna_matches**: 1,460,538 records - Core DNA match data
- **individuals**: 308,823 records - Person information  
- **trees**: 23,236 records - Family trees
- **shared_segment**: 25,805 records - DNA segment details
- **family**: 209,313 records - Family relationships
- **submitters**: 24,084 records - DNA kit submitters
- **surname**: 150,377 records - Surname data

## 🏗️ **Proposed Architecture**

### **Frontend: React Web Application**
```
visualizer-web/
├── src/
│   ├── components/
│   │   ├── Dashboard/           # Main dashboard
│   │   ├── Charts/             # Chart components
│   │   ├── Tables/             # Data tables
│   │   ├── Filters/            # Filter controls
│   │   ├── NetworkGraph/       # 3D/2D network visualization
│   │   └── Maps/               # Geographic visualization
│   ├── pages/
│   │   ├── Overview/           # Database overview
│   │   ├── DNAMatches/         # DNA match analysis
│   │   ├── Individuals/        # Individual profiles
│   │   ├── Trees/              # Family tree analysis
│   │   ├── Geography/          # Geographic distribution
│   │   └── Segments/           # DNA segment analysis
│   ├── hooks/                  # Custom React hooks
│   ├── services/               # API services
│   └── utils/                  # Utility functions
```

### **Backend: Enhanced API Server**
```
cmd/visualizer/                 # New visualizer API server
internal/visualizer/
├── handlers/                   # HTTP handlers
├── services/                   # Business logic
├── analytics/                  # Data analysis functions
└── models/                     # Response models
```

## 🎨 **Core Features & Visualizations**

### **1. Dashboard Overview**
- **Database Statistics**: Live counts, growth trends
- **Quick Insights**: Top matches, recent activity, data quality metrics
- **System Health**: API status, database performance

### **2. DNA Match Analysis**
- **Match Distribution**: Histogram of shared cM values
- **Relationship Mapping**: Visual relationship degree analysis
- **Match Timeline**: When matches were discovered
- **Geographic Clustering**: Where matches are located
- **Confidence Analysis**: Match confidence level distribution

### **3. Network Visualizations**
- **3D Force Graph**: Enhanced version of existing 3D graph
- **2D Network**: Interactive 2D network with clustering
- **Hierarchical Trees**: Family tree structures
- **Clique Analysis**: Visual clique detection and analysis

### **4. Individual Analysis**
- **Individual Profiles**: Detailed person information
- **Connection Maps**: How individuals connect to others
- **Surname Analysis**: Surname distribution and patterns
- **Age Group Analysis**: Demographics visualization

### **5. Geographic Visualization**
- **World Map**: DNA matches by country/region
- **Migration Patterns**: Potential migration routes
- **Density Heatmaps**: Geographic concentration of matches

### **6. DNA Segment Analysis**
- **Chromosome Browser**: Visual chromosome segment mapping
- **Segment Length Distribution**: Analysis of shared segments
- **Triangulation**: Identify triangulated segments

### **7. Data Quality & Analytics**
- **Data Completeness**: Missing data analysis
- **Duplicate Detection**: Identify potential duplicates
- **Data Trends**: Growth patterns over time
- **Export Tools**: Data export capabilities

## 🛠️ **Technology Stack**

### **Frontend**
- **React 18** with TypeScript
- **Material-UI** or **Ant Design** for components
- **D3.js** for custom visualizations
- **Three.js** for 3D visualizations (enhance existing)
- **Leaflet** or **Mapbox** for geographic maps
- **Recharts** or **Chart.js** for standard charts
- **React Query** for data fetching

### **Backend**
- **Go** (consistent with existing stack)
- **Gin** for HTTP routing (consistent with apiserver)
- **GORM** for database operations (consistent with migration)
- **Wire** for dependency injection (consistent with pattern)

### **Database**
- **MariaDB** (current database)
- **Redis** (optional, for caching complex queries)

## 📋 **Implementation Plan**

### **Phase 1: Foundation (Week 1-2)**
1. **Enhanced API Endpoints**
   ```go
   GET /api/v1/analytics/overview          # Dashboard data
   GET /api/v1/analytics/matches/stats     # Match statistics
   GET /api/v1/analytics/individuals/stats # Individual statistics
   GET /api/v1/analytics/geography         # Geographic data
   ```

2. **Basic React App Setup**
   - Project structure
   - Routing setup
   - API service layer
   - Basic dashboard layout

### **Phase 2: Core Visualizations (Week 3-4)**
1. **Dashboard Implementation**
   - Database statistics
   - Quick insights
   - Real-time updates

2. **DNA Match Analysis**
   - Match distribution charts
   - Relationship mapping
   - Basic filtering

### **Phase 3: Advanced Features (Week 5-6)**
1. **Network Visualizations**
   - Enhanced 3D graph (building on existing work)
   - Interactive 2D network
   - Performance optimization

2. **Geographic Features**
   - World map integration
   - Location-based filtering
   - Migration pattern analysis

### **Phase 4: Specialized Analysis (Week 7-8)**
1. **DNA Segment Analysis**
   - Chromosome browser
   - Segment visualization
   - Triangulation tools

2. **Data Quality Tools**
   - Duplicate detection
   - Data completeness analysis
   - Export functionality

## 🔧 **Specific API Endpoints Needed**

```go
// Analytics endpoints
GET /api/v1/analytics/overview
GET /api/v1/analytics/matches/distribution
GET /api/v1/analytics/matches/by-relationship
GET /api/v1/analytics/matches/by-location
GET /api/v1/analytics/individuals/demographics
GET /api/v1/analytics/segments/distribution
GET /api/v1/analytics/geography/countries
GET /api/v1/analytics/geography/regions

// Data endpoints with filtering
GET /api/v1/matches?limit=100&offset=0&min_cm=20&max_cm=100
GET /api/v1/individuals?tree_id=123&country=US
GET /api/v1/segments?chromosome=1&min_length=5

// Network data
GET /api/v1/network/graph?depth=3&min_cm=20
GET /api/v1/network/cliques?min_size=3
```

## 🎯 **Key Benefits**

1. **Comprehensive Analysis**: See patterns in 1.4M+ DNA matches
2. **Interactive Exploration**: Drill down from overview to specific details
3. **Geographic Insights**: Understand geographic distribution of matches
4. **Data Quality**: Identify and fix data issues
5. **Research Tools**: Advanced tools for genealogical research
6. **Performance**: Optimized for large datasets
7. **Extensible**: Easy to add new visualizations and features

## 🚀 **Next Steps**

### **Recommended Starting Points:**
1. **Enhanced API Endpoints** - Add analytics endpoints to existing apiserver
2. **React Frontend Structure** - Set up visualizer web application
3. **Specific Visualization** - Focus on one area (e.g., DNA match analysis)
4. **Development Environment** - Docker setup for visualizer

### **Decision Points:**
- Integration with existing apiserver vs. separate visualizer service
- Frontend framework preferences (Material-UI vs. Ant Design)
- Deployment strategy (Docker, standalone, integrated)
- Performance requirements for large dataset visualization

---

**Created**: 2025-05-24  
**Status**: Proposal - Awaiting approval and implementation decisions  
**Dependencies**: Existing MyHeritage database, API server, React preferences
