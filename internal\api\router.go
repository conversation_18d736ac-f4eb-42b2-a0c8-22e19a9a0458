package api

import (
	"github.com/dmagur/myheritage/internal/api/errorresponse"
	"github.com/dmagur/myheritage/internal/api/middleware"
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	log "github.com/sirupsen/logrus"
)

const (
	day = 24
)

type AuthToken string

func NewRouter(authToken AuthToken) *gin.Engine {
	token := middleware.AuthToken(authToken)
	if err := token.Validate(); err != nil {
		log.Fatal(err)
	}

	router := gin.Default()
	gin.EnableJsonDecoderDisallowUnknownFields()

	// set first param to nil in order to not output recovery entry in log
	router.Use(gin.CustomRecoveryWithWriter(nil, errorresponse.ErrorHandler))

	router.Use(cors.New(cors.Config{
		AllowOrigins: []string{"*"},
		AllowMethods: []string{"HEAD", "GET", "POST", "PUT", "PATCH", "DELETE"},
		AllowHeaders: []string{
			"Accept",
			"Accept-Charset",
			"Accept-Encoding",
			"Accept-Language",
			"Authorization",
			"Connection",
			"Content-Type",
			"Cookie",
			"DNT",
			"Host",
			"Keep-Alive",
			"Origin",
			"Referer",
			"User-Agent",
		},
		AllowCredentials: true,
		ExposeHeaders:    []string{"Content-Length"},
		MaxAge:           day * time.Hour,
	}))

	return router
}
