package main

import (
	"database/sql"
)

type Result struct {
	ID                string
	MatchIndividualID string
	SharedLength      float64
	Name              string
}

func getDnaMatchesByIndividualId(db *sql.DB, individualId string, offset int, limit int) ([]Result, error) {
	// Prepare the SQL query
	query := `
		SELECT dm.id, dm.match_individual_id, dm.total_shared_segments_length_in_cm, i.name
		FROM dna_matches dm
		INNER Join individuals i ON dm.match_individual_id = i.id
		WHERE dm.source_individual_id = $1 and dm.link is not null
		ORDER BY id
		OFFSET $2
		LIMIT $3
	`

	// Execute the query
	rows, err := db.Query(query, individualId, offset, limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	// Slice to hold the results
	var dnaMatches []Result

	// Loop through the rows and store them in the slice
	for rows.Next() {
		var dnaMatch Result
		err := rows.Scan(&dnaMatch.ID, &dnaMatch.MatchIndividualID, &dnaMatch.SharedLength, &dnaMatch.Name)
		if err != nil {
			return nil, err
		}
		dnaMatches = append(dnaMatches, dnaMatch)
	}

	// Check for errors after loop completion
	if err := rows.Err(); err != nil {
		return nil, err
	}

	return dnaMatches, nil
}

func getSharedDnaMatchesByIndividualId(db *sql.DB, individualId string, offset int, limit int) ([]Result, error) {
	// Prepare the SQL query
	query := `
		SELECT dm.id, dm.match_individual_id, dm.total_shared_segments_length_in_cm, i.name
		FROM dna_matches dm
		INNER Join individuals i ON dm.match_individual_id = i.id
		WHERE dm.source_individual_id = $1
		ORDER BY id
		OFFSET $2
		LIMIT $3
	`

	// Execute the query
	rows, err := db.Query(query, individualId, offset, limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	// Slice to hold the results
	var dnaMatches []Result

	// Loop through the rows and store them in the slice
	for rows.Next() {
		var dnaMatch Result
		err := rows.Scan(&dnaMatch.ID, &dnaMatch.MatchIndividualID, &dnaMatch.SharedLength, &dnaMatch.Name)
		if err != nil {
			return nil, err
		}
		dnaMatches = append(dnaMatches, dnaMatch)
	}

	// Check for errors after loop completion
	if err := rows.Err(); err != nil {
		return nil, err
	}

	return dnaMatches, nil
}
