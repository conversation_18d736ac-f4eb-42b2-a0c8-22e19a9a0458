package api

import (
	"fmt"
	"strconv"
)

type PortNumber uint16

// CreatePortNumber creates new instance of PortNumber
// It will fall back to port 80 if invalid port number is provided.
func CreatePortNumber(num string) PortNumber {
	numInt, err := strconv.Atoi(num)
	if err != nil {
		numInt = 80
	}

	return PortNumber(numInt)
}

// GetFormatted returns formatted value. E.g.: ":80".
func (n PortNumber) GetFormatted() string {
	return fmt.Sprintf(":%d", n)
}
