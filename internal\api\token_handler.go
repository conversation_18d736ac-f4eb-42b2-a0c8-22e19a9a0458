package api

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	log "github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// TokenHandler handles API requests for token management
type TokenHandler struct {
	db     *gorm.DB
	logger *log.Logger
}

// NewTokenHandler creates a new TokenHandler
func NewTokenHandler(db *gorm.DB, logger *log.Logger) *TokenHandler {
	return &TokenHandler{
		db:     db,
		logger: logger,
	}
}

// RegisterRoutes registers the token management routes
func (h *TokenHandler) RegisterRoutes(router *gin.Engine) {
	// Group API routes under /api/v1
	api := router.Group("/api/v1")
	{
		// Token management endpoints
		api.GET("/token", h.GetToken)
		api.POST("/token", h.UpdateToken)
	}
}

// TokenResponse is the response for token requests
type TokenResponse struct {
	Token     string    `json:"token,omitempty"`
	CreatedAt time.Time `json:"created_at,omitempty"`
	IsActive  bool      `json:"is_active"`
}

// GetToken returns the current active token
func (h *TokenHandler) GetToken(c *gin.Context) {
	var token ApiToken

	// Query the database for the active token
	result := h.db.Where("is_active = ?", true).
		Order("created_at DESC").
		Limit(1).
		First(&token)

	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			// No active token found
			c.JSON(http.StatusOK, gin.H{
				"message": "No active token found",
				"token":   TokenResponse{IsActive: false},
			})
			return
		}

		// Database error
		h.logger.Errorf("Error retrieving token: %v", result.Error)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to retrieve token",
		})
		return
	}

	// Create response
	response := TokenResponse{
		Token:     token.Token,
		CreatedAt: token.CreatedAt.Time,
		IsActive:  token.IsActive,
	}

	// Mask the token for security in the response
	maskedToken := response.Token
	if len(maskedToken) > 10 {
		maskedToken = maskedToken[:5] + "..." + maskedToken[len(maskedToken)-5:]
	}
	response.Token = maskedToken

	c.JSON(http.StatusOK, gin.H{
		"token": response,
	})
}

// UpdateTokenRequest is the request body for updating a token
type UpdateTokenRequest struct {
	Token string `json:"token" binding:"required"`
}

// UpdateToken updates the API token
func (h *TokenHandler) UpdateToken(c *gin.Context) {
	var request UpdateTokenRequest

	// Bind the request body to the struct
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request format",
		})
		return
	}

	// Validate the token
	if len(request.Token) < 10 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Token is too short",
		})
		return
	}

	// Use a transaction for updating tokens
	err := h.db.Transaction(func(tx *gorm.DB) error {
		// Deactivate all existing tokens
		if err := tx.Model(&ApiToken{}).Where("1=1").Update("is_active", false).Error; err != nil {
			return err
		}

		// Insert the new token
		newToken := ApiToken{
			Token:     request.Token,
			CreatedAt: MySQLTime{Time: time.Now()},
			IsActive:  true,
		}
		if err := tx.Create(&newToken).Error; err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		h.logger.Errorf("Error updating token: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to update token",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Token updated successfully",
	})
}
