# Run the API integration tests
Write-Host "Running API integration tests..."

# Check if the API server is already running
$apiServerRunning = $false
try {
    $response = Invoke-WebRequest -Uri "http://localhost:1231/health" -Method GET -TimeoutSec 2 -ErrorAction SilentlyContinue
    if ($response.StatusCode -eq 200) {
        Write-Host "API server is already running"
        $apiServerRunning = $true
    }
} catch {
    Write-Host "API server is not running, starting it now..."
}

# Start the API server if it's not already running
$apiServerProcess = $null
if (-not $apiServerRunning) {
    # Start the API server in a new process
    $apiServerProcess = Start-Process -FilePath "go" -ArgumentList "run", "cmd/apiserver/main.go", "cmd/apiserver/app.go" -PassThru -NoNewWindow

    # Wait for the API server to start
    Write-Host "Waiting for API server to start..."
    $maxRetries = 10
    $retryCount = 0
    $serverStarted = $false

    while (-not $serverStarted -and $retryCount -lt $maxRetries) {
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:1231/health" -Method GET -TimeoutSec 2
            if ($response.StatusCode -eq 200) {
                Write-Host "API server started successfully"
                $serverStarted = $true
            }
        } catch {
            Write-Host "Waiting for API server to start... ($($retryCount + 1)/$maxRetries)"
            Start-Sleep -Seconds 1
            $retryCount++
        }
    }

    if (-not $serverStarted) {
        Write-Host "Failed to start API server within the timeout period"
        if ($null -ne $apiServerProcess) {
            Stop-Process -Id $apiServerProcess.Id -Force
        }
        exit 1
    }
}

# Run the integration tests
Write-Host "Running integration tests..."
$env:RUN_INTEGRATION_TESTS = "true"
go test -v ./cmd/apiserver -run TestAPIIntegration

# Stop the API server if we started it
if (-not $apiServerRunning -and $null -ne $apiServerProcess) {
    Write-Host "Stopping API server..."
    Stop-Process -Id $apiServerProcess.Id -Force
}

Write-Host "Tests completed"
