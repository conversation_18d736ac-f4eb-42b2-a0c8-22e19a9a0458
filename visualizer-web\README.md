# MyHeritage DNA Database Visualizer

A comprehensive React-based web application for visualizing and analyzing MyHeritage DNA database contents.

## 🎯 Features

- **Interactive Dashboard** - Overview of database statistics and key metrics
- **3D Network Visualization** - Interactive 3D force graph of DNA match relationships
- **DNA Match Analysis** - Comprehensive analysis of match statistics and distributions
- **Geographic Visualization** - Global distribution of matches and individuals
- **Individual Analysis** - Demographics and surname analysis
- **DNA Segment Analysis** - Chromosome browser and segment visualization
- **Advanced Analytics** - Deep dive analytics and data insights

## 🛠️ Technology Stack

- **React 18** with TypeScript
- **Material-UI** for components and theming
- **React Query** for data fetching and caching
- **Recharts** for standard charts and graphs
- **3D Force Graph** for network visualization
- **D3.js** for custom visualizations
- **Leaflet** for geographic maps

## 🚀 Getting Started

### Prerequisites

- Node.js 18 or higher
- npm or yarn
- MyHeritage API server running on port 1231

### Development Setup

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Start development server:**
   ```bash
   npm start
   ```

3. **Open browser:**
   Navigate to `http://localhost:3000`

### Production Build

1. **Build the application:**
   ```bash
   npm run build
   ```

2. **Serve with nginx or any static server**

### Docker Setup

1. **Build Docker image:**
   ```bash
   docker build -t myheritage-visualizer .
   ```

2. **Run container:**
   ```bash
   docker run -p 3000:80 myheritage-visualizer
   ```

## 📋 API Integration

The visualizer connects to the MyHeritage API server running on port 1231. It uses the following endpoints:

- `GET /api/v1/analytics/overview` - Database overview statistics
- `GET /api/v1/analytics/matches/stats` - DNA match statistics
- `GET /api/v1/analytics/geography/top-countries` - Geographic distribution
- `GET /api/v1/analytics/individuals/stats` - Individual statistics
- `GET /api/v1/analytics/network/graph` - Network graph data
- `GET /api/v1/analytics/network/cliques` - Network clique analysis

## 🎨 Components Structure

```
src/
├── components/
│   ├── Dashboard/          # Dashboard components
│   ├── Layout/             # Navigation and layout
│   └── NetworkGraph/       # 3D visualization components
├── pages/
│   ├── Dashboard/          # Main dashboard page
│   ├── DNAMatches/         # DNA match analysis
│   ├── Individuals/        # Individual analysis
│   ├── NetworkGraph/       # Network visualization
│   ├── Geography/          # Geographic visualization
│   ├── Segments/           # DNA segment analysis
│   └── Analytics/          # Advanced analytics
├── services/
│   └── api.ts              # API integration
└── App.tsx                 # Main application
```

## 🔧 Configuration

### Environment Variables

- `REACT_APP_API_URL` - API server URL (default: http://localhost:1231)

### Proxy Configuration

In development, the app uses a proxy to the API server defined in `package.json`:

```json
"proxy": "http://localhost:1231"
```

## 📊 Visualizations

### Dashboard
- Database statistics overview
- Quick action cards
- Summary charts

### Network Graph
- Interactive 3D force graph
- Configurable depth and filtering
- Node and edge interactions

### DNA Matches
- Match distribution charts
- Relationship analysis
- cM range visualization

### Geography
- Country distribution bar chart
- Interactive world map (planned)
- Migration patterns (planned)

## 🧪 Testing

```bash
# Run tests
npm test

# Run tests with coverage
npm test -- --coverage
```

## 🚀 Deployment

### Docker Compose

Use the provided `docker-compose-visualizer.yml` to run the complete stack:

```bash
docker-compose -f docker-compose-visualizer.yml up -d
```

This will start:
- API server on port 1231
- Visualizer web app on port 3000
- Redis cache on port 6379

## 📝 Development Notes

- The app uses React Query for efficient data fetching and caching
- All API calls are centralized in `src/services/api.ts`
- Material-UI provides consistent theming and components
- The 3D network graph uses the `3d-force-graph` library
- Responsive design works on desktop and mobile devices

## 🔮 Future Enhancements

- Interactive world map with heat maps
- Real-time data updates via WebSocket
- Advanced filtering and search capabilities
- Data export functionality
- Custom dashboard creation
- Performance optimizations for large datasets
- Mobile app version

## 🐛 Troubleshooting

### Common Issues

1. **API Connection Failed**
   - Ensure the API server is running on port 1231
   - Check network connectivity
   - Verify CORS settings

2. **3D Graph Not Loading**
   - Check browser WebGL support
   - Ensure sufficient memory for large datasets
   - Try reducing the data limit

3. **Slow Performance**
   - Reduce the network graph limit
   - Enable data caching
   - Check browser performance tools

## 📄 License

This project is part of the MyHeritage DNA analysis toolkit.
