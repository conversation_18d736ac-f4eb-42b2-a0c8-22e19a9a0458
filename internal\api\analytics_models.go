package api

import "time"

// OverviewResponse represents the database overview statistics
type OverviewResponse struct {
	TotalDNAMatches      int64     `json:"total_dna_matches"`
	TotalIndividuals     int64     `json:"total_individuals"`
	TotalTrees           int64     `json:"total_trees"`
	TotalSharedSegments  int64     `json:"total_shared_segments"`
	TotalSubmitters      int64     `json:"total_submitters"`
	TotalFamilies        int64     `json:"total_families"`
	TotalSurnames        int64     `json:"total_surnames"`
	LastUpdated          time.Time `json:"last_updated"`
}

// MatchStatsResponse represents DNA match statistics
type MatchStatsResponse struct {
	TotalMatches         int64                    `json:"total_matches"`
	AvgSharedCM          float64                  `json:"avg_shared_cm"`
	MaxSharedCM          float64                  `json:"max_shared_cm"`
	MinSharedCM          float64                  `json:"min_shared_cm"`
	MatchesByConfidence  map[string]int64         `json:"matches_by_confidence"`
	MatchesByRelationship map[string]int64        `json:"matches_by_relationship"`
	CMDistribution       []CMDistributionBucket   `json:"cm_distribution"`
}

// CMDistributionBucket represents a bucket in the cM distribution
type CMDistributionBucket struct {
	MinCM float64 `json:"min_cm"`
	MaxCM float64 `json:"max_cm"`
	Count int64   `json:"count"`
}

// TopCountriesResponse represents the top countries by DNA matches
type TopCountriesResponse struct {
	Countries []CountryStats `json:"countries"`
	Total     int64          `json:"total"`
}

// CountryStats represents statistics for a specific country
type CountryStats struct {
	Country string `json:"country"`
	Count   int64  `json:"count"`
}

// IndividualStatsResponse represents individual demographics
type IndividualStatsResponse struct {
	TotalIndividuals    int64                `json:"total_individuals"`
	GenderDistribution  map[string]int64     `json:"gender_distribution"`
	AgeGroupDistribution map[string]int64    `json:"age_group_distribution"`
	TopSurnames         []SurnameStats       `json:"top_surnames"`
	IndividualsWithTrees int64               `json:"individuals_with_trees"`
}

// SurnameStats represents statistics for a surname
type SurnameStats struct {
	Surname string `json:"surname"`
	Count   int64  `json:"count"`
}
