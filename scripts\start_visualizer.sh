#!/bin/bash

# MyHeritage DNA Visualizer Startup Script
# This script starts the complete visualizer stack

set -e

echo "🚀 Starting MyHeritage DNA Visualizer Stack..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if Docker Compose is available
if ! command -v docker-compose &> /dev/null; then
    print_error "Docker Compose is not installed. Please install Docker Compose and try again."
    exit 1
fi

# Check if .env file exists
if [ ! -f .env ]; then
    print_warning ".env file not found. Creating a sample .env file..."
    cat > .env << EOF
# Database Configuration
DB_HOST=your-mariadb-host
DB_PORT=3306
DB_USER=your-username
DB_PASSWORD=your-password
DB_NAME=myheritage

# API Configuration
PORT=1231
API_TOKEN=your-api-token

# Visualizer Configuration
REACT_APP_API_URL=http://localhost:1231
EOF
    print_warning "Please update the .env file with your database credentials before continuing."
    read -p "Press Enter to continue after updating .env file..."
fi

# Function to check if a port is in use
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# Check if required ports are available
print_status "Checking port availability..."

if check_port 1231; then
    print_warning "Port 1231 is already in use. The API server might already be running."
fi

if check_port 3000; then
    print_warning "Port 3000 is already in use. The visualizer might already be running."
fi

# Build and start the services
print_status "Building and starting services..."

# Stop any existing containers
print_status "Stopping existing containers..."
docker-compose -f docker-compose-visualizer.yml down 2>/dev/null || true

# Build the images
print_status "Building Docker images..."
docker-compose -f docker-compose-visualizer.yml build

# Start the services
print_status "Starting services..."
docker-compose -f docker-compose-visualizer.yml up -d

# Wait for services to be ready
print_status "Waiting for services to be ready..."

# Function to wait for a service to be healthy
wait_for_service() {
    local service_name=$1
    local url=$2
    local max_attempts=30
    local attempt=1

    print_status "Waiting for $service_name to be ready..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f -s "$url" > /dev/null 2>&1; then
            print_success "$service_name is ready!"
            return 0
        fi
        
        echo -n "."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    print_error "$service_name failed to start within expected time"
    return 1
}

# Wait for API server
wait_for_service "API Server" "http://localhost:1231/health"

# Wait for visualizer
wait_for_service "Visualizer Web App" "http://localhost:3000"

# Show status
print_status "Checking service status..."
docker-compose -f docker-compose-visualizer.yml ps

echo ""
print_success "🎉 MyHeritage DNA Visualizer is now running!"
echo ""
echo "📊 Access the applications:"
echo "   • Visualizer Web App: http://localhost:3000"
echo "   • API Server: http://localhost:1231"
echo "   • API Documentation: http://localhost:1231/swagger"
echo "   • Health Check: http://localhost:1231/health"
echo ""
echo "🔧 Useful commands:"
echo "   • View logs: docker-compose -f docker-compose-visualizer.yml logs -f"
echo "   • Stop services: docker-compose -f docker-compose-visualizer.yml down"
echo "   • Restart services: docker-compose -f docker-compose-visualizer.yml restart"
echo ""
echo "📝 Note: Make sure your database is accessible and the .env file is properly configured."

# Optional: Open browser
if command -v xdg-open &> /dev/null; then
    read -p "Would you like to open the visualizer in your browser? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        xdg-open http://localhost:3000
    fi
elif command -v open &> /dev/null; then
    read -p "Would you like to open the visualizer in your browser? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        open http://localhost:3000
    fi
fi

print_success "Startup complete! 🚀"
