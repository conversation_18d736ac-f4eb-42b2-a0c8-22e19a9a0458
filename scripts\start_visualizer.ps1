# MyHeritage DNA Visualizer Startup Script (PowerShell)
# This script starts the complete visualizer stack on Windows

param(
    [switch]$OpenBrowser = $false
)

Write-Host "🚀 Starting MyHeritage DNA Visualizer Stack..." -ForegroundColor Blue

# Function to print colored output
function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Cyan
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# Check if Docker is running
try {
    docker info | Out-Null
    Write-Status "Docker is running"
} catch {
    Write-Error "Docker is not running. Please start Docker Desktop and try again."
    exit 1
}

# Check if Docker Compose is available
try {
    docker-compose --version | Out-Null
    Write-Status "Docker Compose is available"
} catch {
    Write-Error "Docker Compose is not available. Please install Docker Compose and try again."
    exit 1
}

# Check if .env file exists
if (-not (Test-Path ".env")) {
    Write-Warning ".env file not found. Creating a sample .env file..."
    
    $envContent = @"
# Database Configuration
DB_HOST=your-mariadb-host
DB_PORT=3306
DB_USER=your-username
DB_PASSWORD=your-password
DB_NAME=myheritage

# API Configuration
PORT=1231
API_TOKEN=your-api-token

# Visualizer Configuration
REACT_APP_API_URL=http://localhost:1231
"@
    
    $envContent | Out-File -FilePath ".env" -Encoding UTF8
    Write-Warning "Please update the .env file with your database credentials before continuing."
    Read-Host "Press Enter to continue after updating .env file"
}

# Function to check if a port is in use
function Test-Port {
    param([int]$Port)
    try {
        $connection = New-Object System.Net.Sockets.TcpClient
        $connection.Connect("localhost", $Port)
        $connection.Close()
        return $true
    } catch {
        return $false
    }
}

# Check if required ports are available
Write-Status "Checking port availability..."

if (Test-Port 1231) {
    Write-Warning "Port 1231 is already in use. The API server might already be running."
}

if (Test-Port 3000) {
    Write-Warning "Port 3000 is already in use. The visualizer might already be running."
}

# Build and start the services
Write-Status "Building and starting services..."

# Stop any existing containers
Write-Status "Stopping existing containers..."
try {
    docker-compose -f docker-compose-visualizer.yml down 2>$null
} catch {
    # Ignore errors if containers don't exist
}

# Build the images
Write-Status "Building Docker images..."
docker-compose -f docker-compose-visualizer.yml build

if ($LASTEXITCODE -ne 0) {
    Write-Error "Failed to build Docker images"
    exit 1
}

# Start the services
Write-Status "Starting services..."
docker-compose -f docker-compose-visualizer.yml up -d

if ($LASTEXITCODE -ne 0) {
    Write-Error "Failed to start services"
    exit 1
}

# Wait for services to be ready
Write-Status "Waiting for services to be ready..."

# Function to wait for a service to be healthy
function Wait-ForService {
    param(
        [string]$ServiceName,
        [string]$Url,
        [int]$MaxAttempts = 30
    )
    
    Write-Status "Waiting for $ServiceName to be ready..."
    
    for ($attempt = 1; $attempt -le $MaxAttempts; $attempt++) {
        try {
            $response = Invoke-WebRequest -Uri $Url -UseBasicParsing -TimeoutSec 5
            if ($response.StatusCode -eq 200) {
                Write-Success "$ServiceName is ready!"
                return $true
            }
        } catch {
            # Service not ready yet
        }
        
        Write-Host "." -NoNewline
        Start-Sleep -Seconds 2
    }
    
    Write-Error "$ServiceName failed to start within expected time"
    return $false
}

# Wait for API server
if (-not (Wait-ForService "API Server" "http://localhost:1231/health")) {
    Write-Error "API Server failed to start"
    exit 1
}

# Wait for visualizer
if (-not (Wait-ForService "Visualizer Web App" "http://localhost:3000")) {
    Write-Error "Visualizer Web App failed to start"
    exit 1
}

# Show status
Write-Status "Checking service status..."
docker-compose -f docker-compose-visualizer.yml ps

Write-Host ""
Write-Success "🎉 MyHeritage DNA Visualizer is now running!"
Write-Host ""
Write-Host "📊 Access the applications:" -ForegroundColor Cyan
Write-Host "   • Visualizer Web App: http://localhost:3000" -ForegroundColor White
Write-Host "   • API Server: http://localhost:1231" -ForegroundColor White
Write-Host "   • API Documentation: http://localhost:1231/swagger" -ForegroundColor White
Write-Host "   • Health Check: http://localhost:1231/health" -ForegroundColor White
Write-Host ""
Write-Host "🔧 Useful commands:" -ForegroundColor Cyan
Write-Host "   • View logs: docker-compose -f docker-compose-visualizer.yml logs -f" -ForegroundColor White
Write-Host "   • Stop services: docker-compose -f docker-compose-visualizer.yml down" -ForegroundColor White
Write-Host "   • Restart services: docker-compose -f docker-compose-visualizer.yml restart" -ForegroundColor White
Write-Host ""
Write-Host "📝 Note: Make sure your database is accessible and the .env file is properly configured." -ForegroundColor Yellow

# Optional: Open browser
if ($OpenBrowser) {
    Write-Status "Opening visualizer in browser..."
    Start-Process "http://localhost:3000"
} else {
    $openBrowser = Read-Host "Would you like to open the visualizer in your browser? (y/n)"
    if ($openBrowser -eq "y" -or $openBrowser -eq "Y") {
        Start-Process "http://localhost:3000"
    }
}

Write-Success "Startup complete! 🚀"
