# Using MariaDB with MyHeritage

This document provides instructions on how to use MariaDB with the MyHeritage application.

## Prerequisites

- <PERSON><PERSON> and <PERSON>er Compose installed
- PowerShell (for Windows) or Bash (for Linux/Mac)
- MariaDB server running on another host (*************)

## Configuration

The application is configured to use MariaDB by default. The connection details are:

- Host: *************
- User: myheritage
- Password: Xq(RDl*5C7lkocf1
- Database: myheritage

## Running the Application

### Using Docker Compose

To run the application with Docker Compose:

```powershell
# Windows
.\run_docker_mariadb.ps1

# Linux/Mac
bash run_docker_mariadb.sh
```

This will start the API server and the downloader, both configured to use MariaDB.

### Running Locally

To run the application locally:

```powershell
# Windows
.\run_with_mariadb.ps1

# Linux/Mac
source .env.mariadb
go run cmd/apiserver/main.go cmd/apiserver/app.go
```

## Migration

The migration from PostgreSQL to MariaDB is handled by the following scripts:

- `migrate_large_tables.ps1`: Migrates a single table from PostgreSQL to MariaDB
- `migrate_all.ps1`: Migrates all tables from PostgreSQL to MariaDB

To migrate all tables:

```powershell
# Windows
.\migrate_all.ps1

# Linux/Mac
bash migrate_all.sh
```

## Troubleshooting

### Connection Issues

If you encounter connection issues, check the following:

1. Ensure that the MariaDB server is running and accessible
2. Verify that the connection details are correct
3. Check that the database and tables exist

### Data Migration Issues

If you encounter issues with data migration:

1. Check the logs for error messages
2. Verify that the PostgreSQL server is running and accessible
3. Ensure that the MariaDB server has enough disk space

## Switching Back to PostgreSQL

To switch back to PostgreSQL, set the `DB_TYPE` environment variable to `postgres`:

```powershell
# Windows
$env:DB_TYPE = "postgres"
go run cmd/apiserver/main.go cmd/apiserver/app.go

# Linux/Mac
export DB_TYPE=postgres
go run cmd/apiserver/main.go cmd/apiserver/app.go
```

Or use the Docker Compose file:

```powershell
# Windows
docker-compose up -d

# Linux/Mac
docker-compose up -d
```
