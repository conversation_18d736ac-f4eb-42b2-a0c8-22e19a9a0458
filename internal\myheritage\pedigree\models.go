package pedigree

type ApiResponse struct {
	Data struct {
		DNAMatch struct {
			OtherDNAKit DNAKit `json:"other_dna_kit"`
		} `json:"dna_match"`
	} `json:"data"`
}

type DNAKit struct {
	Member struct {
		Id            string      `json:"id"`
		Name          string      `json:"name"`
		FirstName     string      `json:"first_name"`
		LastName      string      `json:"last_name"`
		Gender        string      `json:"gender"`
		AgeGroup      string      `json:"age_group"`
		PersonalPhoto interface{} `json:"personal_photo"`
	} `json:"member"`
	AssociatedIndividual Individual `json:"associated_individual"`
}

type Individual struct {
	Id                string         `json:"id"`
	Name              string         `json:"name"`
	FirstName         string         `json:"first_name"`
	LastName          string         `json:"last_name"`
	FormattedLastName string         `json:"formatted_last_name"`
	Gender            string         `json:"gender"`
	IsAlive           bool           `json:"is_alive"`
	IsPrivatized      bool           `json:"is_privatized"`
	AgeGroup          string         `json:"age_group"`
	PersonalPhoto     interface{}    `json:"personal_photo"`
	BirthDate         BirthDate      `json:"birth_date"`
	DeathDate         BirthDate      `json:"death_date"`
	ChildInFamilies   []FamilyRef    `json:"child_in_families"`
	SpouseInFamilies  []SpouseFamily `json:"spouse_in_families"`
	CloseFamily       CloseFamily    `json:"close_family"`
}

type BirthDate struct {
	StructuredDate struct {
		FirstDate struct {
			Year int `json:"year"`
		} `json:"first_date"`
	} `json:"structured_date"`
}

type FamilyRef struct {
	ChildType interface{} `json:"child_type"`
	Family    Family      `json:"family"`
}

type Family struct {
	Id string `json:"id"`
}

type CloseFamily struct {
	Data []FamilyIndividual `json:"data"`
}

type FamilyIndividual struct {
	Individual `json:"individual"`
}

type SpouseFamily struct {
	Id      string  `json:"id"`
	Husband Husband `json:"husband"`
	Wife    Wife    `json:"wife"`
}

type Husband struct {
	Id string `json:"id"`
}

type Wife struct {
	Id string `json:"id"`
}
