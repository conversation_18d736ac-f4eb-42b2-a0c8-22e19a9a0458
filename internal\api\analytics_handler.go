package api

import (
	"net/http"
	"time"

	"github.com/dmagur/myheritage/internal/database"
	"github.com/gin-gonic/gin"
	log "github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// AnalyticsHandler handles API requests for analytics
type AnalyticsHandler struct {
	db     *gorm.DB
	logger *log.Logger
}

// NewAnalyticsHandler creates a new AnalyticsHandler
func NewAnalyticsHandler(db *gorm.DB, logger *log.Logger) *AnalyticsHandler {
	return &AnalyticsHandler{
		db:     db,
		logger: logger,
	}
}

// RegisterRoutes registers the analytics routes
func (h *AnalyticsHandler) RegisterRoutes(router *gin.Engine) {
	// Group API routes under /api/v1/analytics
	analytics := router.Group("/api/v1/analytics")
	{
		analytics.GET("/overview", h.GetOverview)
		analytics.GET("/matches/stats", h.GetMatchStats)
		analytics.GET("/geography/top-countries", h.GetTopCountries)
		analytics.GET("/individuals/stats", h.GetIndividualStats)
	}
}

// GetOverview returns database overview statistics
func (h *AnalyticsHandler) GetOverview(c *gin.Context) {
	h.logger.Info("Getting database overview statistics")

	var overview OverviewResponse
	overview.LastUpdated = time.Now()

	// Use a single optimized query to get all counts at once
	// This is much faster than multiple separate COUNT queries
	var result struct {
		DNAMatches     int64 `json:"dna_matches"`
		Individuals    int64 `json:"individuals"`
		Trees          int64 `json:"trees"`
		SharedSegments int64 `json:"shared_segments"`
		Submitters     int64 `json:"submitters"`
		Families       int64 `json:"families"`
		Surnames       int64 `json:"surnames"`
	}

	// Execute optimized query with timeout
	ctx := c.Request.Context()
	query := `
		SELECT
			(SELECT COUNT(*) FROM dna_matches) as dna_matches,
			(SELECT COUNT(*) FROM individuals) as individuals,
			(SELECT COUNT(*) FROM trees) as trees,
			(SELECT COUNT(*) FROM shared_segment) as shared_segments,
			(SELECT COUNT(*) FROM submitters) as submitters,
			(SELECT COUNT(*) FROM family) as families,
			(SELECT COUNT(*) FROM surname) as surnames
	`

	if err := h.db.WithContext(ctx).Raw(query).Scan(&result).Error; err != nil {
		h.logger.WithError(err).Error("Failed to get overview statistics")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get overview statistics"})
		return
	}

	overview.TotalDNAMatches = result.DNAMatches
	overview.TotalIndividuals = result.Individuals
	overview.TotalTrees = result.Trees
	overview.TotalSharedSegments = result.SharedSegments
	overview.TotalSubmitters = result.Submitters
	overview.TotalFamilies = result.Families
	overview.TotalSurnames = result.Surnames

	h.logger.WithFields(log.Fields{
		"dna_matches":     overview.TotalDNAMatches,
		"individuals":     overview.TotalIndividuals,
		"trees":           overview.TotalTrees,
		"shared_segments": overview.TotalSharedSegments,
		"submitters":      overview.TotalSubmitters,
		"families":        overview.TotalFamilies,
		"surnames":        overview.TotalSurnames,
	}).Info("Database overview statistics retrieved")

	c.JSON(http.StatusOK, overview)
}

// GetMatchStats returns DNA match statistics
func (h *AnalyticsHandler) GetMatchStats(c *gin.Context) {
	h.logger.Info("Getting DNA match statistics")

	var stats MatchStatsResponse
	ctx := c.Request.Context()

	// Get basic statistics in a single optimized query
	var basicStats struct {
		TotalMatches int64   `json:"total_matches"`
		AvgCM        float64 `json:"avg_cm"`
		MaxCM        float64 `json:"max_cm"`
		MinCM        float64 `json:"min_cm"`
	}

	basicQuery := `
		SELECT
			COUNT(*) as total_matches,
			AVG(CASE WHEN total_shared_segments_length_in_cm > 0 THEN total_shared_segments_length_in_cm END) as avg_cm,
			MAX(total_shared_segments_length_in_cm) as max_cm,
			MIN(CASE WHEN total_shared_segments_length_in_cm > 0 THEN total_shared_segments_length_in_cm END) as min_cm
		FROM dna_matches
	`

	if err := h.db.WithContext(ctx).Raw(basicQuery).Scan(&basicStats).Error; err != nil {
		h.logger.WithError(err).Error("Failed to get basic match statistics")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get basic match statistics"})
		return
	}

	stats.TotalMatches = basicStats.TotalMatches
	stats.AvgSharedCM = basicStats.AvgCM
	stats.MaxSharedCM = basicStats.MaxCM
	stats.MinSharedCM = basicStats.MinCM

	// Get confidence level distribution with timeout
	var confidenceResults []struct {
		ConfidenceLevel string
		Count           int64
	}

	if err := h.db.WithContext(ctx).Model(&database.DNAMatch{}).
		Select("COALESCE(confidence_level, 'unknown') as confidence_level, COUNT(*) as count").
		Group("confidence_level").
		Limit(20).
		Scan(&confidenceResults).Error; err != nil {
		h.logger.WithError(err).Error("Failed to get confidence distribution")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get confidence distribution"})
		return
	}

	stats.MatchesByConfidence = make(map[string]int64)
	for _, result := range confidenceResults {
		stats.MatchesByConfidence[result.ConfidenceLevel] = result.Count
	}

	// Get relationship distribution with timeout and limit
	var relationshipResults []struct {
		ExactDnaRelationship string
		Count                int64
	}

	if err := h.db.WithContext(ctx).Model(&database.DNAMatch{}).
		Select("COALESCE(exact_dna_relationship, 'unknown') as exact_dna_relationship, COUNT(*) as count").
		Group("exact_dna_relationship").
		Order("count DESC").
		Limit(15).
		Scan(&relationshipResults).Error; err != nil {
		h.logger.WithError(err).Error("Failed to get relationship distribution")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get relationship distribution"})
		return
	}

	stats.MatchesByRelationship = make(map[string]int64)
	for _, result := range relationshipResults {
		stats.MatchesByRelationship[result.ExactDnaRelationship] = result.Count
	}

	// Get cM distribution using a single optimized query
	distributionQuery := `
		SELECT
			CASE
				WHEN total_shared_segments_length_in_cm >= 0 AND total_shared_segments_length_in_cm < 10 THEN '0-10'
				WHEN total_shared_segments_length_in_cm >= 10 AND total_shared_segments_length_in_cm < 20 THEN '10-20'
				WHEN total_shared_segments_length_in_cm >= 20 AND total_shared_segments_length_in_cm < 50 THEN '20-50'
				WHEN total_shared_segments_length_in_cm >= 50 AND total_shared_segments_length_in_cm < 100 THEN '50-100'
				WHEN total_shared_segments_length_in_cm >= 100 AND total_shared_segments_length_in_cm < 200 THEN '100-200'
				WHEN total_shared_segments_length_in_cm >= 200 AND total_shared_segments_length_in_cm < 500 THEN '200-500'
				WHEN total_shared_segments_length_in_cm >= 500 AND total_shared_segments_length_in_cm < 1000 THEN '500-1000'
				WHEN total_shared_segments_length_in_cm >= 1000 THEN '1000+'
				ELSE 'unknown'
			END as bucket,
			COUNT(*) as count
		FROM dna_matches
		WHERE total_shared_segments_length_in_cm >= 0
		GROUP BY bucket
		ORDER BY
			CASE bucket
				WHEN '0-10' THEN 1
				WHEN '10-20' THEN 2
				WHEN '20-50' THEN 3
				WHEN '50-100' THEN 4
				WHEN '100-200' THEN 5
				WHEN '200-500' THEN 6
				WHEN '500-1000' THEN 7
				WHEN '1000+' THEN 8
				ELSE 9
			END
	`

	var distributionResults []struct {
		Bucket string
		Count  int64
	}

	if err := h.db.WithContext(ctx).Raw(distributionQuery).Scan(&distributionResults).Error; err != nil {
		h.logger.WithError(err).Error("Failed to get cM distribution")
		// Don't fail the entire request, just log the error
		stats.CMDistribution = []CMDistributionBucket{}
	} else {
		// Map bucket names to actual ranges
		bucketMap := map[string]CMDistributionBucket{
			"0-10":     {MinCM: 0, MaxCM: 10},
			"10-20":    {MinCM: 10, MaxCM: 20},
			"20-50":    {MinCM: 20, MaxCM: 50},
			"50-100":   {MinCM: 50, MaxCM: 100},
			"100-200":  {MinCM: 100, MaxCM: 200},
			"200-500":  {MinCM: 200, MaxCM: 500},
			"500-1000": {MinCM: 500, MaxCM: 1000},
			"1000+":    {MinCM: 1000, MaxCM: 10000},
		}

		stats.CMDistribution = make([]CMDistributionBucket, 0, len(distributionResults))
		for _, result := range distributionResults {
			if bucket, exists := bucketMap[result.Bucket]; exists {
				bucket.Count = result.Count
				stats.CMDistribution = append(stats.CMDistribution, bucket)
			}
		}
	}

	h.logger.WithFields(log.Fields{
		"total_matches": stats.TotalMatches,
		"avg_cm":        stats.AvgSharedCM,
		"max_cm":        stats.MaxSharedCM,
		"min_cm":        stats.MinSharedCM,
	}).Info("DNA match statistics retrieved")

	c.JSON(http.StatusOK, stats)
}

// GetTopCountries returns the top countries by DNA matches
func (h *AnalyticsHandler) GetTopCountries(c *gin.Context) {
	h.logger.Info("Getting top countries statistics")

	var results []struct {
		Country string
		Count   int64
	}

	// Get top countries from trees table (site_creator_country)
	if err := h.db.Table("trees").
		Select("site_creator_country as country, COUNT(*) as count").
		Where("site_creator_country IS NOT NULL AND site_creator_country != ''").
		Group("site_creator_country").
		Order("count DESC").
		Limit(20).
		Scan(&results).Error; err != nil {
		h.logger.WithError(err).Error("Failed to get top countries")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get top countries"})
		return
	}

	var response TopCountriesResponse
	response.Countries = make([]CountryStats, len(results))
	var total int64

	for i, result := range results {
		response.Countries[i] = CountryStats{
			Country: result.Country,
			Count:   result.Count,
		}
		total += result.Count
	}
	response.Total = total

	h.logger.WithField("countries_count", len(response.Countries)).Info("Top countries statistics retrieved")

	c.JSON(http.StatusOK, response)
}

// GetIndividualStats returns individual demographics statistics
func (h *AnalyticsHandler) GetIndividualStats(c *gin.Context) {
	h.logger.Info("Getting individual statistics")

	var stats IndividualStatsResponse

	// Get total count
	if err := h.db.Model(&database.Individual{}).Count(&stats.TotalIndividuals).Error; err != nil {
		h.logger.WithError(err).Error("Failed to count individuals")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get individual count"})
		return
	}

	// Get gender distribution
	var genderResults []struct {
		Gender string
		Count  int64
	}

	if err := h.db.Model(&database.Individual{}).
		Select("gender, COUNT(*) as count").
		Group("gender").
		Scan(&genderResults).Error; err != nil {
		h.logger.WithError(err).Error("Failed to get gender distribution")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get gender distribution"})
		return
	}

	stats.GenderDistribution = make(map[string]int64)
	for _, result := range genderResults {
		if result.Gender == "" {
			stats.GenderDistribution["unknown"] = result.Count
		} else {
			stats.GenderDistribution[result.Gender] = result.Count
		}
	}

	// Get age group distribution
	var ageResults []struct {
		AgeGroup string
		Count    int64
	}

	if err := h.db.Model(&database.Individual{}).
		Select("age_group, COUNT(*) as count").
		Group("age_group").
		Scan(&ageResults).Error; err != nil {
		h.logger.WithError(err).Error("Failed to get age group distribution")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get age group distribution"})
		return
	}

	stats.AgeGroupDistribution = make(map[string]int64)
	for _, result := range ageResults {
		if result.AgeGroup == "" {
			stats.AgeGroupDistribution["unknown"] = result.Count
		} else {
			stats.AgeGroupDistribution[result.AgeGroup] = result.Count
		}
	}

	// Count individuals with trees
	if err := h.db.Model(&database.Individual{}).
		Where("tree_id IS NOT NULL AND tree_id != ''").
		Count(&stats.IndividualsWithTrees).Error; err != nil {
		h.logger.WithError(err).Error("Failed to count individuals with trees")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to count individuals with trees"})
		return
	}

	h.logger.WithFields(log.Fields{
		"total_individuals":      stats.TotalIndividuals,
		"individuals_with_trees": stats.IndividualsWithTrees,
	}).Info("Individual statistics retrieved")

	c.JSON(http.StatusOK, stats)
}
