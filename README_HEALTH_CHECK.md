# MyHeritage Application Health Check

This document describes the comprehensive health check script for all MyHeritage applications.

## Overview

The `health_check.sh` script provides automated testing to ensure all applications in the MyHeritage project can start and run without errors. It's designed for general application health monitoring rather than specific migration testing.

## What It Tests

### 🔍 **Dependency Verification**
- Go module integrity (`go mod verify`)
- Module tidiness check
- Dependency availability

### 🔨 **Compilation Tests**
- All main applications compile successfully
- No build errors or warnings
- Clean compilation process

### 🧪 **Unit Tests**
- All package unit tests pass
- Short test suite execution
- Test coverage validation

### 🚀 **Application Startup Tests**
- Applications start without crashing
- Help/usage information available
- No immediate runtime errors
- Graceful startup process

### 🐳 **Docker Build Tests** (Optional)
- Docker images build successfully
- No container build errors
- Dockerfile validation

## Applications Tested

The script automatically tests all applications in the project:

- **apiserver** - API server application
- **clique** - Clique analysis tool
- **myheritagedownloader** - Main downloader application
- **graphjson-v1** - Graph JSON generator v1
- **graphjson-v2** - Graph JSON generator v2
- **graphjson-v3** - Graph JSON generator v3
- **graphjson-v4** - Graph JSON generator v4

## Usage

### Quick Health Check (Recommended)
```bash
./health_check.sh --quick
```
Runs compilation and unit tests only. Fast execution (~30 seconds).

### Full Health Check
```bash
./health_check.sh
```
Runs all tests including startup and Docker builds (~2-5 minutes).

### Verbose Output
```bash
./health_check.sh --verbose
```
Shows detailed output including build logs and error messages.

### Custom Timeout
```bash
./health_check.sh --timeout 15
```
Sets custom timeout for application startup tests (default: 10 seconds).

### Skip Docker Tests
```bash
./health_check.sh --no-docker
```
Runs all tests except Docker builds.

### List Applications
```bash
./health_check.sh --list-apps
```
Shows all applications and Docker configurations that will be tested.

### Help
```bash
./health_check.sh --help
```
Shows all available options and usage examples.

## Test Results

### ✅ **Success Indicators**
- All applications compile without errors
- Unit tests pass
- Applications start successfully
- Docker builds complete (if enabled)
- Clean dependency verification

### ❌ **Failure Indicators**
- Compilation errors
- Test failures
- Application crashes on startup
- Docker build failures
- Missing or corrupted dependencies

### ⚠️ **Warning Indicators**
- Module needs tidying
- Unclear help output
- Non-critical issues

## Example Output

```bash
🏥 === MyHeritage Application Health Check ===

ℹ️  Testing Go module dependencies...
✅ Go module dependencies verified
✅ Go module is tidy

ℹ️  Testing application compilation...
✅ apiserver compiles successfully
✅ clique compiles successfully
✅ myheritagedownloader compiles successfully
✅ graphjson-v1 compiles successfully
✅ graphjson-v2 compiles successfully
✅ graphjson-v3 compiles successfully
✅ graphjson-v4 compiles successfully

ℹ️  Running unit tests...
✅ Unit tests pass

ℹ️  Testing application startup (timeout: 10s)...
✅ apiserver starts and shows help
✅ clique starts and shows help
✅ myheritagedownloader starts and shows help
✅ graphjson-v1 starts without immediate crash
✅ graphjson-v2 starts without immediate crash
✅ graphjson-v3 starts without immediate crash
✅ graphjson-v4 starts without immediate crash

ℹ️  Testing Docker builds...
✅ Docker image myheritage-downloader built successfully
✅ Docker image myheritage-apiserver built successfully

📊 === Health Check Summary ===
✅ All health checks passed! 🎉
```

## Integration with CI/CD

The health check script is designed for CI/CD integration:

### GitHub Actions Example
```yaml
- name: Run Application Health Check
  run: |
    chmod +x health_check.sh
    ./health_check.sh --quick
```

### For environments without Docker:
```yaml
- name: Run Health Check (No Docker)
  run: ./health_check.sh --no-docker
```

### With custom timeout:
```yaml
- name: Run Health Check (Extended)
  run: ./health_check.sh --timeout 30
```

## Troubleshooting

### Common Issues

**Compilation Failures**
- Check Go version compatibility
- Run `go mod tidy` to fix dependencies
- Verify all imports are correct

**Startup Test Failures**
- Check application command-line interface
- Verify required configuration files exist
- Review application logs with `--verbose`

**Docker Build Failures**
- Ensure Docker is running
- Check Dockerfile syntax
- Verify build context

**Unit Test Failures**
- Run tests individually: `go test ./internal/package -v`
- Check for missing test dependencies
- Verify test data setup

### Getting Detailed Information

1. **Run with verbose output:**
   ```bash
   ./health_check.sh --verbose
   ```

2. **Test individual components:**
   ```bash
   go build ./cmd/apiserver
   go test ./internal/database -v
   ```

3. **Check specific application:**
   ```bash
   go run ./cmd/apiserver --help
   ```

## Maintenance

The health check script automatically discovers applications in the `cmd/` directory and Docker configurations. Update the script when:

- New applications are added to `cmd/`
- New Dockerfiles are created
- Test requirements change
- New health check categories are needed

## Performance

- **Quick mode**: ~30 seconds
- **Full mode**: ~2-5 minutes (depending on Docker builds)
- **Verbose mode**: Adds ~20% overhead for detailed logging

The script is optimized for CI/CD environments and provides fast feedback on application health.
