package main

import (
	"database/sql"
	"encoding/json"
	"fmt"
	_ "github.com/lib/pq"
	"os"
)

type GraphNode struct {
	Id    string `json:"id"`
	Group string `json:"group"`
}

type GraphLink struct {
	Source string `json:"source"`
	Target string `json:"target"`
	Value  int    `json:"value"`
}

type GraphData struct {
	Nodes []GraphNode `json:"nodes"`
	Links []GraphLink `json:"links"`
}

func main() {
	connStr := "user=postgres password=mysecretpassword dbname=dna_match_db sslmode=disable"
	db, err := sql.Open("postgres", connStr)
	if err != nil {
		fmt.Println("Error connecting to the database:", err)
		return
	}
	defer db.Close()

	// Pavel
	/*
		offset := 0
		sourceIndividualId := "individual-32361932-1000006"
		dnaMatchIDs, err := getDnaMatchesByIndividualId(db, sourceIndividualId, offset, 500)
		bearerToken := "4.cbdd04f731661d487d728136bd8331a0.122085562.1725369381.1440.7345153..s0gts5f0.881b70f1bb9aaa4b3d9ce42ab04ca654004f194b2c7b8298a45976d057f6195d"
	*/

	// Elena

	//offset := 0
	//sourceIndividualId := "individual-32361932-1000002"
	//dnaMatchIDs, err := getDnaMatchesByIndividualId(db, sourceIndividualId, offset, 500)
	//bearerToken := os.Getenv("BEARER_TOKEN")

	//Dmitrii
	offset := 0
	sourceIndividualId := "individual-32361932-1000003"
	dnaMatchIDs, err := getDnaMatchesByIndividualId(db, sourceIndividualId, offset, 1)
	sourceName := "Dmitrii Magur"

	graphData := GraphData{}
	graphData.Nodes = append(graphData.Nodes, GraphNode{Id: sourceName, Group: sourceIndividualId})
	nodesHashMap := make(map[string]bool)
	nodesHashMap[sourceName] = true
	linksHashMap := make(map[string]bool)

	// Now loop through the slice of DnaMatch
	for _, match := range dnaMatchIDs {
		if match.MatchIndividualID == "" {
			continue
		}

		if nodesHashMap[match.MatchIndividualID] != true {
			graphData.Nodes = append(graphData.Nodes, GraphNode{Id: match.Name, Group: match.MatchIndividualID})
			nodesHashMap[match.MatchIndividualID] = true
		}

		if linksHashMap[sourceIndividualId+match.MatchIndividualID] != true && !linksHashMap[match.MatchIndividualID+sourceIndividualId] != true {
			graphData.Links = append(graphData.Links, GraphLink{Source: sourceName, Target: match.Name, Value: int(match.SharedLength)})
			nodesHashMap[sourceIndividualId+match.MatchIndividualID] = true
		}

		nodesHashMap, linksHashMap = getSharedMatches(db, match, &graphData, 2, nodesHashMap, linksHashMap)
	}

	// Convert the struct to JSON
	jsonData, err := json.MarshalIndent(graphData, "", "  ")
	if err != nil {
		fmt.Println("Error converting struct to JSON:", err)
		return
	}

	//fmt.Printf("%v\n", jsonData)

	// Save the JSON data to a file
	file, err := os.Create("person.json")
	if err != nil {
		fmt.Println("Error creating JSON file:", err)
		return
	}
	defer file.Close()

	_, err = file.Write(jsonData)
	if err != nil {
		fmt.Println("Error writing to JSON file:", err)
		return
	}

	fmt.Println("JSON data saved to person.json")
}

func getSharedMatches(db *sql.DB, match Result, graphData *GraphData, level int, nodesHashMap map[string]bool, linksHashMap map[string]bool) (map[string]bool, map[string]bool) {
	if level > 6 {
		return nodesHashMap, linksHashMap
	}
	level++

	sharedMatches, err := getSharedDnaMatchesByIndividualId(db, match.MatchIndividualID, 0, 10)
	fmt.Printf("level: %d\n", level)
	if err != nil {
		fmt.Println("Error fetching shared matches:", err)
		return nodesHashMap, linksHashMap
	}
	for _, sharedMatch := range sharedMatches {
		if match.MatchIndividualID == "" {
			continue
		}

		if nodesHashMap[sharedMatch.MatchIndividualID] != true {
			graphData.Nodes = append(graphData.Nodes, GraphNode{Id: sharedMatch.Name, Group: match.MatchIndividualID})
			nodesHashMap[sharedMatch.MatchIndividualID] = true
		}

		if !linksHashMap[sharedMatch.MatchIndividualID+match.MatchIndividualID] && !linksHashMap[match.MatchIndividualID+sharedMatch.MatchIndividualID] {
			graphData.Links = append(graphData.Links, GraphLink{Source: match.Name, Target: sharedMatch.Name, Value: int(sharedMatch.SharedLength)})
			nodesHashMap[sharedMatch.MatchIndividualID+match.MatchIndividualID] = true
		}

		nodesHashMap, linksHashMap = getSharedMatches(db, sharedMatch, graphData, level, nodesHashMap, linksHashMap)
	}

	return nodesHashMap, linksHashMap
}
