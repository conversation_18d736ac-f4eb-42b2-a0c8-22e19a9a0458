package fetch

import (
	"bytes"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/dmagur/myheritage/internal/database"
	log "github.com/sirupsen/logrus"
)

type BearerToken string

type MyHeritageApiClient struct {
	bearerToken BearerToken
	db          *database.GormDB
	logger      *log.Logger
}

func NewMyHeritageClient(bearerToken BearerToken, db *database.GormDB) *MyHeritageApiClient {
	// Create a default logger if none is provided
	logger := log.New()
	logger.SetFormatter(&log.TextFormatter{})

	return &MyHeritageApiClient{
		bearerToken: bearerToken,
		db:          db,
		logger:      logger,
	}
}

func (c MyHeritageApiClient) Fetch(url string, query string, queryName string) ([]byte, error) {
	// Log the full token being used
	tokenStr := string(c.bearerToken)
	if len(tokenStr) > 0 {
		c.logger.Infof("Using bearer token for API request: %s (FULL TOKEN)", tokenStr)
	} else {
		c.logger.Warn("No bearer token provided for API request!")
	}

	boundary := "------WebKitFormBoundary4t41amcgIlOuUB7u"
	// Define the multipart form data payload
	payload := fmt.Sprintf(`%s
Content-Disposition: form-data; name="bearer_token"

%s
%s
Content-Disposition: form-data; name="query"

%s
%s
Content-Disposition: form-data; name="operation"


%s
Content-Disposition: form-data; name="variables"


%s
Content-Disposition: form-data; name="site_id"

32361932
%s
Content-Disposition: form-data; name="description"

%s
%s
Content-Disposition: form-data; name="mhc#PHPSESSID"

fcc2609ff52f638fd60ce1be66b204ac
%s--
`, boundary, c.bearerToken, boundary, query, boundary, boundary, boundary, boundary, queryName, boundary, boundary)

	// Create a new request with the payload
	req, err := http.NewRequest("POST", url, bytes.NewBuffer([]byte(payload)))
	if err != nil {
		fmt.Println("Error creating request:", err)
	}

	// Set the headers
	req.Header.Set("Content-Type", "multipart/form-data; boundary=----WebKitFormBoundary4t41amcgIlOuUB7u")

	// Create an HTTP client and send the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		fmt.Println("Error sending request:", err)
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	//fmt.Printf("Response body: %s\n", string(body))
	if err != nil {
		fmt.Println("Error reading response body:", err)
		return nil, err
	}

	currentTime := time.Now()

	// Create a new API call record
	apiCall := database.ApiCall{
		URL:       url,
		Response:  string(body),
		CreatedAt: currentTime.Format("2006-01-02 15:04:05"),
		Query:     query,
	}

	// Save the API call to the database using GORM
	result := c.db.DB.Create(&apiCall)
	if result.Error != nil {
		fmt.Println("Error saving api call in db:", result.Error)
		return nil, result.Error
	}

	if strings.Contains(string(body), "exceeded the allowed rate limit") {
		c.logger.Error("API request failed: exceeded the allowed rate limit")
		return nil, fmt.Errorf("exceeded the allowed rate limit")
	}

	if strings.Contains(string(body), "Invalid OAuth2 access token") {
		c.logger.Errorf("API request failed: Invalid OAuth2 access token. Token used: %s (FULL TOKEN)", string(c.bearerToken))
		return nil, fmt.Errorf("Invalid OAuth2 access token")
	}

	c.logger.Info("API request successful")
	return body, nil
}
