import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Re<PERSON>ons<PERSON><PERSON><PERSON><PERSON>,
  Legend,
} from 'recharts';
import { Box, Typography } from '@mui/material';

interface QuickChartProps {
  type: 'pie' | 'line';
}

const QuickChart: React.FC<QuickChartProps> = ({ type }) => {
  // Sample data for demonstration
  const pieData = [
    { name: 'DNA Matches', value: 1460538, color: '#1976d2' },
    { name: 'Individuals', value: 308823, color: '#388e3c' },
    { name: 'Families', value: 209313, color: '#00796b' },
    { name: 'Surnames', value: 150377, color: '#5d4037' },
    { name: 'Shared Segments', value: 25805, color: '#7b1fa2' },
    { name: 'Trees', value: 23236, color: '#f57c00' },
  ];

  const lineData = [
    { month: 'Jan', matches: 120000, individuals: 25000 },
    { month: 'Feb', matches: 135000, individuals: 27000 },
    { month: 'Mar', matches: 148000, individuals: 29000 },
    { month: 'Apr', matches: 162000, individuals: 31000 },
    { month: 'May', matches: 175000, individuals: 33000 },
    { month: 'Jun', matches: 190000, individuals: 35000 },
  ];

  if (type === 'pie') {
    return (
      <Box sx={{ width: '100%', height: '300px' }}>
        <ResponsiveContainer width="100%" height="100%">
          <PieChart>
            <Pie
              data={pieData}
              cx="50%"
              cy="50%"
              labelLine={false}
              label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
              outerRadius={80}
              fill="#8884d8"
              dataKey="value"
            >
              {pieData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.color} />
              ))}
            </Pie>
            <Tooltip
              formatter={(value: number) => [value.toLocaleString(), 'Count']}
            />
            <Legend />
          </PieChart>
        </ResponsiveContainer>
      </Box>
    );
  }

  if (type === 'line') {
    return (
      <Box sx={{ width: '100%', height: '300px' }}>
        <ResponsiveContainer width="100%" height="100%">
          <LineChart data={lineData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="month" />
            <YAxis />
            <Tooltip
              formatter={(value: number) => [value.toLocaleString(), '']}
            />
            <Legend />
            <Line
              type="monotone"
              dataKey="matches"
              stroke="#1976d2"
              strokeWidth={2}
              name="DNA Matches"
            />
            <Line
              type="monotone"
              dataKey="individuals"
              stroke="#388e3c"
              strokeWidth={2}
              name="Individuals"
            />
          </LineChart>
        </ResponsiveContainer>
      </Box>
    );
  }

  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '300px',
      }}
    >
      <Typography color="text.secondary">
        Chart type not supported
      </Typography>
    </Box>
  );
};

export default QuickChart;
