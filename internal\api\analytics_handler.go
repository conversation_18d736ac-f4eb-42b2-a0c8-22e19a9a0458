package api

import (
	"net/http"
	"time"

	"github.com/dmagur/myheritage/internal/database"
	"github.com/gin-gonic/gin"
	log "github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// AnalyticsHandler handles API requests for analytics
type AnalyticsHandler struct {
	db     *gorm.DB
	logger *log.Logger
}

// NewAnalyticsHandler creates a new AnalyticsHandler
func NewAnalyticsHandler(db *gorm.DB, logger *log.Logger) *AnalyticsHandler {
	return &AnalyticsHandler{
		db:     db,
		logger: logger,
	}
}

// RegisterRoutes registers the analytics routes
func (h *AnalyticsHandler) RegisterRoutes(router *gin.Engine) {
	// Group API routes under /api/v1/analytics
	analytics := router.Group("/api/v1/analytics")
	{
		analytics.GET("/overview", h.GetOverview)
		analytics.GET("/matches/stats", h.GetMatchStats)
		analytics.GET("/geography/top-countries", h.GetTopCountries)
		analytics.GET("/individuals/stats", h.GetIndividualStats)
	}
}

// GetOverview returns database overview statistics
func (h *AnalyticsHandler) GetOverview(c *gin.Context) {
	h.logger.Info("Getting database overview statistics")

	var overview OverviewResponse
	overview.LastUpdated = time.Now()

	// Count DNA matches
	var dnaMatchCount int64
	if err := h.db.Model(&database.DNAMatch{}).Count(&dnaMatchCount).Error; err != nil {
		h.logger.WithError(err).Error("Failed to count DNA matches")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get DNA match count"})
		return
	}
	overview.TotalDNAMatches = dnaMatchCount

	// Count individuals
	var individualCount int64
	if err := h.db.Model(&database.Individual{}).Count(&individualCount).Error; err != nil {
		h.logger.WithError(err).Error("Failed to count individuals")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get individual count"})
		return
	}
	overview.TotalIndividuals = individualCount

	// Count trees
	var treeCount int64
	if err := h.db.Model(&database.Tree{}).Count(&treeCount).Error; err != nil {
		h.logger.WithError(err).Error("Failed to count trees")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get tree count"})
		return
	}
	overview.TotalTrees = treeCount

	// Count shared segments
	var segmentCount int64
	if err := h.db.Model(&database.SharedSegment{}).Count(&segmentCount).Error; err != nil {
		h.logger.WithError(err).Error("Failed to count shared segments")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get shared segment count"})
		return
	}
	overview.TotalSharedSegments = segmentCount

	// Count submitters
	var submitterCount int64
	if err := h.db.Model(&database.Submitter{}).Count(&submitterCount).Error; err != nil {
		h.logger.WithError(err).Error("Failed to count submitters")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get submitter count"})
		return
	}
	overview.TotalSubmitters = submitterCount

	// Count families
	var familyCount int64
	if err := h.db.Model(&database.Family{}).Count(&familyCount).Error; err != nil {
		h.logger.WithError(err).Error("Failed to count families")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get family count"})
		return
	}
	overview.TotalFamilies = familyCount

	// Count surnames
	var surnameCount int64
	if err := h.db.Model(&database.Surname{}).Count(&surnameCount).Error; err != nil {
		h.logger.WithError(err).Error("Failed to count surnames")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get surname count"})
		return
	}
	overview.TotalSurnames = surnameCount

	h.logger.WithFields(log.Fields{
		"dna_matches":     overview.TotalDNAMatches,
		"individuals":     overview.TotalIndividuals,
		"trees":           overview.TotalTrees,
		"shared_segments": overview.TotalSharedSegments,
		"submitters":      overview.TotalSubmitters,
		"families":        overview.TotalFamilies,
		"surnames":        overview.TotalSurnames,
	}).Info("Database overview statistics retrieved")

	c.JSON(http.StatusOK, overview)
}

// GetMatchStats returns DNA match statistics
func (h *AnalyticsHandler) GetMatchStats(c *gin.Context) {
	h.logger.Info("Getting DNA match statistics")

	var stats MatchStatsResponse

	// Get total count
	if err := h.db.Model(&database.DNAMatch{}).Count(&stats.TotalMatches).Error; err != nil {
		h.logger.WithError(err).Error("Failed to count DNA matches")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get match count"})
		return
	}

	// Get cM statistics
	var cmStats struct {
		AvgCM float64
		MaxCM float64
		MinCM float64
	}

	if err := h.db.Model(&database.DNAMatch{}).
		Select("AVG(total_shared_segments_length_in_cm) as avg_cm, MAX(total_shared_segments_length_in_cm) as max_cm, MIN(total_shared_segments_length_in_cm) as min_cm").
		Where("total_shared_segments_length_in_cm > 0").
		Scan(&cmStats).Error; err != nil {
		h.logger.WithError(err).Error("Failed to get cM statistics")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get cM statistics"})
		return
	}

	stats.AvgSharedCM = cmStats.AvgCM
	stats.MaxSharedCM = cmStats.MaxCM
	stats.MinSharedCM = cmStats.MinCM

	// Get confidence level distribution
	var confidenceResults []struct {
		ConfidenceLevel string
		Count           int64
	}

	if err := h.db.Model(&database.DNAMatch{}).
		Select("confidence_level, COUNT(*) as count").
		Group("confidence_level").
		Scan(&confidenceResults).Error; err != nil {
		h.logger.WithError(err).Error("Failed to get confidence distribution")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get confidence distribution"})
		return
	}

	stats.MatchesByConfidence = make(map[string]int64)
	for _, result := range confidenceResults {
		if result.ConfidenceLevel == "" {
			stats.MatchesByConfidence["unknown"] = result.Count
		} else {
			stats.MatchesByConfidence[result.ConfidenceLevel] = result.Count
		}
	}

	// Get relationship distribution
	var relationshipResults []struct {
		ExactDnaRelationship string
		Count                int64
	}

	if err := h.db.Model(&database.DNAMatch{}).
		Select("exact_dna_relationship, COUNT(*) as count").
		Group("exact_dna_relationship").
		Limit(10).
		Scan(&relationshipResults).Error; err != nil {
		h.logger.WithError(err).Error("Failed to get relationship distribution")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get relationship distribution"})
		return
	}

	stats.MatchesByRelationship = make(map[string]int64)
	for _, result := range relationshipResults {
		if result.ExactDnaRelationship == "" {
			stats.MatchesByRelationship["unknown"] = result.Count
		} else {
			stats.MatchesByRelationship[result.ExactDnaRelationship] = result.Count
		}
	}

	// Create cM distribution buckets
	buckets := []struct {
		MinCM float64
		MaxCM float64
	}{
		{0, 10},
		{10, 20},
		{20, 50},
		{50, 100},
		{100, 200},
		{200, 500},
		{500, 1000},
		{1000, 10000},
	}

	stats.CMDistribution = make([]CMDistributionBucket, len(buckets))
	for i, bucket := range buckets {
		var count int64
		if err := h.db.Model(&database.DNAMatch{}).
			Where("total_shared_segments_length_in_cm >= ? AND total_shared_segments_length_in_cm < ?", bucket.MinCM, bucket.MaxCM).
			Count(&count).Error; err != nil {
			h.logger.WithError(err).Error("Failed to get cM distribution bucket")
			continue
		}
		stats.CMDistribution[i] = CMDistributionBucket{
			MinCM: bucket.MinCM,
			MaxCM: bucket.MaxCM,
			Count: count,
		}
	}

	h.logger.WithFields(log.Fields{
		"total_matches": stats.TotalMatches,
		"avg_cm":        stats.AvgSharedCM,
		"max_cm":        stats.MaxSharedCM,
		"min_cm":        stats.MinSharedCM,
	}).Info("DNA match statistics retrieved")

	c.JSON(http.StatusOK, stats)
}

// GetTopCountries returns the top countries by DNA matches
func (h *AnalyticsHandler) GetTopCountries(c *gin.Context) {
	h.logger.Info("Getting top countries statistics")

	var results []struct {
		Country string
		Count   int64
	}

	// Get top countries from trees table (site_creator_country)
	if err := h.db.Table("trees").
		Select("site_creator_country as country, COUNT(*) as count").
		Where("site_creator_country IS NOT NULL AND site_creator_country != ''").
		Group("site_creator_country").
		Order("count DESC").
		Limit(20).
		Scan(&results).Error; err != nil {
		h.logger.WithError(err).Error("Failed to get top countries")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get top countries"})
		return
	}

	var response TopCountriesResponse
	response.Countries = make([]CountryStats, len(results))
	var total int64

	for i, result := range results {
		response.Countries[i] = CountryStats{
			Country: result.Country,
			Count:   result.Count,
		}
		total += result.Count
	}
	response.Total = total

	h.logger.WithField("countries_count", len(response.Countries)).Info("Top countries statistics retrieved")

	c.JSON(http.StatusOK, response)
}

// GetIndividualStats returns individual demographics statistics
func (h *AnalyticsHandler) GetIndividualStats(c *gin.Context) {
	h.logger.Info("Getting individual statistics")

	var stats IndividualStatsResponse

	// Get total count
	if err := h.db.Model(&database.Individual{}).Count(&stats.TotalIndividuals).Error; err != nil {
		h.logger.WithError(err).Error("Failed to count individuals")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get individual count"})
		return
	}

	// Get gender distribution
	var genderResults []struct {
		Gender string
		Count  int64
	}

	if err := h.db.Model(&database.Individual{}).
		Select("gender, COUNT(*) as count").
		Group("gender").
		Scan(&genderResults).Error; err != nil {
		h.logger.WithError(err).Error("Failed to get gender distribution")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get gender distribution"})
		return
	}

	stats.GenderDistribution = make(map[string]int64)
	for _, result := range genderResults {
		if result.Gender == "" {
			stats.GenderDistribution["unknown"] = result.Count
		} else {
			stats.GenderDistribution[result.Gender] = result.Count
		}
	}

	// Get age group distribution
	var ageResults []struct {
		AgeGroup string
		Count    int64
	}

	if err := h.db.Model(&database.Individual{}).
		Select("age_group, COUNT(*) as count").
		Group("age_group").
		Scan(&ageResults).Error; err != nil {
		h.logger.WithError(err).Error("Failed to get age group distribution")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get age group distribution"})
		return
	}

	stats.AgeGroupDistribution = make(map[string]int64)
	for _, result := range ageResults {
		if result.AgeGroup == "" {
			stats.AgeGroupDistribution["unknown"] = result.Count
		} else {
			stats.AgeGroupDistribution[result.AgeGroup] = result.Count
		}
	}

	// Count individuals with trees
	if err := h.db.Model(&database.Individual{}).
		Where("tree_id IS NOT NULL AND tree_id != ''").
		Count(&stats.IndividualsWithTrees).Error; err != nil {
		h.logger.WithError(err).Error("Failed to count individuals with trees")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to count individuals with trees"})
		return
	}

	h.logger.WithFields(log.Fields{
		"total_individuals":      stats.TotalIndividuals,
		"individuals_with_trees": stats.IndividualsWithTrees,
	}).Info("Individual statistics retrieved")

	c.JSON(http.StatusOK, stats)
}
