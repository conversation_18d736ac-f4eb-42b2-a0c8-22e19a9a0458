#!/bin/bash

# Run the API integration tests
echo "Running API integration tests..."

# Check if the API server is already running
API_SERVER_RUNNING=false
if curl -s http://localhost:1231/health > /dev/null 2>&1; then
    echo "API server is already running"
    API_SERVER_RUNNING=true
else
    echo "API server is not running, starting it now..."
fi

# Start the API server if it's not already running
API_SERVER_PID=""
if [ "$API_SERVER_RUNNING" = false ]; then
    # Start the API server in the background
    go run cmd/apiserver/main.go cmd/apiserver/app.go &
    API_SERVER_PID=$!
    
    # Wait for the API server to start
    echo "Waiting for API server to start..."
    MAX_RETRIES=10
    RETRY_COUNT=0
    SERVER_STARTED=false
    
    while [ "$SERVER_STARTED" = false ] && [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
        if curl -s http://localhost:1231/health > /dev/null 2>&1; then
            echo "API server started successfully"
            SERVER_STARTED=true
        else
            echo "Waiting for API server to start... ($(($RETRY_COUNT + 1))/$MAX_RETRIES)"
            sleep 1
            RETRY_COUNT=$((RETRY_COUNT + 1))
        fi
    done
    
    if [ "$SERVER_STARTED" = false ]; then
        echo "Failed to start API server within the timeout period"
        if [ -n "$API_SERVER_PID" ]; then
            kill -9 $API_SERVER_PID
        fi
        exit 1
    fi
fi

# Run the integration tests
echo "Running integration tests..."
RUN_INTEGRATION_TESTS=true go test -v ./cmd/apiserver -run TestAPIIntegration

# Store the test result
TEST_RESULT=$?

# Stop the API server if we started it
if [ "$API_SERVER_RUNNING" = false ] && [ -n "$API_SERVER_PID" ]; then
    echo "Stopping API server..."
    kill -9 $API_SERVER_PID
fi

echo "Tests completed"
exit $TEST_RESULT
