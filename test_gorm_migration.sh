#!/bin/bash

# GORM Migration Test Suite
# Comprehensive testing script for MyHeritage GORM migration
# Author: Augment Agent
# Date: $(date +%Y-%m-%d)

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Test counters
TESTS_PASSED=0
TESTS_FAILED=0
TOTAL_TESTS=0

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    case $status in
        "PASS")
            echo -e "${GREEN}✅ $message${NC}"
            ((TESTS_PASSED++))
            ;;
        "FAIL")
            echo -e "${RED}❌ $message${NC}"
            ((TESTS_FAILED++))
            ;;
        "INFO")
            echo -e "${BLUE}ℹ️  $message${NC}"
            ;;
        "WARN")
            echo -e "${YELLOW}⚠️  $message${NC}"
            ;;
        "HEADER")
            echo -e "${PURPLE}🔍 === $message ===${NC}"
            ;;
        "SUBHEADER")
            echo -e "${CYAN}📋 $message${NC}"
            ;;
    esac
    ((TOTAL_TESTS++))
}

# Function to run a test command
run_test() {
    local test_name=$1
    local command=$2
    local expected_exit_code=${3:-0}

    print_status "INFO" "Running: $test_name"

    if eval "$command" >/dev/null 2>&1; then
        local exit_code=$?
        if [ $exit_code -eq $expected_exit_code ]; then
            print_status "PASS" "$test_name"
            return 0
        else
            print_status "FAIL" "$test_name (exit code: $exit_code, expected: $expected_exit_code)"
            return 1
        fi
    else
        print_status "FAIL" "$test_name (command failed)"
        return 1
    fi
}

# Function to check if a file exists
check_file_exists() {
    local file_path=$1
    local description=$2

    if [ -f "$file_path" ]; then
        print_status "PASS" "$description exists: $file_path"
        return 0
    else
        print_status "FAIL" "$description missing: $file_path"
        return 1
    fi
}

# Function to check if a directory exists
check_dir_exists() {
    local dir_path=$1
    local description=$2

    if [ -d "$dir_path" ]; then
        print_status "PASS" "$description exists: $dir_path"
        return 0
    else
        print_status "FAIL" "$description missing: $dir_path"
        return 1
    fi
}

# Function to check Go module dependencies
check_gorm_dependency() {
    print_status "SUBHEADER" "Checking GORM Dependencies"

    if go list -m gorm.io/gorm >/dev/null 2>&1; then
        local gorm_version=$(go list -m gorm.io/gorm | awk '{print $2}')
        print_status "PASS" "GORM dependency found: $gorm_version"
    else
        print_status "FAIL" "GORM dependency not found"
        return 1
    fi

    if go list -m gorm.io/driver/postgres >/dev/null 2>&1; then
        local postgres_version=$(go list -m gorm.io/driver/postgres | awk '{print $2}')
        print_status "PASS" "PostgreSQL driver found: $postgres_version"
    else
        print_status "FAIL" "PostgreSQL driver not found"
        return 1
    fi

    if go list -m gorm.io/driver/mysql >/dev/null 2>&1; then
        local mysql_version=$(go list -m gorm.io/driver/mysql | awk '{print $2}')
        print_status "PASS" "MySQL driver found: $mysql_version"
    else
        print_status "FAIL" "MySQL driver not found"
        return 1
    fi
}

# Help function
show_help() {
    echo "GORM Migration Test Suite"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -h, --help     Show this help message"
    echo "  -q, --quiet    Run tests with minimal output"
    echo "  -v, --verbose  Run tests with verbose output"
    echo "  --no-docker    Skip Docker build tests"
    echo "  --quick        Run only essential tests (skip Docker and performance tests)"
    echo ""
    echo "Examples:"
    echo "  $0              # Run all tests"
    echo "  $0 --quick      # Run quick tests only"
    echo "  $0 --no-docker  # Skip Docker tests"
}

# Main test execution
main() {
    print_status "HEADER" "GORM Migration Test Suite"
    echo -e "${BLUE}Starting comprehensive testing of MyHeritage GORM migration...${NC}"
    echo ""

    # Check if we're in the right directory
    if [ ! -f "go.mod" ]; then
        print_status "FAIL" "Not in Go project root (go.mod not found)"
        exit 1
    fi

    print_status "PASS" "Found Go project root"

    # Test 1: Go Module Dependencies
    print_status "HEADER" "Testing Go Module Dependencies"
    check_gorm_dependency
    echo ""

    # Test 2: Core Package Compilation
    print_status "HEADER" "Testing Core Package Compilation"
    run_test "Database package compilation" "go build ./internal/database"
    run_test "Fetch package compilation" "go build ./internal/fetch"
    run_test "Models package compilation" "go build ./internal/models"
    echo ""

    # Test 3: Application Compilation
    print_status "HEADER" "Testing Application Compilation"
    run_test "MyHeritage Downloader compilation" "go build ./cmd/myheritagedownloader"
    run_test "API Server compilation" "go build ./cmd/apiserver"
    run_test "Clique compilation" "go build ./cmd/clique"
    echo ""

    # Test 4: Comprehensive Build
    print_status "HEADER" "Testing Comprehensive Build"
    run_test "All packages compilation" "go build ./..."
    echo ""

    # Test 5: Unit Tests
    print_status "HEADER" "Running Unit Tests"
    run_test "Database package tests" "go test ./internal/database -v"
    run_test "Models package tests" "go test ./internal/models -v"
    run_test "All package tests" "go test ./... -short"
    echo ""

    # Test 6: Code Quality Checks
    print_status "HEADER" "Code Quality Checks"
    run_test "Go fmt check" "test -z \$(gofmt -l .)"
    run_test "Go vet check" "go vet ./..."
    echo ""

    # Test 7: File Structure Verification
    print_status "HEADER" "Verifying File Structure"
    check_file_exists "internal/database/README.md" "Database documentation"
    check_file_exists "internal/database/db_adapter.go" "Database adapter"
    check_file_exists "internal/database/factory.go" "Database factory"
    check_file_exists "internal/database/mariadb.go" "MariaDB implementation"
    check_file_exists "internal/database/pgsql.go" "PostgreSQL implementation"
    check_dir_exists "cmd/myheritagedownloader" "MyHeritage Downloader"
    check_dir_exists "cmd/apiserver" "API Server"
    check_dir_exists "cmd/clique" "Clique application"
    echo ""

    # Test 8: Docker Build Tests (skip if NO_DOCKER or QUICK_MODE)
    if [ "$NO_DOCKER" = false ] && [ "$QUICK_MODE" = false ]; then
        print_status "HEADER" "Testing Docker Builds"
        if command -v docker >/dev/null 2>&1; then
            run_test "Docker downloader build" "docker build -f Dockerfile.downloader -t myheritage-downloader-test ."
            run_test "Docker API server build" "docker build -f Dockerfile.apiserver -t myheritage-apiserver-test ."

            # Clean up test images
            docker rmi myheritage-downloader-test >/dev/null 2>&1 || true
            docker rmi myheritage-apiserver-test >/dev/null 2>&1 || true
        else
            print_status "WARN" "Docker not available, skipping Docker build tests"
        fi
        echo ""
    fi

    # Test 9: Git Repository Status
    print_status "HEADER" "Git Repository Status"
    if git rev-parse --git-dir >/dev/null 2>&1; then
        local git_status=$(git status --porcelain)
        if [ -z "$git_status" ]; then
            print_status "PASS" "Git working directory is clean"
        else
            print_status "WARN" "Git working directory has uncommitted changes"
        fi

        local branch=$(git branch --show-current)
        print_status "INFO" "Current branch: $branch"

        local commits_ahead=$(git rev-list --count HEAD ^origin/$branch 2>/dev/null || echo "0")
        if [ "$commits_ahead" -gt 0 ]; then
            print_status "INFO" "Branch is $commits_ahead commits ahead of origin"
        else
            print_status "INFO" "Branch is up to date with origin"
        fi
    else
        print_status "WARN" "Not in a Git repository"
    fi
    echo ""

    # Test 10: Performance and Memory Tests (skip if QUICK_MODE)
    if [ "$QUICK_MODE" = false ]; then
        print_status "HEADER" "Performance Tests"
        run_test "Race condition detection" "go test -race ./internal/database"
        run_test "Memory leak detection" "go test -memprofile=mem.prof ./internal/database && rm -f mem.prof"
        echo ""
    fi

    # Final Summary
    print_status "HEADER" "Test Summary"
    echo -e "${BLUE}Total tests run: $TOTAL_TESTS${NC}"
    echo -e "${GREEN}Tests passed: $TESTS_PASSED${NC}"
    echo -e "${RED}Tests failed: $TESTS_FAILED${NC}"

    if [ $TESTS_FAILED -eq 0 ]; then
        echo ""
        echo -e "${GREEN}🎉 ALL TESTS PASSED! 🎉${NC}"
        echo -e "${GREEN}GORM migration is working correctly!${NC}"
        exit 0
    else
        echo ""
        echo -e "${RED}❌ SOME TESTS FAILED ❌${NC}"
        echo -e "${RED}Please review the failed tests above.${NC}"
        exit 1
    fi
}

# Parse command line arguments
QUICK_MODE=false
NO_DOCKER=false
VERBOSE=false
QUIET=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -q|--quiet)
            QUIET=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        --no-docker)
            NO_DOCKER=true
            shift
            ;;
        --quick)
            QUICK_MODE=true
            shift
            ;;
        *)
            echo "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Run main function
main
