version: '3'
services:
  apiserver:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: my_api_server
    environment:
      - PORT=1231
      - DB_TYPE=mariadb
      - DB_HOST=*************
      - DB_USER=myheritage
      - DB_PASSWORD=Xq(RDl*5C7lkocf1
      - DB_NAME=myheritage
    ports:
      - "1231:1231"
    restart: unless-stopped

  downloader:
    build:
      context: .
      dockerfile: Dockerfile.downloader
    container_name: my_downloader
    environment:
      - DB_TYPE=mariadb
      - DB_HOST=*************
      - DB_USER=myheritage
      - DB_PASSWORD=Xq(RDl*5C7lkocf1
      - DB_NAME=myheritage
    restart: unless-stopped
