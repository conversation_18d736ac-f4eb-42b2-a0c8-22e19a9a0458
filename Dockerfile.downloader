FROM golang:1.23-alpine AS builder

WORKDIR /app

# Copy go.mod and go.sum files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy the source code
COPY . .

# Build the application
RUN CGO_ENABLED=0 GOOS=linux go build -o downloader cmd/myheritagedownloader/main.go cmd/myheritagedownloader/app.go cmd/myheritagedownloader/wire_gen.go

# Create a minimal image
FROM alpine:latest

WORKDIR /app

# Install tzdata for timezone support
RUN apk add --no-cache tzdata

# Copy the binary from the builder stage
COPY --from=builder /app/downloader .

# Set environment variables
ENV DB_TYPE=mariadb
ENV DB_HOST=*************
ENV DB_USER=myheritage
ENV DB_PASSWORD=Xq(RDl*5C7lkocf1
ENV DB_NAME=myheritage

# Run the application
CMD ["./downloader"]
