package sharedsegments

// Chromosome represents a single chromosome's information.
type Chromosome struct {
	ID                       int     `json:"id"`
	StartLocation            int     `json:"start_location"`
	EndLocation              int     `json:"end_location"`
	Length                   int     `json:"length"`
	LengthInPercentage       float64 `json:"length_in_percentage"`
	CentromerePositionInPerc float64 `json:"centromere_position_in_percents"`
}

// NotSampledSegment represents a chromosome segment that was not sampled.
type NotSampledSegment struct {
	ChromosomeID    int     `json:"chromosome_id"`
	StartPosition   int     `json:"start_position"`
	EndPosition     int     `json:"end_position"`
	StartPercentage float64 `json:"start_percentage"`
	EndPercentage   float64 `json:"end_percentage"`
}

// SystemData represents system-related DNA data.
type SystemData struct {
	Chromosomes              []Chromosome        `json:"chromosomes"`
	NotSampledChromosomeSegs []NotSampledSegment `json:"not_sampled_chromosome_segments"`
}

// DnaSharedSegment represents a segment of shared DNA.
type DnaSharedSegment struct {
	ChromosomeID         string  `json:"chromosome_id"`
	StartPosition        string  `json:"start_position"`
	EndPosition          string  `json:"end_position"`
	StartRsid            string  `json:"start_rsid"`
	EndRsid              string  `json:"end_rsid"`
	LengthInCentimorgans float64 `json:"length_in_centimorgans"`
	SnpCount             int     `json:"snp_count"`
}

// DnaSharedSegmentsData represents the shared DNA segments data.
type DnaSharedSegmentsData struct {
	Count int                `json:"count"`
	Data  []DnaSharedSegment `json:"data"`
}

// DnaKit represents the eligibility variant information.
type DnaKit struct {
	EligibilityVariant string `json:"eligibility_variant"`
}

// DnaMatch represents the DNA match information.
type DnaMatch struct {
	ID                    string                `json:"id"`
	CanViewSharedSegments bool                  `json:"can_view_shared_segments"`
	DnaKit                DnaKit                `json:"dna_kit"`
	DnaSharedSegments     DnaSharedSegmentsData `json:"dna_shared_segments"`
}

// DnaEnums represents the system-level DNA enumerations.
type DnaEnums struct {
	Data []SystemData `json:"data"`
}

// System represents the system part of the DNA data.
type System struct {
	DnaEnums DnaEnums `json:"dna_enums"`
}

// DnaData represents the complete DNA data structure.
type DnaData struct {
	DnaMatch DnaMatch `json:"dna_match"`
}

type ApiResponse struct {
	Data DnaData `json:"data"`
}
