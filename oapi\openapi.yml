openapi: 3.0.0
info:
  title: Myheritage Downloader - API
  version: 0.3.0

servers:
  - description: Development
    url: http://localhost:1231

tags:
  - name: Myheritage Downloader
    description: Myheritage Downloader
  - name: Matches
    description: DNA Matches
  - name: Token Management
    description: API Token Management
  - name: Health
    description: Health and Status Endpoints
  - name: Metrics
    description: Metrics Endpoints

security:
  - ApiKeyAuth: [ ]

components:
  securitySchemes:
    ApiKeyAuth:
      type: apiKey
      in: header
      name: Authorization
      description: API key authentication. Format: "Bearer {token}"

paths:
  /dna-matches:
    get:
      summary: Get dna matches list
      tags:
        - Matches
      operationId: getMatches
      responses:
        200:
          description: Ok
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: 'schemas/dna-match.yml#/components/schemas/DnaMatch'

        default:
          $ref: 'responses.yml#/components/responses/Error'

  /api/v1/token:
    get:
      summary: Get the current active token
      tags:
        - Token Management
      operationId: getToken
      responses:
        200:
          description: Ok
          content:
            application/json:
              schema:
                type: object
                properties:
                  token:
                    type: object
                    properties:
                      token:
                        type: string
                        description: The masked token value
                      created_at:
                        type: string
                        format: date-time
                        description: The creation timestamp
                      is_active:
                        type: boolean
                        description: Whether the token is active
                  message:
                    type: string
                    description: Message when no token is found
        default:
          $ref: 'responses.yml#/components/responses/Error'
    post:
      summary: Update the API token
      tags:
        - Token Management
      operationId: updateToken
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                token:
                  type: string
                  description: The new token value
              required:
                - token
      responses:
        200:
          description: Ok
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    description: Success message
        400:
          description: Bad Request
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    description: Error message
        default:
          $ref: 'responses.yml#/components/responses/Error'

  /health:
    get:
      summary: Health check endpoint
      tags:
        - Health
      operationId: getHealth
      security: []
      responses:
        200:
          description: Ok
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    description: Health status
                    example: ok

  /liveness:
    get:
      summary: Liveness check endpoint
      tags:
        - Health
      operationId: getLiveness
      security: []
      responses:
        200:
          description: Ok
          content:
            text/plain:
              schema:
                type: string

  /readiness:
    get:
      summary: Readiness check endpoint
      tags:
        - Health
      operationId: getReadiness
      security: []
      responses:
        200:
          description: Ok
          content:
            text/plain:
              schema:
                type: string

  /metrics:
    get:
      summary: Prometheus metrics endpoint
      tags:
        - Metrics
      operationId: getMetrics
      security: []
      responses:
        200:
          description: Ok
          content:
            text/plain:
              schema:
                type: string
                description: Prometheus metrics