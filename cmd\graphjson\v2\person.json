{"nodes": [{"id": "Віктор Заноха", "group": "individual-1592334192-1500079"}, {"id": "<PERSON>", "group": "individual-1160031061-1500001"}, {"id": "<PERSON>", "group": "individual-1164926251-1500001"}, {"id": "danie<PERSON> guer<PERSON>i", "group": "individual-1176915291-1500001"}, {"id": "<PERSON>", "group": "individual-1204741252-1500001"}, {"id": "<PERSON>", "group": "individual-1216610922-1500001"}, {"id": "<PERSON><PERSON><PERSON>", "group": "individual-1221710892-1500007"}, {"id": "<PERSON><PERSON> (Pawlak)", "group": "individual-1229844512-1500323"}, {"id": "Nataliia-<PERSON>", "group": "individual-1237744452-1500001"}, {"id": "<PERSON><PERSON><PERSON>", "group": "individual-1245224492-1500001"}, {"id": "<PERSON><PERSON>", "group": "individual-1248415742-1500001"}, {"id": "<PERSON><PERSON><PERSON>", "group": "individual-1260062332-1500001"}, {"id": "<PERSON><PERSON><PERSON>", "group": "individual-1295658292-1500003"}, {"id": "<PERSON><PERSON>", "group": "individual-132474831-1000007"}, {"id": "<Privat> <PERSON><PERSON><PERSON><PERSON>", "group": "individual-13322841-3000001"}, {"id": "<PERSON><PERSON><PERSON><PERSON>", "group": "individual-1352917982-1500001"}, {"id": "<PERSON><PERSON><PERSON><PERSON>", "group": "individual-1378665172-1500001"}, {"id": "<PERSON><PERSON><PERSON>", "group": "individual-1394713382-1500001"}, {"id": "<PERSON>", "group": "individual-1424917132-1500001"}, {"id": "<PERSON>", "group": "individual-1459057422-1500001"}, {"id": "<PERSON><PERSON>", "group": "individual-1468963532-1500001"}, {"id": "<PERSON><PERSON><PERSON><PERSON>", "group": "individual-1471026932-1500002"}, {"id": "<PERSON>", "group": "individual-1476536082-1500003"}, {"id": "<PERSON>", "group": "individual-1477506832-1500001"}, {"id": "<PERSON>", "group": "individual-1480660282-1500002"}, {"id": "<PERSON>", "group": "individual-1495569852-1500001"}, {"id": "<PERSON><PERSON>", "group": "individual-150114892-1000003"}, {"id": "<PERSON>", "group": "individual-1514260312-1500001"}, {"id": "<PERSON><PERSON><PERSON>", "group": "individual-1571631542-1500001"}, {"id": "<PERSON><PERSON>", "group": "individual-1588101742-1500001"}, {"id": "<PERSON>", "group": "individual-1591705882-1500001"}, {"id": "<PERSON>", "group": "individual-1615862302-1500001"}, {"id": "<PERSON>", "group": "individual-1630172918-1500003"}, {"id": "<PERSON><PERSON>", "group": "individual-1630172918-1500005"}, {"id": "<PERSON><PERSON><PERSON>", "group": "individual-1630237542-1500001"}, {"id": "<PERSON>", "group": "individual-1631003926-1500001"}, {"id": "<PERSON><PERSON><PERSON>", "group": "individual-1637877228-1500005"}, {"id": "<PERSON><PERSON><PERSON><PERSON>", "group": "individual-1638418228-1500001"}, {"id": "<PERSON><PERSON>", "group": "individual-1639324158-1500001"}, {"id": "<PERSON><PERSON><PERSON>", "group": "individual-1643476534-1500001"}, {"id": "Stefan <PERSON>", "group": "individual-1644407398-1500001"}, {"id": "Зоя Калинкина", "group": "individual-1644720824-1500002"}, {"id": "Вероника Витальевна Савуляк", "group": "individual-1644720824-1500015"}, {"id": "Nechypor Boris", "group": "individual-1644720824-1500017"}, {"id": "<PERSON>", "group": "individual-1644839238-1500001"}, {"id": "DIANA SKOROKHODOVA", "group": "individual-1645121652-1500002"}, {"id": "Юлия Габинет", "group": "individual-1646670802-1500001"}, {"id": "<PERSON><PERSON>lö<PERSON>", "group": "individual-1647070910-1500002"}, {"id": "<PERSON>", "group": "individual-1650743650-1500003"}, {"id": "<PERSON><PERSON>", "group": "individual-1650866654-1500001"}, {"id": "<PERSON><PERSON>", "group": "individual-1656552720-1500001"}, {"id": "<PERSON><PERSON>", "group": "individual-1657026652-1500001"}, {"id": "<PERSON><PERSON><PERSON>-<PERSON><PERSON> (Szabó)", "group": "individual-194007431-1000002"}, {"id": "<PERSON><PERSON><PERSON>", "group": "individual-197636371-1000003"}, {"id": "<PERSON> (<PERSON> <PERSON><PERSON>)", "group": "individual-206490141-1500003"}, {"id": "<PERSON><PERSON><PERSON>", "group": "individual-250192421-1500003"}, {"id": "<PERSON><PERSON><PERSON>", "group": "individual-269878081-1500003"}, {"id": "<PERSON>", "group": "individual-314248121-1500003"}, {"id": "<PERSON>а<PERSON><PERSON><PERSON>ур", "group": "individual-32361932-1000006"}, {"id": "<PERSON><PERSON><PERSON><PERSON>", "group": "individual-378294561-1500013"}, {"id": "<PERSON><PERSON>he Metzker Birkeland", "group": "individual-379812171-1500001"}, {"id": "<PERSON>", "group": "individual-379812171-1500002"}, {"id": "<PERSON>", "group": "individual-39795452-1500018"}, {"id": "Ека<PERSON><PERSON><PERSON><PERSON><PERSON>абуния", "group": "individual-427604661-1500001"}, {"id": "<PERSON>", "group": "individual-468947151-1500164"}, {"id": "<PERSON><PERSON><PERSON>", "group": "individual-492580601-1500001"}, {"id": "<PERSON>", "group": "individual-523287551-1500005"}, {"id": "<PERSON><PERSON>", "group": "individual-576926081-1500001"}, {"id": "Дарья Бобровская", "group": "individual-578823801-1500010"}, {"id": "<PERSON><PERSON><PERSON>", "group": "individual-578845111-1500002"}, {"id": "<PERSON>", "group": "individual-591213491-1500001"}, {"id": "<PERSON>і<PERSON><PERSON><PERSON><PERSON><PERSON> Дикий", "group": "individual-594084661-1500001"}, {"id": "<PERSON><PERSON>", "group": "individual-600791681-1500001"}, {"id": "<PERSON>", "group": "individual-606910911-1500001"}, {"id": "<PERSON>", "group": "individual-610502611-1500001"}, {"id": "<PERSON>", "group": "individual-610531921-1500001"}, {"id": "<PERSON><PERSON> (Pluta)", "group": "individual-62118452-1000002"}, {"id": "<PERSON><PERSON>", "group": "individual-623837941-1500001"}, {"id": "<PERSON><PERSON>", "group": "individual-632179471-2000677"}, {"id": "<PERSON>", "group": "individual-636109371-1500001"}, {"id": "<PERSON>", "group": "individual-639459911-1500002"}, {"id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "group": "individual-639686001-1500001"}, {"id": "<PERSON>", "group": "individual-643469001-1500001"}, {"id": "<PERSON><PERSON><PERSON>", "group": "individual-654584721-1500001"}, {"id": "WALDEMAR MARCINKOWSKI", "group": "individual-661751231-1500001"}, {"id": "<PERSON><PERSON>", "group": "individual-663042411-1500002"}, {"id": "<PERSON>", "group": "individual-68265941-1000003"}, {"id": "<PERSON><PERSON><PERSON><PERSON>", "group": "individual-685245611-1500001"}, {"id": "<PERSON>рина <PERSON>ымова", "group": "individual-689955951-1500001"}, {"id": "<PERSON>", "group": "individual-690351531-1500001"}, {"id": "Michel PODGORSK<PERSON>", "group": "individual-691485671-1000001"}, {"id": "<PERSON>", "group": "individual-693122061-1500001"}, {"id": "<PERSON>", "group": "individual-696967011-1500001"}, {"id": "<PERSON>", "group": "individual-701541871-1500001"}, {"id": "<PERSON><PERSON>", "group": "individual-707577681-1500005"}, {"id": "<Privat> Szczepaniak", "group": "individual-718541251-1500002"}, {"id": "<Privat> Szczepaniak", "group": "individual-718541251-1500004"}, {"id": "<PERSON>", "group": "individual-719524501-1500001"}, {"id": "<PERSON>", "group": "individual-731468821-1500007"}, {"id": "<PERSON><PERSON>", "group": "individual-739475891-1500001"}, {"id": "<PERSON><PERSON><PERSON>", "group": "individual-747529741-1500001"}, {"id": "<PERSON><PERSON><PERSON>", "group": "individual-747914611-1500001"}, {"id": "<PERSON>", "group": "individual-761948011-1500001"}, {"id": "<Privat> <PERSON><PERSON> (z d. <PERSON>)", "group": "individual-78502623-1000061"}, {"id": "<PERSON>", "group": "individual-788280941-1500001"}, {"id": "<PERSON> (geb. <PERSON>)", "group": "individual-79378303-1000020"}, {"id": "<PERSON><PERSON>", "group": "individual-797409971-1500001"}, {"id": "<PERSON><PERSON><PERSON><PERSON>", "group": "individual-815517511-1500001"}, {"id": "<PERSON>", "group": "individual-818143411-1500001"}, {"id": "<PERSON><PERSON><PERSON><PERSON>", "group": "individual-827236501-1500001"}, {"id": "<PERSON><PERSON>", "group": "individual-834040421-1500044"}, {"id": "<PERSON><PERSON><PERSON>", "group": "individual-835789011-1500001"}, {"id": "Rikke Fjordbak Bentsen", "group": "individual-842259601-1500001"}, {"id": "<PERSON>", "group": "individual-842817771-1500001"}, {"id": "<PERSON>", "group": "individual-846681581-1500001"}, {"id": "<PERSON><PERSON><PERSON> Winter-Sza<PERSON>", "group": "individual-850948131-1500001"}], "links": [{"source": "<PERSON>", "target": "<PERSON>", "value": 13}, {"source": "DIANA SKOROKHODOVA", "target": "<PERSON>", "value": 8}, {"source": "Stefan <PERSON>", "target": "<PERSON>", "value": 17}, {"source": "Michel PODGORSK<PERSON>", "target": "<PERSON>", "value": 13}, {"source": "Юлия Габинет", "target": "<PERSON>", "value": 9}, {"source": "<PERSON><PERSON>he Metzker Birkeland", "target": "<PERSON>", "value": 16}, {"source": "DIANA SKOROKHODOVA", "target": "<PERSON>", "value": 15}, {"source": "Stefan <PERSON>", "target": "<PERSON>", "value": 15}, {"source": "Stefan <PERSON>", "target": "danie<PERSON> guer<PERSON>i", "value": 10}, {"source": "Michel PODGORSK<PERSON>", "target": "danie<PERSON> guer<PERSON>i", "value": 8}, {"source": "<PERSON>", "target": "danie<PERSON> guer<PERSON>i", "value": 9}, {"source": "Michel PODGORSK<PERSON>", "target": "<PERSON>", "value": 13}, {"source": "<PERSON>", "target": "<PERSON>", "value": 8}, {"source": "<PERSON>", "target": "<PERSON>", "value": 15}, {"source": "Юлия Габинет", "target": "<PERSON>", "value": 8}, {"source": "<PERSON><PERSON>he Metzker Birkeland", "target": "<PERSON>", "value": 8}, {"source": "DIANA SKOROKHODOVA", "target": "<PERSON>", "value": 15}, {"source": "Stefan <PERSON>", "target": "<PERSON>", "value": 8}, {"source": "<PERSON>а<PERSON><PERSON><PERSON>ур", "target": "<PERSON>", "value": 8}, {"source": "Stefan <PERSON>", "target": "<PERSON><PERSON><PERSON>", "value": 8}, {"source": "Michel PODGORSK<PERSON>", "target": "<PERSON><PERSON><PERSON>", "value": 14}, {"source": "<PERSON><PERSON>he Metzker Birkeland", "target": "<PERSON><PERSON><PERSON>", "value": 9}, {"source": "<PERSON>", "target": "<PERSON><PERSON><PERSON>", "value": 17}, {"source": "DIANA SKOROKHODOVA", "target": "<PERSON><PERSON><PERSON>", "value": 9}, {"source": "<PERSON>", "target": "<PERSON><PERSON> (Pawlak)", "value": 17}, {"source": "<PERSON>", "target": "Nataliia-<PERSON>", "value": 26}, {"source": "Юлия Габинет", "target": "Nataliia-<PERSON>", "value": 10}, {"source": "DIANA SKOROKHODOVA", "target": "Nataliia-<PERSON>", "value": 15}, {"source": "Stefan <PERSON>", "target": "Nataliia-<PERSON>", "value": 8}, {"source": "Michel PODGORSK<PERSON>", "target": "Nataliia-<PERSON>", "value": 8}, {"source": "<PERSON>", "target": "<PERSON><PERSON><PERSON>", "value": 13}, {"source": "DIANA SKOROKHODOVA", "target": "<PERSON><PERSON><PERSON>", "value": 22}, {"source": "Stefan <PERSON>", "target": "<PERSON><PERSON><PERSON>", "value": 23}, {"source": "Michel PODGORSK<PERSON>", "target": "<PERSON><PERSON><PERSON>", "value": 8}, {"source": "<PERSON>", "target": "<PERSON><PERSON>", "value": 17}, {"source": "Stefan <PERSON>", "target": "<PERSON><PERSON>", "value": 11}, {"source": "Michel PODGORSK<PERSON>", "target": "<PERSON><PERSON>", "value": 21}, {"source": "<PERSON>", "target": "<PERSON><PERSON>", "value": 13}, {"source": "DIANA SKOROKHODOVA", "target": "<PERSON><PERSON><PERSON>", "value": 16}, {"source": "Stefan <PERSON>", "target": "<PERSON><PERSON><PERSON>", "value": 9}, {"source": "<PERSON>", "target": "<PERSON><PERSON><PERSON>", "value": 11}, {"source": "Юлия Габинет", "target": "<PERSON><PERSON><PERSON>", "value": 18}, {"source": "<PERSON><PERSON>he Metzker Birkeland", "target": "<PERSON><PERSON><PERSON>", "value": 26}, {"source": "DIANA SKOROKHODOVA", "target": "<PERSON><PERSON><PERSON>", "value": 12}, {"source": "Stefan <PERSON>", "target": "<PERSON><PERSON><PERSON>", "value": 8}, {"source": "Юлия Габинет", "target": "<PERSON><PERSON><PERSON>", "value": 12}, {"source": "DIANA SKOROKHODOVA", "target": "<PERSON><PERSON>", "value": 8}, {"source": "Stefan <PERSON>", "target": "<PERSON><PERSON>", "value": 10}, {"source": "<PERSON>", "target": "<PERSON><PERSON>", "value": 14}, {"source": "<PERSON>", "target": "<Privat> <PERSON><PERSON><PERSON><PERSON>", "value": 16}, {"source": "<PERSON>", "target": "<Privat> <PERSON><PERSON><PERSON><PERSON>", "value": 18}, {"source": "Юлия Габинет", "target": "<Privat> <PERSON><PERSON><PERSON><PERSON>", "value": 32}, {"source": "<PERSON>", "target": "<Privat> <PERSON><PERSON><PERSON><PERSON>", "value": 14}, {"source": "DIANA SKOROKHODOVA", "target": "<Privat> <PERSON><PERSON><PERSON><PERSON>", "value": 9}, {"source": "Stefan <PERSON>", "target": "<Privat> <PERSON><PERSON><PERSON><PERSON>", "value": 15}, {"source": "Michel PODGORSK<PERSON>", "target": "<Privat> <PERSON><PERSON><PERSON><PERSON>", "value": 13}, {"source": "Michel PODGORSK<PERSON>", "target": "<PERSON><PERSON><PERSON><PERSON>", "value": 16}, {"source": "<PERSON>", "target": "<PERSON><PERSON><PERSON><PERSON>", "value": 20}, {"source": "<PERSON>", "target": "<PERSON><PERSON><PERSON><PERSON>", "value": 13}, {"source": "<PERSON><PERSON>he Metzker Birkeland", "target": "<PERSON><PERSON><PERSON><PERSON>", "value": 19}, {"source": "<PERSON>", "target": "<PERSON><PERSON><PERSON><PERSON>", "value": 14}, {"source": "DIANA SKOROKHODOVA", "target": "<PERSON><PERSON><PERSON><PERSON>", "value": 14}, {"source": "Stefan <PERSON>", "target": "<PERSON><PERSON><PERSON><PERSON>", "value": 14}, {"source": "<PERSON><PERSON>he Metzker Birkeland", "target": "<PERSON><PERSON><PERSON><PERSON>", "value": 14}, {"source": "DIANA SKOROKHODOVA", "target": "<PERSON><PERSON><PERSON><PERSON>", "value": 10}, {"source": "Stefan <PERSON>", "target": "<PERSON><PERSON><PERSON><PERSON>", "value": 10}, {"source": "<PERSON>", "target": "<PERSON><PERSON><PERSON><PERSON>", "value": 16}, {"source": "Юлия Габинет", "target": "<PERSON><PERSON><PERSON><PERSON>", "value": 17}, {"source": "<PERSON>", "target": "<PERSON><PERSON><PERSON>", "value": 12}, {"source": "<PERSON>", "target": "<PERSON><PERSON><PERSON>", "value": 11}, {"source": "Юлия Габинет", "target": "<PERSON><PERSON><PERSON>", "value": 17}, {"source": "<PERSON><PERSON>he Metzker Birkeland", "target": "<PERSON><PERSON><PERSON>", "value": 9}, {"source": "<PERSON>", "target": "<PERSON><PERSON><PERSON>", "value": 13}, {"source": "DIANA SKOROKHODOVA", "target": "<PERSON><PERSON><PERSON>", "value": 17}, {"source": "Michel PODGORSK<PERSON>", "target": "<PERSON>", "value": 19}, {"source": "<PERSON>", "target": "<PERSON>", "value": 13}, {"source": "<PERSON>", "target": "<PERSON>", "value": 14}, {"source": "Stefan <PERSON>", "target": "<PERSON>", "value": 14}, {"source": "Юлия Габинет", "target": "<PERSON>", "value": 10}, {"source": "<PERSON><PERSON>he Metzker Birkeland", "target": "<PERSON>", "value": 8}, {"source": "DIANA SKOROKHODOVA", "target": "<PERSON>", "value": 8}, {"source": "Stefan <PERSON>", "target": "<PERSON>", "value": 15}, {"source": "Michel PODGORSK<PERSON>", "target": "<PERSON>", "value": 12}, {"source": "<PERSON>", "target": "<PERSON>", "value": 9}, {"source": "Stefan <PERSON>", "target": "<PERSON><PERSON>", "value": 8}, {"source": "Michel PODGORSK<PERSON>", "target": "<PERSON><PERSON>", "value": 20}, {"source": "<PERSON>", "target": "<PERSON><PERSON>", "value": 31}, {"source": "Юлия Габинет", "target": "<PERSON><PERSON>", "value": 9}, {"source": "DIANA SKOROKHODOVA", "target": "<PERSON><PERSON>", "value": 8}, {"source": "Michel PODGORSK<PERSON>", "target": "<PERSON><PERSON><PERSON><PERSON>", "value": 8}, {"source": "<PERSON>", "target": "<PERSON><PERSON><PERSON><PERSON>", "value": 9}, {"source": "<PERSON><PERSON>he Metzker Birkeland", "target": "<PERSON><PERSON><PERSON><PERSON>", "value": 14}, {"source": "<PERSON>", "target": "<PERSON><PERSON><PERSON><PERSON>", "value": 15}, {"source": "DIANA SKOROKHODOVA", "target": "<PERSON><PERSON><PERSON><PERSON>", "value": 14}, {"source": "Stefan <PERSON>", "target": "<PERSON><PERSON><PERSON><PERSON>", "value": 9}, {"source": "DIANA SKOROKHODOVA", "target": "<PERSON>", "value": 8}, {"source": "Stefan <PERSON>", "target": "<PERSON>", "value": 9}, {"source": "<PERSON>а<PERSON><PERSON><PERSON>ур", "target": "<PERSON>", "value": 11}, {"source": "<PERSON>", "target": "<PERSON>", "value": 15}, {"source": "Stefan <PERSON>", "target": "<PERSON>", "value": 8}, {"source": "Michel PODGORSK<PERSON>", "target": "<PERSON>", "value": 9}, {"source": "Юлия Габинет", "target": "<PERSON>", "value": 15}, {"source": "<PERSON><PERSON>he Metzker Birkeland", "target": "<PERSON>", "value": 14}, {"source": "<PERSON>", "target": "<PERSON>", "value": 24}, {"source": "Stefan <PERSON>", "target": "<PERSON>", "value": 16}, {"source": "<PERSON>", "target": "<PERSON>", "value": 13}, {"source": "<PERSON>", "target": "<PERSON>", "value": 8}, {"source": "Stefan <PERSON>", "target": "<PERSON>", "value": 15}, {"source": "<PERSON>а<PERSON><PERSON><PERSON>ур", "target": "<PERSON>", "value": 8}, {"source": "<PERSON>", "target": "<PERSON><PERSON>", "value": 16}, {"source": "Stefan <PERSON>", "target": "<PERSON><PERSON>", "value": 9}, {"source": "<PERSON>", "target": "<PERSON><PERSON>", "value": 15}, {"source": "Michel PODGORSK<PERSON>", "target": "<PERSON>", "value": 14}, {"source": "<PERSON>", "target": "<PERSON><PERSON><PERSON>", "value": 8}, {"source": "<PERSON>", "target": "<PERSON><PERSON><PERSON>", "value": 18}, {"source": "Юлия Габинет", "target": "<PERSON><PERSON><PERSON>", "value": 25}, {"source": "<PERSON><PERSON>he Metzker Birkeland", "target": "<PERSON><PERSON><PERSON>", "value": 8}, {"source": "<PERSON>", "target": "<PERSON><PERSON><PERSON>", "value": 16}, {"source": "DIANA SKOROKHODOVA", "target": "<PERSON><PERSON><PERSON>", "value": 9}, {"source": "Stefan <PERSON>", "target": "<PERSON><PERSON><PERSON>", "value": 16}, {"source": "<PERSON>", "target": "<PERSON><PERSON>", "value": 21}, {"source": "DIANA SKOROKHODOVA", "target": "<PERSON><PERSON>", "value": 8}, {"source": "Michel PODGORSK<PERSON>", "target": "<PERSON><PERSON>", "value": 16}, {"source": "Michel PODGORSK<PERSON>", "target": "<PERSON>", "value": 15}, {"source": "<PERSON>", "target": "<PERSON>", "value": 14}, {"source": "<PERSON>", "target": "<PERSON>", "value": 24}, {"source": "Юлия Габинет", "target": "<PERSON>", "value": 9}, {"source": "<PERSON>", "target": "<PERSON>", "value": 14}, {"source": "DIANA SKOROKHODOVA", "target": "<PERSON>", "value": 9}, {"source": "Stefan <PERSON>", "target": "<PERSON>", "value": 9}, {"source": "<PERSON>", "target": "<PERSON>", "value": 23}, {"source": "<PERSON>", "target": "<PERSON>", "value": 15}, {"source": "<PERSON><PERSON>he Metzker Birkeland", "target": "<PERSON>", "value": 23}, {"source": "<PERSON>", "target": "<PERSON>", "value": 16}, {"source": "DIANA SKOROKHODOVA", "target": "<PERSON>", "value": 15}, {"source": "Stefan <PERSON>", "target": "<PERSON>", "value": 10}, {"source": "Michel PODGORSK<PERSON>", "target": "<PERSON>", "value": 9}, {"source": "Юлия Габинет", "target": "<PERSON>", "value": 9}, {"source": "<PERSON><PERSON>he Metzker Birkeland", "target": "<PERSON>", "value": 8}, {"source": "DIANA SKOROKHODOVA", "target": "<PERSON>", "value": 9}, {"source": "Michel PODGORSK<PERSON>", "target": "<PERSON>", "value": 14}, {"source": "<PERSON>", "target": "<PERSON>", "value": 28}, {"source": "<PERSON>", "target": "<PERSON>", "value": 14}, {"source": "Юлия Габинет", "target": "<PERSON>", "value": 9}, {"source": "<PERSON>", "target": "<PERSON>", "value": 16}, {"source": "Stefan <PERSON>", "target": "<PERSON>", "value": 11}, {"source": "Stefan <PERSON>", "target": "<PERSON><PERSON>", "value": 8}, {"source": "Michel PODGORSK<PERSON>", "target": "<PERSON><PERSON>", "value": 20}, {"source": "Юлия Габинет", "target": "<PERSON><PERSON>", "value": 10}, {"source": "<PERSON><PERSON>he Metzker Birkeland", "target": "<PERSON><PERSON>", "value": 15}, {"source": "<PERSON>", "target": "<PERSON><PERSON>", "value": 31}, {"source": "DIANA SKOROKHODOVA", "target": "<PERSON><PERSON>", "value": 9}, {"source": "<PERSON>", "target": "<PERSON><PERSON>", "value": 28}, {"source": "<PERSON>", "target": "<PERSON><PERSON>", "value": 14}, {"source": "<PERSON>", "target": "<PERSON><PERSON><PERSON>", "value": 8}, {"source": "DIANA SKOROKHODOVA", "target": "<PERSON><PERSON><PERSON>", "value": 13}, {"source": "Stefan <PERSON>", "target": "<PERSON><PERSON><PERSON>", "value": 24}, {"source": "Юлия Габинет", "target": "<PERSON><PERSON><PERSON>", "value": 31}, {"source": "Michel PODGORSK<PERSON>", "target": "<PERSON>", "value": 14}, {"source": "Michel PODGORSK<PERSON>", "target": "<PERSON><PERSON><PERSON>", "value": 12}, {"source": "<PERSON>", "target": "<PERSON><PERSON><PERSON>", "value": 14}, {"source": "<PERSON>", "target": "<PERSON><PERSON><PERSON>", "value": 9}, {"source": "Юлия Габинет", "target": "<PERSON><PERSON><PERSON>", "value": 10}, {"source": "<PERSON>", "target": "<PERSON><PERSON><PERSON>", "value": 8}, {"source": "DIANA SKOROKHODOVA", "target": "<PERSON><PERSON><PERSON>", "value": 10}, {"source": "Stefan <PERSON>", "target": "<PERSON><PERSON><PERSON>", "value": 8}, {"source": "Michel PODGORSK<PERSON>", "target": "<PERSON><PERSON><PERSON><PERSON>", "value": 13}, {"source": "<PERSON>", "target": "<PERSON><PERSON><PERSON><PERSON>", "value": 15}, {"source": "<PERSON>", "target": "<PERSON><PERSON><PERSON><PERSON>", "value": 8}, {"source": "Юлия Габинет", "target": "<PERSON><PERSON><PERSON><PERSON>", "value": 15}, {"source": "<PERSON>", "target": "<PERSON><PERSON><PERSON><PERSON>", "value": 14}, {"source": "Stefan <PERSON>", "target": "<PERSON><PERSON><PERSON><PERSON>", "value": 9}, {"source": "Юлия Габинет", "target": "<PERSON><PERSON>", "value": 10}, {"source": "<PERSON><PERSON>he Metzker Birkeland", "target": "<PERSON><PERSON>", "value": 16}, {"source": "DIANA SKOROKHODOVA", "target": "<PERSON><PERSON>", "value": 14}, {"source": "Stefan <PERSON>", "target": "<PERSON><PERSON>", "value": 17}, {"source": "Michel PODGORSK<PERSON>", "target": "<PERSON><PERSON>", "value": 15}, {"source": "<PERSON>", "target": "<PERSON><PERSON>", "value": 10}, {"source": "Michel PODGORSK<PERSON>", "target": "<PERSON><PERSON><PERSON>", "value": 13}, {"source": "<PERSON>", "target": "<PERSON><PERSON><PERSON>", "value": 14}, {"source": "<PERSON>", "target": "<PERSON><PERSON><PERSON>", "value": 16}, {"source": "Stefan <PERSON>", "target": "<PERSON><PERSON><PERSON>", "value": 9}, {"source": "<PERSON>", "target": "Stefan <PERSON>", "value": 9}, {"source": "DIANA SKOROKHODOVA", "target": "Stefan <PERSON>", "value": 16}, {"source": "Michel PODGORSK<PERSON>", "target": "Stefan <PERSON>", "value": 29}, {"source": "DIANA SKOROKHODOVA", "target": "Зоя Калинкина", "value": 22}, {"source": "Stefan <PERSON>", "target": "Зоя Калинкина", "value": 9}, {"source": "Michel PODGORSK<PERSON>", "target": "Зоя Калинкина", "value": 13}, {"source": "<PERSON>", "target": "Зоя Калинкина", "value": 16}, {"source": "Юлия Габинет", "target": "Зоя Калинкина", "value": 17}, {"source": "DIANA SKOROKHODOVA", "target": "Вероника Витальевна Савуляк", "value": 8}, {"source": "Stefan <PERSON>", "target": "Вероника Витальевна Савуляк", "value": 13}, {"source": "<PERSON>", "target": "Вероника Витальевна Савуляк", "value": 9}, {"source": "Юлия Габинет", "target": "Вероника Витальевна Савуляк", "value": 10}, {"source": "<PERSON><PERSON>he Metzker Birkeland", "target": "Вероника Витальевна Савуляк", "value": 13}, {"source": "Юлия Габинет", "target": "Nechypor Boris", "value": 22}, {"source": "<PERSON>", "target": "Nechypor Boris", "value": 8}, {"source": "Stefan <PERSON>", "target": "<PERSON>", "value": 10}, {"source": "<PERSON>", "target": "<PERSON>", "value": 16}, {"source": "<PERSON>", "target": "<PERSON>", "value": 10}, {"source": "Юлия Габинет", "target": "<PERSON>", "value": 10}, {"source": "<PERSON><PERSON>he Metzker Birkeland", "target": "<PERSON>", "value": 8}, {"source": "<PERSON>", "target": "<PERSON>", "value": 12}, {"source": "DIANA SKOROKHODOVA", "target": "<PERSON>", "value": 14}, {"source": "Michel PODGORSK<PERSON>", "target": "DIANA SKOROKHODOVA", "value": 9}, {"source": "DIANA SKOROKHODOVA", "target": "Юлия Габинет", "value": 14}, {"source": "Stefan <PERSON>", "target": "Юлия Габинет", "value": 10}, {"source": "<PERSON>а<PERSON><PERSON><PERSON>ур", "target": "Юлия Габинет", "value": 10}, {"source": "<PERSON>", "target": "<PERSON><PERSON>lö<PERSON>", "value": 8}, {"source": "DIANA SKOROKHODOVA", "target": "<PERSON><PERSON>lö<PERSON>", "value": 14}, {"source": "Stefan <PERSON>", "target": "<PERSON><PERSON>lö<PERSON>", "value": 14}, {"source": "Michel PODGORSK<PERSON>", "target": "<PERSON><PERSON>lö<PERSON>", "value": 13}, {"source": "<PERSON>", "target": "<PERSON><PERSON>lö<PERSON>", "value": 13}, {"source": "Юлия Габинет", "target": "<PERSON><PERSON>lö<PERSON>", "value": 10}, {"source": "<PERSON><PERSON>he Metzker Birkeland", "target": "<PERSON><PERSON>lö<PERSON>", "value": 9}, {"source": "<PERSON>", "target": "<PERSON><PERSON>lö<PERSON>", "value": 17}, {"source": "Michel PODGORSK<PERSON>", "target": "<PERSON>", "value": 21}, {"source": "<PERSON>", "target": "<PERSON>", "value": 15}, {"source": "<PERSON>", "target": "<PERSON>", "value": 21}, {"source": "<PERSON>", "target": "<PERSON>", "value": 15}, {"source": "DIANA SKOROKHODOVA", "target": "<PERSON>", "value": 20}, {"source": "Stefan <PERSON>", "target": "<PERSON>", "value": 15}, {"source": "Юлия Габинет", "target": "<PERSON><PERSON>", "value": 9}, {"source": "DIANA SKOROKHODOVA", "target": "<PERSON><PERSON>", "value": 14}, {"source": "Stefan <PERSON>", "target": "<PERSON><PERSON>", "value": 8}, {"source": "Michel PODGORSK<PERSON>", "target": "<PERSON><PERSON>", "value": 8}, {"source": "<PERSON>", "target": "<PERSON><PERSON>", "value": 9}, {"source": "<PERSON>", "target": "<PERSON><PERSON>", "value": 20}, {"source": "<PERSON><PERSON>he Metzker Birkeland", "target": "<PERSON><PERSON>", "value": 8}, {"source": "DIANA SKOROKHODOVA", "target": "<PERSON><PERSON>", "value": 17}, {"source": "Stefan <PERSON>", "target": "<PERSON><PERSON>", "value": 9}, {"source": "Michel PODGORSK<PERSON>", "target": "<PERSON><PERSON>", "value": 14}, {"source": "<PERSON>", "target": "<PERSON><PERSON>", "value": 9}, {"source": "Юлия Габинет", "target": "<PERSON><PERSON>", "value": 9}, {"source": "DIANA SKOROKHODOVA", "target": "<PERSON><PERSON>", "value": 17}, {"source": "<PERSON>", "target": "<PERSON><PERSON><PERSON>-<PERSON><PERSON> (Szabó)", "value": 20}, {"source": "<PERSON>", "target": "<PERSON><PERSON><PERSON>-<PERSON><PERSON> (Szabó)", "value": 14}, {"source": "Юлия Габинет", "target": "<PERSON><PERSON><PERSON>-<PERSON><PERSON> (Szabó)", "value": 8}, {"source": "DIANA SKOROKHODOVA", "target": "<PERSON><PERSON><PERSON>-<PERSON><PERSON> (Szabó)", "value": 13}, {"source": "Юлия Габинет", "target": "<PERSON><PERSON><PERSON>", "value": 23}, {"source": "<PERSON><PERSON>he Metzker Birkeland", "target": "<PERSON><PERSON><PERSON>", "value": 14}, {"source": "<PERSON>", "target": "<PERSON><PERSON><PERSON>", "value": 13}, {"source": "DIANA SKOROKHODOVA", "target": "<PERSON><PERSON><PERSON>", "value": 9}, {"source": "Stefan <PERSON>", "target": "<PERSON><PERSON><PERSON>", "value": 15}, {"source": "Michel PODGORSK<PERSON>", "target": "<PERSON><PERSON><PERSON>", "value": 9}, {"source": "<PERSON>", "target": "<PERSON><PERSON><PERSON>", "value": 9}, {"source": "Юлия Габинет", "target": "<PERSON> (<PERSON> <PERSON><PERSON>)", "value": 16}, {"source": "<PERSON>", "target": "<PERSON> (<PERSON> <PERSON><PERSON>)", "value": 13}, {"source": "DIANA SKOROKHODOVA", "target": "<PERSON> (<PERSON> <PERSON><PERSON>)", "value": 15}, {"source": "Stefan <PERSON>", "target": "<PERSON> (<PERSON> <PERSON><PERSON>)", "value": 14}, {"source": "<PERSON>", "target": "<PERSON> (<PERSON> <PERSON><PERSON>)", "value": 10}, {"source": "<PERSON><PERSON>he Metzker Birkeland", "target": "<PERSON><PERSON><PERSON>", "value": 13}, {"source": "<PERSON>", "target": "<PERSON><PERSON><PERSON>", "value": 14}, {"source": "DIANA SKOROKHODOVA", "target": "<PERSON><PERSON><PERSON>", "value": 13}, {"source": "Stefan <PERSON>", "target": "<PERSON><PERSON><PERSON>", "value": 10}, {"source": "<PERSON>", "target": "<PERSON><PERSON><PERSON>", "value": 22}, {"source": "<PERSON>", "target": "<PERSON><PERSON><PERSON>", "value": 8}, {"source": "DIANA SKOROKHODOVA", "target": "<PERSON><PERSON><PERSON>", "value": 8}, {"source": "<PERSON>", "target": "<PERSON>", "value": 22}, {"source": "DIANA SKOROKHODOVA", "target": "<PERSON>", "value": 22}, {"source": "Stefan <PERSON>", "target": "<PERSON>", "value": 8}, {"source": "Michel PODGORSK<PERSON>", "target": "<PERSON>", "value": 8}, {"source": "<PERSON>", "target": "<PERSON>", "value": 14}, {"source": "<PERSON>", "target": "<PERSON>", "value": 17}, {"source": "Michel PODGORSK<PERSON>", "target": "<PERSON>а<PERSON><PERSON><PERSON>ур", "value": 21}, {"source": "<PERSON><PERSON>he Metzker Birkeland", "target": "<PERSON>а<PERSON><PERSON><PERSON>ур", "value": 9}, {"source": "<PERSON>", "target": "<PERSON>а<PERSON><PERSON><PERSON>ур", "value": 8}, {"source": "DIANA SKOROKHODOVA", "target": "<PERSON>а<PERSON><PERSON><PERSON>ур", "value": 11}, {"source": "Stefan <PERSON>", "target": "<PERSON>а<PERSON><PERSON><PERSON>ур", "value": 15}, {"source": "DIANA SKOROKHODOVA", "target": "<PERSON><PERSON><PERSON><PERSON>", "value": 10}, {"source": "<PERSON>", "target": "<PERSON><PERSON><PERSON><PERSON>", "value": 13}, {"source": "<PERSON>", "target": "<PERSON><PERSON>he Metzker Birkeland", "value": 15}, {"source": "Stefan <PERSON>", "target": "<PERSON><PERSON>he Metzker Birkeland", "value": 9}, {"source": "<PERSON>", "target": "<PERSON>", "value": 8}, {"source": "DIANA SKOROKHODOVA", "target": "<PERSON>", "value": 8}, {"source": "Stefan <PERSON>", "target": "<PERSON>", "value": 10}, {"source": "<PERSON>", "target": "<PERSON>", "value": 9}, {"source": "Юлия Габинет", "target": "<PERSON>", "value": 10}, {"source": "<PERSON><PERSON>he Metzker Birkeland", "target": "<PERSON>", "value": 3520}, {"source": "DIANA SKOROKHODOVA", "target": "<PERSON>", "value": 16}, {"source": "<PERSON>", "target": "<PERSON>", "value": 13}, {"source": "Юлия Габинет", "target": "<PERSON>", "value": 13}, {"source": "<PERSON>", "target": "<PERSON>", "value": 9}, {"source": "Michel PODGORSK<PERSON>", "target": "Ека<PERSON><PERSON><PERSON><PERSON><PERSON>абуния", "value": 15}, {"source": "DIANA SKOROKHODOVA", "target": "<PERSON>", "value": 14}, {"source": "Stefan <PERSON>", "target": "<PERSON>", "value": 14}, {"source": "Michel PODGORSK<PERSON>", "target": "<PERSON>", "value": 15}, {"source": "<PERSON>", "target": "<PERSON>", "value": 19}, {"source": "Юлия Габинет", "target": "<PERSON>", "value": 9}, {"source": "<PERSON>", "target": "<PERSON><PERSON><PERSON>", "value": 35}, {"source": "Юлия Габинет", "target": "<PERSON><PERSON><PERSON>", "value": 16}, {"source": "<PERSON><PERSON>he Metzker Birkeland", "target": "<PERSON><PERSON><PERSON>", "value": 9}, {"source": "<PERSON>", "target": "<PERSON><PERSON><PERSON>", "value": 16}, {"source": "DIANA SKOROKHODOVA", "target": "<PERSON><PERSON><PERSON>", "value": 22}, {"source": "Michel PODGORSK<PERSON>", "target": "<PERSON><PERSON><PERSON>", "value": 8}, {"source": "<PERSON>", "target": "<PERSON><PERSON><PERSON>", "value": 17}, {"source": "DIANA SKOROKHODOVA", "target": "<PERSON>", "value": 8}, {"source": "Stefan <PERSON>", "target": "<PERSON>", "value": 8}, {"source": "<PERSON>", "target": "<PERSON>", "value": 8}, {"source": "Юлия Габинет", "target": "<PERSON>", "value": 19}, {"source": "Stefan <PERSON>", "target": "<PERSON><PERSON>", "value": 17}, {"source": "Michel PODGORSK<PERSON>", "target": "<PERSON><PERSON>", "value": 15}, {"source": "Юлия Габинет", "target": "<PERSON><PERSON>", "value": 28}, {"source": "<PERSON><PERSON>he Metzker Birkeland", "target": "<PERSON><PERSON>", "value": 21}, {"source": "Юлия Габинет", "target": "Дарья Бобровская", "value": 15}, {"source": "<PERSON><PERSON>he Metzker Birkeland", "target": "Дарья Бобровская", "value": 14}, {"source": "DIANA SKOROKHODOVA", "target": "Дарья Бобровская", "value": 13}, {"source": "Stefan <PERSON>", "target": "Дарья Бобровская", "value": 9}, {"source": "Michel PODGORSK<PERSON>", "target": "Дарья Бобровская", "value": 25}, {"source": "<PERSON>", "target": "Дарья Бобровская", "value": 16}, {"source": "Stefan <PERSON>", "target": "<PERSON><PERSON><PERSON>", "value": 22}, {"source": "Michel PODGORSK<PERSON>", "target": "<PERSON><PERSON><PERSON>", "value": 8}, {"source": "<PERSON>", "target": "<PERSON><PERSON><PERSON>", "value": 15}, {"source": "<PERSON>", "target": "<PERSON>", "value": 14}, {"source": "<PERSON>", "target": "<PERSON>", "value": 8}, {"source": "Юлия Габинет", "target": "<PERSON>", "value": 13}, {"source": "<PERSON><PERSON>he Metzker Birkeland", "target": "<PERSON>", "value": 8}, {"source": "<PERSON>", "target": "<PERSON>", "value": 16}, {"source": "DIANA SKOROKHODOVA", "target": "<PERSON>", "value": 8}, {"source": "Stefan <PERSON>", "target": "<PERSON>", "value": 12}, {"source": "Michel PODGORSK<PERSON>", "target": "<PERSON>", "value": 17}, {"source": "Michel PODGORSK<PERSON>", "target": "<PERSON>і<PERSON><PERSON><PERSON><PERSON><PERSON> Дикий", "value": 19}, {"source": "<PERSON>", "target": "<PERSON>і<PERSON><PERSON><PERSON><PERSON><PERSON> Дикий", "value": 30}, {"source": "<PERSON>", "target": "<PERSON>і<PERSON><PERSON><PERSON><PERSON><PERSON> Дикий", "value": 16}, {"source": "Юлия Габинет", "target": "<PERSON>і<PERSON><PERSON><PERSON><PERSON><PERSON> Дикий", "value": 17}, {"source": "DIANA SKOROKHODOVA", "target": "<PERSON>і<PERSON><PERSON><PERSON><PERSON><PERSON> Дикий", "value": 18}, {"source": "Stefan <PERSON>", "target": "<PERSON>і<PERSON><PERSON><PERSON><PERSON><PERSON> Дикий", "value": 8}, {"source": "Michel PODGORSK<PERSON>", "target": "<PERSON><PERSON>", "value": 14}, {"source": "<PERSON>", "target": "<PERSON><PERSON>", "value": 13}, {"source": "<PERSON><PERSON>he Metzker Birkeland", "target": "<PERSON><PERSON>", "value": 8}, {"source": "<PERSON>", "target": "<PERSON><PERSON>", "value": 15}, {"source": "Stefan <PERSON>", "target": "<PERSON><PERSON>", "value": 21}, {"source": "<PERSON>", "target": "<PERSON>", "value": 14}, {"source": "Stefan <PERSON>", "target": "<PERSON>", "value": 16}, {"source": "Michel PODGORSK<PERSON>", "target": "<PERSON>", "value": 15}, {"source": "<PERSON>", "target": "<PERSON>", "value": 9}, {"source": "<PERSON><PERSON>he Metzker Birkeland", "target": "<PERSON>", "value": 20}, {"source": "Stefan <PERSON>", "target": "<PERSON>", "value": 8}, {"source": "Michel PODGORSK<PERSON>", "target": "<PERSON>", "value": 15}, {"source": "<PERSON>", "target": "<PERSON>", "value": 9}, {"source": "Stefan <PERSON>", "target": "<PERSON>", "value": 15}, {"source": "<PERSON>", "target": "<PERSON>", "value": 16}, {"source": "<PERSON>", "target": "<PERSON>", "value": 20}, {"source": "Юлия Габинет", "target": "<PERSON>", "value": 9}, {"source": "<PERSON><PERSON>", "target": "<PERSON>", "value": 21}, {"source": "DIANA SKOROKHODOVA", "target": "<PERSON>", "value": 15}, {"source": "DIANA SKOROKHODOVA", "target": "<PERSON><PERSON> (Pluta)", "value": 8}, {"source": "Stefan <PERSON>", "target": "<PERSON><PERSON> (Pluta)", "value": 8}, {"source": "<PERSON>", "target": "<PERSON><PERSON> (Pluta)", "value": 12}, {"source": "<PERSON>", "target": "<PERSON><PERSON> (Pluta)", "value": 10}, {"source": "Юлия Габинет", "target": "<PERSON><PERSON> (Pluta)", "value": 22}, {"source": "<PERSON><PERSON>he Metzker Birkeland", "target": "<PERSON><PERSON> (Pluta)", "value": 8}, {"source": "<PERSON>", "target": "<PERSON><PERSON> (Pluta)", "value": 16}, {"source": "<PERSON>", "target": "<PERSON><PERSON>", "value": 8}, {"source": "<PERSON>", "target": "<PERSON><PERSON>", "value": 8}, {"source": "Юлия Габинет", "target": "<PERSON><PERSON>", "value": 25}, {"source": "DIANA SKOROKHODOVA", "target": "<PERSON><PERSON>", "value": 8}, {"source": "Stefan <PERSON>", "target": "<PERSON><PERSON>", "value": 14}, {"source": "Stefan <PERSON>", "target": "<PERSON><PERSON>", "value": 8}, {"source": "Michel PODGORSK<PERSON>", "target": "<PERSON><PERSON>", "value": 15}, {"source": "<PERSON>", "target": "<PERSON><PERSON>", "value": 18}, {"source": "Юлия Габинет", "target": "<PERSON><PERSON>", "value": 14}, {"source": "<PERSON><PERSON>he Metzker Birkeland", "target": "<PERSON><PERSON>", "value": 19}, {"source": "<PERSON><PERSON>he Metzker Birkeland", "target": "<PERSON>", "value": 15}, {"source": "<PERSON>", "target": "<PERSON>", "value": 16}, {"source": "Stefan <PERSON>", "target": "<PERSON>", "value": 8}, {"source": "<PERSON>", "target": "<PERSON>", "value": 14}, {"source": "<PERSON>", "target": "<PERSON>", "value": 13}, {"source": "Stefan <PERSON>", "target": "<PERSON>", "value": 8}, {"source": "Michel PODGORSK<PERSON>", "target": "<PERSON>", "value": 14}, {"source": "<PERSON>", "target": "<PERSON>", "value": 21}, {"source": "<PERSON>", "target": "<PERSON>", "value": 14}, {"source": "Юлия Габинет", "target": "<PERSON>", "value": 8}, {"source": "<PERSON>", "target": "<PERSON>", "value": 8}, {"source": "DIANA SKOROKHODOVA", "target": "<PERSON>", "value": 8}, {"source": "Michel PODGORSK<PERSON>", "target": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": 19}, {"source": "<PERSON>", "target": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": 13}, {"source": "Юлия Габинет", "target": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": 9}, {"source": "DIANA SKOROKHODOVA", "target": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": 13}, {"source": "DIANA SKOROKHODOVA", "target": "<PERSON>", "value": 9}, {"source": "Stefan <PERSON>", "target": "<PERSON>", "value": 8}, {"source": "Michel PODGORSK<PERSON>", "target": "<PERSON>", "value": 8}, {"source": "Юлия Габинет", "target": "<PERSON>", "value": 8}, {"source": "<PERSON><PERSON>he Metzker Birkeland", "target": "<PERSON>", "value": 13}, {"source": "Юлия Габинет", "target": "<PERSON><PERSON><PERSON>", "value": 15}, {"source": "<PERSON><PERSON>he Metzker Birkeland", "target": "<PERSON><PERSON><PERSON>", "value": 13}, {"source": "DIANA SKOROKHODOVA", "target": "<PERSON><PERSON><PERSON>", "value": 8}, {"source": "Stefan <PERSON>", "target": "<PERSON><PERSON><PERSON>", "value": 14}, {"source": "<PERSON>", "target": "<PERSON><PERSON><PERSON>", "value": 21}, {"source": "Stefan <PERSON>", "target": "WALDEMAR MARCINKOWSKI", "value": 10}, {"source": "<PERSON>", "target": "WALDEMAR MARCINKOWSKI", "value": 17}, {"source": "<PERSON>", "target": "WALDEMAR MARCINKOWSKI", "value": 25}, {"source": "Юлия Габинет", "target": "WALDEMAR MARCINKOWSKI", "value": 9}, {"source": "<PERSON>", "target": "WALDEMAR MARCINKOWSKI", "value": 19}, {"source": "DIANA SKOROKHODOVA", "target": "WALDEMAR MARCINKOWSKI", "value": 9}, {"source": "Stefan <PERSON>", "target": "<PERSON><PERSON>", "value": 10}, {"source": "Stefan <PERSON>", "target": "<PERSON>", "value": 8}, {"source": "Michel PODGORSK<PERSON>", "target": "<PERSON>", "value": 9}, {"source": "<PERSON>", "target": "<PERSON>", "value": 8}, {"source": "Юлия Габинет", "target": "<PERSON>", "value": 8}, {"source": "<PERSON>", "target": "<PERSON><PERSON><PERSON><PERSON>", "value": 12}, {"source": "<PERSON>", "target": "<PERSON><PERSON><PERSON><PERSON>", "value": 8}, {"source": "Юлия Габинет", "target": "<PERSON><PERSON><PERSON><PERSON>", "value": 21}, {"source": "Stefan <PERSON>", "target": "<PERSON><PERSON><PERSON><PERSON>", "value": 8}, {"source": "DIANA SKOROKHODOVA", "target": "<PERSON>рина <PERSON>ымова", "value": 14}, {"source": "Stefan <PERSON>", "target": "<PERSON>рина <PERSON>ымова", "value": 8}, {"source": "<PERSON>", "target": "<PERSON>рина <PERSON>ымова", "value": 8}, {"source": "<PERSON><PERSON>he Metzker Birkeland", "target": "<PERSON>рина <PERSON>ымова", "value": 13}, {"source": "<PERSON>", "target": "<PERSON>рина <PERSON>ымова", "value": 14}, {"source": "Stefan <PERSON>", "target": "<PERSON>", "value": 8}, {"source": "Юлия Габинет", "target": "<PERSON>", "value": 8}, {"source": "Michel PODGORSK<PERSON>", "target": "<PERSON>", "value": 28}, {"source": "<PERSON>", "target": "<PERSON>", "value": 13}, {"source": "<PERSON><PERSON>he Metzker Birkeland", "target": "<PERSON>", "value": 20}, {"source": "DIANA SKOROKHODOVA", "target": "<PERSON>", "value": 14}, {"source": "Stefan <PERSON>", "target": "<PERSON>", "value": 15}, {"source": "<PERSON>", "target": "<PERSON>", "value": 10}, {"source": "Юлия Габинет", "target": "<PERSON>", "value": 17}, {"source": "Stefan <PERSON>", "target": "<PERSON>", "value": 30}, {"source": "Michel PODGORSK<PERSON>", "target": "<PERSON>", "value": 30}, {"source": "<PERSON>", "target": "<PERSON><PERSON>", "value": 9}, {"source": "DIANA SKOROKHODOVA", "target": "<PERSON><PERSON>", "value": 17}, {"source": "Stefan <PERSON>", "target": "<PERSON><PERSON>", "value": 15}, {"source": "<PERSON>", "target": "<PERSON><PERSON>", "value": 32}, {"source": "<PERSON>", "target": "<PERSON><PERSON>", "value": 15}, {"source": "Юлия Габинет", "target": "<PERSON><PERSON>", "value": 22}, {"source": "<PERSON><PERSON>he Metzker Birkeland", "target": "<PERSON><PERSON>", "value": 10}, {"source": "Michel PODGORSK<PERSON>", "target": "<Privat> Szczepaniak", "value": 8}, {"source": "<PERSON>", "target": "<Privat> Szczepaniak", "value": 17}, {"source": "Юлия Габинет", "target": "<Privat> Szczepaniak", "value": 11}, {"source": "Stefan <PERSON>", "target": "<Privat> Szczepaniak", "value": 39}, {"source": "Stefan <PERSON>", "target": "<Privat> Szczepaniak", "value": 14}, {"source": "Michel PODGORSK<PERSON>", "target": "<Privat> Szczepaniak", "value": 13}, {"source": "<PERSON>", "target": "<PERSON>", "value": 9}, {"source": "Юлия Габинет", "target": "<PERSON>", "value": 9}, {"source": "<PERSON>", "target": "<PERSON>", "value": 13}, {"source": "DIANA SKOROKHODOVA", "target": "<PERSON>", "value": 17}, {"source": "Stefan <PERSON>", "target": "<PERSON>", "value": 16}, {"source": "Michel PODGORSK<PERSON>", "target": "<PERSON><PERSON>", "value": 8}, {"source": "Юлия Габинет", "target": "<PERSON><PERSON>", "value": 22}, {"source": "<PERSON>", "target": "<PERSON><PERSON><PERSON>", "value": 15}, {"source": "<PERSON>", "target": "<PERSON><PERSON><PERSON>", "value": 30}, {"source": "DIANA SKOROKHODOVA", "target": "<PERSON><PERSON><PERSON>", "value": 16}, {"source": "Stefan <PERSON>", "target": "<PERSON><PERSON><PERSON>", "value": 16}, {"source": "Michel PODGORSK<PERSON>", "target": "<PERSON><PERSON><PERSON>", "value": 8}, {"source": "<PERSON>", "target": "<PERSON><PERSON><PERSON>", "value": 18}, {"source": "Юлия Габинет", "target": "<PERSON><PERSON><PERSON>", "value": 9}, {"source": "<PERSON><PERSON>he Metzker Birkeland", "target": "<PERSON><PERSON><PERSON>", "value": 8}, {"source": "Michel PODGORSK<PERSON>", "target": "<PERSON>", "value": 8}, {"source": "<PERSON>", "target": "<PERSON>", "value": 8}, {"source": "Юлия Габинет", "target": "<PERSON>", "value": 16}, {"source": "<PERSON>", "target": "<PERSON>", "value": 15}, {"source": "DIANA SKOROKHODOVA", "target": "<PERSON>", "value": 9}, {"source": "Stefan <PERSON>", "target": "<PERSON>", "value": 23}, {"source": "<PERSON>", "target": "<Privat> <PERSON><PERSON> (z d. <PERSON>)", "value": 13}, {"source": "DIANA SKOROKHODOVA", "target": "<Privat> <PERSON><PERSON> (z d. <PERSON>)", "value": 13}, {"source": "Michel PODGORSK<PERSON>", "target": "<Privat> <PERSON><PERSON> (z d. <PERSON>)", "value": 13}, {"source": "Michel PODGORSK<PERSON>", "target": "<PERSON>", "value": 10}, {"source": "Юлия Габинет", "target": "<PERSON>", "value": 10}, {"source": "<PERSON><PERSON>he Metzker Birkeland", "target": "<PERSON>", "value": 15}, {"source": "DIANA SKOROKHODOVA", "target": "<PERSON>", "value": 9}, {"source": "Stefan <PERSON>", "target": "<PERSON>", "value": 15}, {"source": "<PERSON>", "target": "<PERSON>", "value": 14}, {"source": "<PERSON>", "target": "<PERSON>", "value": 16}, {"source": "<PERSON>", "target": "<PERSON>", "value": 9}, {"source": "Юлия Габинет", "target": "<PERSON> (geb. <PERSON>)", "value": 8}, {"source": "Stefan <PERSON>", "target": "<PERSON> (geb. <PERSON>)", "value": 9}, {"source": "Michel PODGORSK<PERSON>", "target": "<PERSON> (geb. <PERSON>)", "value": 22}, {"source": "DIANA SKOROKHODOVA", "target": "<PERSON><PERSON>", "value": 13}, {"source": "<PERSON>", "target": "<PERSON><PERSON>", "value": 8}, {"source": "<PERSON>", "target": "<PERSON><PERSON>", "value": 8}, {"source": "Юлия Габинет", "target": "<PERSON><PERSON>", "value": 14}, {"source": "<PERSON><PERSON>he Metzker Birkeland", "target": "<PERSON><PERSON>", "value": 19}, {"source": "Юлия Габинет", "target": "<PERSON><PERSON><PERSON><PERSON>", "value": 8}, {"source": "Stefan <PERSON>", "target": "<PERSON><PERSON><PERSON><PERSON>", "value": 10}, {"source": "Michel PODGORSK<PERSON>", "target": "<PERSON><PERSON><PERSON><PERSON>", "value": 15}, {"source": "Юлия Габинет", "target": "<PERSON>", "value": 12}, {"source": "<PERSON>", "target": "<PERSON>", "value": 14}, {"source": "DIANA SKOROKHODOVA", "target": "<PERSON>", "value": 12}, {"source": "DIANA SKOROKHODOVA", "target": "<PERSON><PERSON><PERSON><PERSON>", "value": 16}, {"source": "Michel PODGORSK<PERSON>", "target": "<PERSON><PERSON><PERSON><PERSON>", "value": 13}, {"source": "Юлия Габинет", "target": "<PERSON><PERSON><PERSON><PERSON>", "value": 24}, {"source": "<PERSON>а<PERSON><PERSON><PERSON>ур", "target": "<PERSON><PERSON>", "value": 13}, {"source": "DIANA SKOROKHODOVA", "target": "<PERSON><PERSON><PERSON>", "value": 13}, {"source": "Stefan <PERSON>", "target": "Rikke Fjordbak Bentsen", "value": 9}, {"source": "Michel PODGORSK<PERSON>", "target": "Rikke Fjordbak Bentsen", "value": 14}, {"source": "<PERSON>", "target": "Rikke Fjordbak Bentsen", "value": 9}, {"source": "Michel PODGORSK<PERSON>", "target": "<PERSON>", "value": 8}, {"source": "<PERSON>", "target": "<PERSON>", "value": 19}, {"source": "Юлия Габинет", "target": "<PERSON>", "value": 15}, {"source": "<PERSON>", "target": "<PERSON>", "value": 15}, {"source": "Юлия Габинет", "target": "<PERSON>", "value": 17}, {"source": "<PERSON><PERSON>he Metzker Birkeland", "target": "<PERSON>", "value": 20}, {"source": "DIANA SKOROKHODOVA", "target": "<PERSON>", "value": 22}, {"source": "Stefan <PERSON>", "target": "<PERSON>", "value": 13}, {"source": "<PERSON>", "target": "<PERSON>", "value": 8}, {"source": "<PERSON>", "target": "<PERSON>", "value": 10}, {"source": "<PERSON>", "target": "<PERSON><PERSON><PERSON> Winter-Sza<PERSON>", "value": 13}, {"source": "<PERSON>", "target": "<PERSON><PERSON><PERSON> Winter-Sza<PERSON>", "value": 21}, {"source": "Stefan <PERSON>", "target": "<PERSON><PERSON><PERSON> Winter-Sza<PERSON>", "value": 9}, {"source": "Michel PODGORSK<PERSON>", "target": "<PERSON><PERSON><PERSON> Winter-Sza<PERSON>", "value": 14}, {"source": "Юлия Габинет", "target": "<PERSON><PERSON><PERSON> Winter-Sza<PERSON>", "value": 8}, {"source": "<PERSON><PERSON>he Metzker Birkeland", "target": "<PERSON><PERSON><PERSON> Winter-Sza<PERSON>", "value": 8}, {"source": "<PERSON><PERSON>", "target": "<PERSON><PERSON><PERSON> Winter-Sza<PERSON>", "value": 21}, {"source": "<PERSON>", "target": "<PERSON><PERSON><PERSON> Winter-Sza<PERSON>", "value": 23}, {"source": "DIANA SKOROKHODOVA", "target": "<PERSON><PERSON><PERSON> Winter-Sza<PERSON>", "value": 10}]}